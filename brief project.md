# PTQ Al Ihsan - Islamic Boarding School Management System

## Product Overview

PTQ Al Ihsan is a comprehensive web-based management system for an Islamic boarding school (pondok pesantren) that focuses on Quranic memorization and Islamic studies. The system provides a complete solution for school administration, student achievement tracking, parent communication, and educational content management. Built with modern web technologies, it offers a responsive and user-friendly interface for administrators, teachers, parents, and prospective students.

## Technical Stack

- **Frontend Framework**: Next.js 14.0.0
- **Language**: TypeScript
- **UI Components**:
  - Shadcn UI components
  - Tailwind CSS for styling
  - Lucide React for icons
- **State Management**: React Context API and React Hooks
- **Form Handling**: React Hook Form
- **Data Validation**: Zod
- **Backend**:
  - Next.js App Router with Server Components
  - Server Actions for form submissions
  - Supabase for database and authentication
- **Database**: PostgreSQL (via Supabase)
- **Authentication**: Supabase Auth
- **File Storage**: Supabase Storage
- **Deployment**: Vercel

## Core Features

### 1. Public Website

- **Home Page**: Showcasing the school's vision, mission, and core values
- **About Us**: School history, facilities, and teaching staff
- **Programs**: Detailed information about academic and extracurricular programs
- **PPDB Online**: Online student registration system
- **Blog**: School news, articles, and announcements
- **Contact**: Contact information and inquiry form

### 2. Student Achievement Tracking

- **Timeline Input**: Teacher interface for recording student progress
- **Achievement Dashboard**: Visual representation of student achievements
- **Progress Reports**: Detailed reports on Quran memorization, hadith studies, and academic subjects
- **Attendance Tracking**: Record and monitor student attendance
- **Behavior Assessment**: Track and evaluate student behavior and character development

### 3. Parent Portal

- **Student Progress Viewer**: Real-time access to child's academic progress
- **Timeline View**: Chronological view of student achievements
- **WhatsApp Verification**: Quick access using WhatsApp number verification
- **Communication Channel**: Direct messaging with teachers
- **Notification System**: Updates on important events and student achievements

### 4. Administrative Dashboard

- **Student Management**: Add, edit, and manage student records
- **Teacher Management**: Manage teaching staff information and assignments
- **Class Management**: Create and manage classes and schedules
- **PPDB Management**: Process and approve new student applications
- **Report Generation**: Generate various reports for administrative purposes

## User Interface

### Design System

- **Color Scheme**: Emerald green as primary color, representing Islamic identity
- **Typography**: Clean, readable fonts optimized for both Arabic and Latin scripts
- **Responsive Design**: Mobile-first approach ensuring usability on all devices
- **Accessibility**: WCAG-compliant components and color contrast
- **Multilingual Support**: Bilingual interface (Indonesian and Arabic)

### Key UI Components

- **Navigation**: Responsive navigation menu with role-based access
- **Dashboard Cards**: Visual representation of key metrics and information
- **Data Tables**: Interactive tables for displaying student and class data
- **Forms**: User-friendly forms with validation for data input
- **Timeline Component**: Visual representation of chronological achievements
- **Accordion**: Collapsible content sections for organizing information
- **Dialog/Modal**: For confirmations and focused tasks
- **Alerts**: For system notifications and feedback

## Technical Requirements

### Frontend

- **Server Components**: Leverage Next.js 14 server components for improved performance
- **Client Components**: Interactive UI elements with client-side JavaScript
- **TypeScript**: Type-safe development to reduce runtime errors
- **Responsive Design**: Mobile-first approach using Tailwind CSS
- **Accessibility**: Keyboard navigation, screen reader support, and proper ARIA attributes
- **SEO Optimization**: Meta tags, structured data, and optimized content

### Backend Integration

- **Supabase Integration**:
  - PostgreSQL database with proper schema design
  - Row-level security policies for data protection
  - Real-time subscriptions for live updates
  - Storage buckets for file management
  - Authentication and authorization system
  - Serverless functions for complex operations

### Performance

- **Image Optimization**: Next.js Image component for optimized image loading
- **Code Splitting**: Automatic code splitting for faster page loads
- **Incremental Static Regeneration**: For frequently updated pages
- **API Route Optimization**: Efficient API routes with proper caching
- **Database Query Optimization**: Indexed queries and efficient joins

### Security

- **Authentication**: Secure user authentication with Supabase Auth
- **Authorization**: Role-based access control (RBAC)
- **Data Encryption**: Encrypted sensitive data
- **Input Validation**: Server-side validation of all user inputs
- **CSRF Protection**: Protection against cross-site request forgery
- **Rate Limiting**: Prevention of brute force attacks

## Data Model (Supabase Tables)

### 1. Users

- id (Primary Key)
- email
- phone
- password_hash
- role (admin, teacher, parent)
- created_at
- updated_at

### 2. Students

- id (Primary Key)
- student_id (unique identifier)
- name
- gender
- birth_date
- address
- photo_url
- batch
- status
- created_at
- updated_at

### 3. Parents

- id (Primary Key)
- user_id (Foreign Key to Users)
- name
- phone
- email
- address
- created_at
- updated_at

### 4. Student_Parent

- id (Primary Key)
- student_id (Foreign Key to Students)
- parent_id (Foreign Key to Parents)
- relationship
- is_primary
- created_at

### 5. Teachers

- id (Primary Key)
- user_id (Foreign Key to Users)
- teacher_id (unique identifier)
- name
- specialization
- photo_url
- join_date
- status
- created_at
- updated_at

### 6. Classes

- id (Primary Key)
- class_id (unique identifier)
- name
- level
- academic_year
- homeroom_teacher_id (Foreign Key to Teachers)
- created_at
- updated_at

### 7. Class_Students

- id (Primary Key)
- class_id (Foreign Key to Classes)
- student_id (Foreign Key to Students)
- created_at

### 8. Subjects

- id (Primary Key)
- name
- category
- description
- created_at
- updated_at

### 9. Timeline_Entries

- id (Primary Key)
- student_id (Foreign Key to Students)
- teacher_id (Foreign Key to Teachers)
- month
- year
- created_at
- updated_at

### 10. Timeline_Details

- id (Primary Key)
- timeline_id (Foreign Key to Timeline_Entries)
- subject_id (Foreign Key to Subjects)
- value
- grade
- notes
- created_at
- updated_at

### 11. Attendance

- id (Primary Key)
- student_id (Foreign Key to Students)
- class_id (Foreign Key to Classes)
- attendance_date
- status
- notes
- created_at
- updated_at

### 12. PPDB_Applications

- id (Primary Key)
- application_id (unique identifier)
- student_name
- parent_name
- email
- phone
- address
- birth_date
- previous_school
- status
- documents_url
- submitted_at
- processed_at
- processed_by (Foreign Key to Users)
- created_at
- updated_at

### 13. Blog_Posts

- id (Primary Key)
- author_id (Foreign Key to Users)
- title
- slug
- content
- featured_image
- status
- published_at
- created_at
- updated_at

## Key Components and Pages

### 1. Public Website Components

- **Header/Navigation**: Site-wide navigation with responsive design
- **Hero Section**: Featured content and call-to-action
- **Value Cards**: Highlighting school's core values
- **Program Cards**: Showcasing educational programs
- **Testimonial Cards**: Parent testimonials
- **Footer**: Site-wide footer with links and contact information
- **Parent Access Dialog**: WhatsApp verification for parent access

### 2. Dashboard Components

- **Sidebar**: Navigation for dashboard sections
- **Student List**: Searchable, filterable list of students
- **Student Card**: Summary of student information
- **Achievement Card**: Visual representation of student achievements
- **Timeline Input Form**: Form for teachers to input student progress
- **Class Selector**: Component for selecting classes
- **Period Selector**: Component for selecting time periods
- **Bulk Input Interface**: Interface for inputting data for multiple students

### 3. Key Pages

- **Home Page**: Main landing page for the website
- **About Us Page**: Information about the school
- **Programs Page**: Details about educational programs
- **PPDB Page**: Online registration for new students
- **Contact Page**: Contact information and inquiry form
- **Blog Page**: School news and articles
- **Blog Post Detail Page**: Individual blog post
- **Dashboard Home**: Main dashboard for authenticated users
- **Teacher Timeline Input**: Interface for teachers to record student progress
- **Student Achievement Dashboard**: Visual dashboard of student achievements
- **Parent Dashboard**: Dashboard for parents to view their children's progress
- **Admin PPDB Management**: Interface for managing student applications
- **Settings Page**: User settings and preferences

## Development Guidelines

### Code Structure

- **Feature-based Organization**: Components and logic organized by feature
- **Separation of Concerns**: Clear separation between UI, logic, and data access
- **Reusable Components**: Library of reusable UI components
- **Type Safety**: Comprehensive TypeScript types for all components and data
- **Server/Client Separation**: Clear distinction between server and client components

### Best Practices

- **Accessibility First**: Ensure all components are accessible
- **Performance Optimization**: Optimize for speed and efficiency
- **Responsive Design**: Ensure usability across all devices
- **Error Handling**: Comprehensive error handling and user feedback
- **Documentation**: Clear documentation for all components and functions

## Deployment and Infrastructure

- **Vercel Deployment**: Continuous deployment via Vercel
- **Environment Configuration**: Proper environment variable management
- **Monitoring**: Error tracking and performance monitoring
- **Backup Strategy**: Regular database backups
- **CI/CD Pipeline**: Automated testing and deployment

## Future Enhancements

1. **Mobile Application**: Native mobile app for parents and teachers
2. **Advanced Analytics**: Deeper insights into student performance
3. **AI-powered Recommendations**: Personalized learning recommendations
4. **Integrated Learning Management System**: Online learning modules
5. **Financial Management**: Tuition tracking and payment processing
6. **Alumni Network**: Platform for connecting with graduates
7. **Multi-language Support**: Additional language options

## Success Metrics

- **User Adoption**: Percentage of parents and teachers actively using the system
- **Data Accuracy**: Reduction in manual record-keeping errors
- **Communication Efficiency**: Improved parent-teacher communication
- **Administrative Efficiency**: Reduction in administrative workload
- **Student Performance**: Improved tracking and support for student achievements
- **Enrollment Growth**: Increase in new student applications 