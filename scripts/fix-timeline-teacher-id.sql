-- <PERSON><PERSON>t untuk memperbaiki timeline_entries agar teacher_id bisa null untuk admin
-- Ini lebih masuk akal karena admin hanya input data, bukan mengajar

-- 1. Ubah constraint teacher_id menjadi nullable
ALTER TABLE timeline_entries ALTER COLUMN teacher_id DROP NOT NULL;

-- 2. Ubah constraint behavior_records juga jika ada
ALTER TABLE behavior_records ALTER COLUMN teacher_id DROP NOT NULL;

-- 3. Update existing records yang mungkin punya teacher_id admin
-- Set teacher_id = NULL untuk records yang dibuat oleh admin
UPDATE timeline_entries 
SET teacher_id = NULL 
WHERE teacher_id IN (
    SELECT t.id 
    FROM teachers t 
    JOIN profiles p ON p.id = t.profile_id 
    WHERE p.role = 'admin'
);

UPDATE behavior_records 
SET teacher_id = NULL 
WHERE teacher_id IN (
    SELECT t.id 
    FROM teachers t 
    JOIN profiles p ON p.id = t.profile_id 
    WHERE p.role = 'admin'
);

-- 4. <PERSON><PERSON> teacher records yang dibuat untuk admin (opsional)
-- Uncomment jika ingin menghapus teacher records admin
/*
DELETE FROM teachers 
WHERE profile_id IN (
    SELECT id FROM profiles WHERE role = 'admin'
);
*/

-- 5. Verifikasi perubahan
SELECT 
    'timeline_entries' as table_name,
    COUNT(*) as total_records,
    COUNT(teacher_id) as records_with_teacher,
    COUNT(*) - COUNT(teacher_id) as records_without_teacher
FROM timeline_entries

UNION ALL

SELECT 
    'behavior_records' as table_name,
    COUNT(*) as total_records,
    COUNT(teacher_id) as records_with_teacher,
    COUNT(*) - COUNT(teacher_id) as records_without_teacher
FROM behavior_records;

-- 6. Cek constraint
SELECT 
    table_name,
    column_name,
    is_nullable,
    data_type
FROM information_schema.columns 
WHERE table_name IN ('timeline_entries', 'behavior_records') 
AND column_name = 'teacher_id';

SELECT 'Timeline teacher_id constraint updated successfully!' as status;
