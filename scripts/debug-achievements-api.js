// Script untuk debug API achievements
// Jalankan dengan: node scripts/debug-achievements-api.js

const BASE_URL = 'http://localhost:3001'

// Helper function untuk HTTP request dengan detailed logging
async function makeRequest(url, options = {}) {
  console.log(`\n🔍 Making request to: ${url}`)
  console.log(`Method: ${options.method || 'GET'}`)
  
  if (options.body) {
    console.log(`Body: ${options.body}`)
  }
  
  try {
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    })
    
    console.log(`Response Status: ${response.status} ${response.statusText}`)
    
    const data = await response.json()
    console.log(`Response Data:`, JSON.stringify(data, null, 2))
    
    return { 
      success: response.ok, 
      data, 
      status: response.status,
      statusText: response.statusText 
    }
  } catch (error) {
    console.error(`❌ Request failed:`, error.message)
    return { 
      success: false, 
      error: error.message,
      status: 0,
      statusText: 'Network Error'
    }
  }
}

// Test basic connectivity
async function testConnectivity() {
  console.log('\n=== Testing Basic Connectivity ===')
  
  try {
    const response = await fetch(`${BASE_URL}/api/health`)
    console.log(`Health check status: ${response.status}`)
  } catch (error) {
    console.log(`❌ Cannot connect to server: ${error.message}`)
    console.log('Make sure your development server is running with: npm run dev')
    return false
  }
  
  return true
}

// Test achievements API with different scenarios
async function testAchievementsAPI() {
  console.log('\n=== Testing Achievements API ===')
  
  // Test 1: Basic GET without parameters
  console.log('\n--- Test 1: Basic GET /api/achievements ---')
  const basicResult = await makeRequest(`${BASE_URL}/api/achievements`)
  
  if (!basicResult.success) {
    console.log('❌ Basic GET failed. Checking possible causes:')
    
    if (basicResult.status === 401) {
      console.log('- Authentication required. User not logged in.')
    } else if (basicResult.status === 403) {
      console.log('- Authorization failed. User does not have permission.')
    } else if (basicResult.status === 500) {
      console.log('- Server error. Possible database or RLS policy issue.')
    } else if (basicResult.status === 0) {
      console.log('- Network error. Server might not be running.')
    }
    
    return basicResult
  }
  
  // Test 2: GET with limit parameter
  console.log('\n--- Test 2: GET with limit parameter ---')
  await makeRequest(`${BASE_URL}/api/achievements?limit=5`)
  
  // Test 3: GET with studentId parameter (if we have students)
  console.log('\n--- Test 3: Testing with studentId parameter ---')
  const studentsResult = await makeRequest(`${BASE_URL}/api/students`)
  
  if (studentsResult.success && studentsResult.data.students?.length > 0) {
    const studentId = studentsResult.data.students[0].id
    console.log(`Using student ID: ${studentId}`)
    await makeRequest(`${BASE_URL}/api/achievements?studentId=${studentId}`)
  } else {
    console.log('No students found to test with studentId parameter')
  }
  
  return basicResult
}

// Test related APIs that achievements depends on
async function testRelatedAPIs() {
  console.log('\n=== Testing Related APIs ===')
  
  // Test students API
  console.log('\n--- Testing Students API ---')
  const studentsResult = await makeRequest(`${BASE_URL}/api/students`)
  
  // Test subjects API
  console.log('\n--- Testing Subjects API ---')
  const subjectsResult = await makeRequest(`${BASE_URL}/api/subjects`)
  
  // Test teachers API
  console.log('\n--- Testing Teachers API ---')
  const teachersResult = await makeRequest(`${BASE_URL}/api/teachers`)
  
  return {
    students: studentsResult,
    subjects: subjectsResult,
    teachers: teachersResult
  }
}

// Test database connectivity through a simple API
async function testDatabaseConnectivity() {
  console.log('\n=== Testing Database Connectivity ===')
  
  // Try to access a simple table
  console.log('\n--- Testing Profiles API (should be simple) ---')
  const profilesResult = await makeRequest(`${BASE_URL}/api/profile`)
  
  return profilesResult
}

// Analyze the error and provide suggestions
function analyzeError(achievementsResult, relatedResults) {
  console.log('\n=== Error Analysis & Suggestions ===')
  
  if (!achievementsResult.success) {
    console.log('\n🔍 Achievements API failed. Analyzing...')
    
    if (achievementsResult.status === 500) {
      console.log('\n💡 Possible causes for 500 error:')
      console.log('1. Database table "achievements" does not exist')
      console.log('2. RLS policies are blocking access')
      console.log('3. Foreign key references are invalid')
      console.log('4. Query syntax error in the API')
      
      console.log('\n🔧 Suggested fixes:')
      console.log('1. Run: scripts/fix-achievements-rls.sql in Supabase SQL Editor')
      console.log('2. Check if achievements table exists in Supabase dashboard')
      console.log('3. Verify foreign key constraints are correct')
      console.log('4. Check server logs for detailed error messages')
    }
    
    if (achievementsResult.status === 401) {
      console.log('\n💡 Authentication required:')
      console.log('1. Make sure you are logged in to the application')
      console.log('2. Check if session is valid')
      console.log('3. Verify authentication middleware is working')
    }
    
    if (achievementsResult.status === 403) {
      console.log('\n💡 Authorization failed:')
      console.log('1. Check RLS policies for achievements table')
      console.log('2. Verify user role and permissions')
      console.log('3. Check if user profile exists in profiles table')
    }
  }
  
  // Check related APIs
  const relatedFailures = []
  if (!relatedResults.students.success) relatedFailures.push('students')
  if (!relatedResults.subjects.success) relatedFailures.push('subjects')
  if (!relatedResults.teachers.success) relatedFailures.push('teachers')
  
  if (relatedFailures.length > 0) {
    console.log(`\n⚠️  Related APIs also failing: ${relatedFailures.join(', ')}`)
    console.log('This suggests a broader authentication or database issue.')
  } else {
    console.log('\n✅ Related APIs are working, issue is specific to achievements.')
  }
}

// Main debug function
async function debugAchievements() {
  console.log('🚀 Starting Achievements API Debug...')
  console.log('This script will help identify why achievements are failing to load.')
  
  // Test basic connectivity
  const isConnected = await testConnectivity()
  if (!isConnected) {
    return
  }
  
  // Test database connectivity
  await testDatabaseConnectivity()
  
  // Test related APIs
  const relatedResults = await testRelatedAPIs()
  
  // Test achievements API
  const achievementsResult = await testAchievementsAPI()
  
  // Analyze results
  analyzeError(achievementsResult, relatedResults)
  
  console.log('\n🎯 Summary:')
  if (achievementsResult.success) {
    console.log('✅ Achievements API is working correctly!')
    console.log(`Total achievements found: ${achievementsResult.data.total || 0}`)
  } else {
    console.log('❌ Achievements API is failing.')
    console.log('Please follow the suggested fixes above.')
  }
  
  console.log('\n📋 Next Steps:')
  console.log('1. If RLS policies are the issue, run: scripts/fix-achievements-rls.sql')
  console.log('2. Check browser console for additional error details')
  console.log('3. Check server logs with: npm run dev')
  console.log('4. Verify you are logged in as an admin user')
}

// Run debug if this script is executed directly
if (require.main === module) {
  debugAchievements()
}

module.exports = { debugAchievements, testAchievementsAPI, testRelatedAPIs }
