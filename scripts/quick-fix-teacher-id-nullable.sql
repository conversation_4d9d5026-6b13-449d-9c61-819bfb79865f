-- Quick fix untuk membuat teacher_id nullable
-- Jalankan script ini di Supabase SQL Editor

-- 1. Cek constraint saat ini
SELECT 
    table_name,
    column_name,
    is_nullable,
    data_type
FROM information_schema.columns 
WHERE table_name IN ('timeline_entries', 'behavior_records') 
AND column_name = 'teacher_id';

-- 2. Ubah constraint menjadi nullable
ALTER TABLE timeline_entries ALTER COLUMN teacher_id DROP NOT NULL;
ALTER TABLE behavior_records ALTER COLUMN teacher_id DROP NOT NULL;

-- 3. <PERSON><PERSON><PERSON><PERSON><PERSON> per<PERSON>han
SELECT 
    table_name,
    column_name,
    is_nullable,
    data_type
FROM information_schema.columns 
WHERE table_name IN ('timeline_entries', 'behavior_records') 
AND column_name = 'teacher_id';

-- 4. Test insert dengan teacher_id null
INSERT INTO timeline_entries (student_id, teacher_id, month, year) 
VALUES ('test-student-id', NULL, 'Juli', 2025)
ON CONFLICT DO NOTHING;

-- 5. <PERSON>pus test data
DELETE FROM timeline_entries WHERE student_id = 'test-student-id';

SELECT 'teacher_id constraint updated successfully!' as status;
