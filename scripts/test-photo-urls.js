// Script untuk test apakah photo URLs bisa diakses
// Jalankan dengan: node scripts/test-photo-urls.js

const BASE_URL = 'http://localhost:3001'

// Helper function untuk HTTP request
async function makeRequest(url, options = {}) {
  try {
    const response = await fetch(url, options)
    const data = await response.json()
    return { success: response.ok, data, status: response.status }
  } catch (error) {
    return { success: false, error: error.message, status: 0 }
  }
}

// Test if image URL is accessible
async function testImageUrl(url, name) {
  try {
    console.log(`Testing: ${name}`)
    console.log(`URL: ${url}`)
    
    const response = await fetch(url)
    
    if (response.ok) {
      const contentType = response.headers.get('content-type')
      const contentLength = response.headers.get('content-length')
      
      console.log(`✅ Image accessible`)
      console.log(`   Content-Type: ${contentType}`)
      console.log(`   Content-Length: ${contentLength} bytes`)
      
      if (contentType && contentType.startsWith('image/')) {
        console.log(`✅ Valid image content type`)
      } else {
        console.log(`⚠️  Unexpected content type: ${contentType}`)
      }
      
      return true
    } else {
      console.log(`❌ Image not accessible: ${response.status} ${response.statusText}`)
      return false
    }
  } catch (error) {
    console.log(`❌ Error accessing image: ${error.message}`)
    return false
  }
}

// Get teachers with photos and test their URLs
async function testTeacherPhotos() {
  console.log('\n=== Testing Teacher Photo URLs ===')
  
  const result = await makeRequest(`${BASE_URL}/api/teachers`)
  
  if (!result.success) {
    console.log('❌ Failed to get teachers')
    return
  }
  
  const teachers = result.data.teachers || []
  const teachersWithPhotos = teachers.filter(teacher => 
    teacher.photo_url && 
    teacher.photo_url.trim() !== ''
  )
  
  console.log(`Found ${teachersWithPhotos.length} teachers with photos`)
  
  if (teachersWithPhotos.length === 0) {
    console.log('No teachers with photos to test')
    return
  }
  
  let accessibleCount = 0
  
  for (const teacher of teachersWithPhotos) {
    console.log(`\n--- Testing ${teacher.name} ---`)
    const isAccessible = await testImageUrl(teacher.photo_url, teacher.name)
    if (isAccessible) {
      accessibleCount++
    }
  }
  
  console.log(`\n📊 Results: ${accessibleCount}/${teachersWithPhotos.length} photos accessible`)
}

// Get students with photos and test their URLs
async function testStudentPhotos() {
  console.log('\n=== Testing Student Photo URLs ===')
  
  const result = await makeRequest(`${BASE_URL}/api/students`)
  
  if (!result.success) {
    console.log('❌ Failed to get students')
    return
  }
  
  const students = result.data.students || []
  const studentsWithPhotos = students.filter(student => 
    student.photo_url && 
    student.photo_url.trim() !== ''
  )
  
  console.log(`Found ${studentsWithPhotos.length} students with photos`)
  
  if (studentsWithPhotos.length === 0) {
    console.log('No students with photos to test')
    return
  }
  
  let accessibleCount = 0
  
  for (const student of studentsWithPhotos) {
    console.log(`\n--- Testing ${student.name} ---`)
    const isAccessible = await testImageUrl(student.photo_url, student.name)
    if (isAccessible) {
      accessibleCount++
    }
  }
  
  console.log(`\n📊 Results: ${accessibleCount}/${studentsWithPhotos.length} photos accessible`)
}

// Test specific Supabase URL format
async function testSupabaseUrls() {
  console.log('\n=== Testing Supabase URL Patterns ===')
  
  // Test the specific URL from your example
  const testUrl = "https://labs-sptsa.xd1iar.easypanel.host/storage/v1/object/public/photos/teachers/teacher_85e4443c-cc53-457d-8050-2a7026c848a2_1752686331734_3surto.png"
  
  console.log('Testing your specific URL:')
  await testImageUrl(testUrl, 'Test Teacher')
}

// Test CORS and browser compatibility
async function testCorsAndBrowser() {
  console.log('\n=== Testing CORS and Browser Compatibility ===')
  
  console.log('CORS Test:')
  console.log('- Supabase Storage should have CORS enabled by default')
  console.log('- If images work in direct browser access but not in app, check CORS')
  console.log('')
  
  console.log('Browser Console Test:')
  console.log('1. Open browser DevTools (F12)')
  console.log('2. Go to Network tab')
  console.log('3. Navigate to teacher list page')
  console.log('4. Look for failed image requests (red entries)')
  console.log('5. Check error messages in Console tab')
  console.log('')
  
  console.log('Direct URL Test:')
  console.log('1. Copy photo URL from database')
  console.log('2. Paste in new browser tab')
  console.log('3. Should show the image directly')
}

// Debug frontend image loading
async function debugFrontendImageLoading() {
  console.log('\n=== Frontend Image Loading Debug ===')
  
  console.log('Common issues and solutions:')
  console.log('')
  
  console.log('1. Image URL is correct but not showing:')
  console.log('   - Check browser console for CORS errors')
  console.log('   - Verify Next.js Image component configuration')
  console.log('   - Check if domain is allowed in next.config.js')
  console.log('')
  
  console.log('2. Avatar component not showing image:')
  console.log('   - AvatarImage src prop should use actual photo_url')
  console.log('   - Check if onError handler is working')
  console.log('   - Verify AvatarFallback is not overriding image')
  console.log('')
  
  console.log('3. Image loads but appears broken:')
  console.log('   - Check image file integrity')
  console.log('   - Verify content-type headers')
  console.log('   - Test image URL in incognito mode')
  console.log('')
  
  console.log('4. Next.js Image optimization issues:')
  console.log('   - Add Supabase domain to next.config.js images.domains')
  console.log('   - Or use unoptimized={true} prop temporarily')
}

// Check Next.js configuration
async function checkNextJsConfig() {
  console.log('\n=== Next.js Configuration Check ===')
  
  console.log('Required next.config.js configuration:')
  console.log('')
  console.log('module.exports = {')
  console.log('  images: {')
  console.log('    domains: [')
  console.log('      "labs-sptsa.xd1iar.easypanel.host", // Your Supabase domain')
  console.log('      // Add other domains as needed')
  console.log('    ],')
  console.log('  },')
  console.log('}')
  console.log('')
  
  console.log('If using remotePatterns (Next.js 13+):')
  console.log('')
  console.log('module.exports = {')
  console.log('  images: {')
  console.log('    remotePatterns: [')
  console.log('      {')
  console.log('        protocol: "https",')
  console.log('        hostname: "labs-sptsa.xd1iar.easypanel.host",')
  console.log('        port: "",')
  console.log('        pathname: "/storage/v1/object/public/**",')
  console.log('      },')
  console.log('    ],')
  console.log('  },')
  console.log('}')
}

// Main test function
async function runPhotoUrlTests() {
  console.log('🚀 Starting Photo URL Tests...')
  console.log('This will test if photo URLs are accessible and working correctly')
  console.log('')
  
  try {
    // Test specific Supabase URL
    await testSupabaseUrls()
    
    // Test teacher photos
    await testTeacherPhotos()
    
    // Test student photos
    await testStudentPhotos()
    
    // CORS and browser tests
    await testCorsAndBrowser()
    
    // Frontend debugging
    await debugFrontendImageLoading()
    
    // Next.js config check
    await checkNextJsConfig()
    
    console.log('\n🎉 Photo URL tests completed!')
    
    console.log('\n📋 Next Steps:')
    console.log('1. If URLs are accessible but images not showing in frontend:')
    console.log('   - Check browser console for errors')
    console.log('   - Update next.config.js with Supabase domain')
    console.log('   - Verify Avatar components use photo_url prop')
    console.log('')
    console.log('2. If URLs are not accessible:')
    console.log('   - Check Supabase Storage bucket is public')
    console.log('   - Verify storage policies allow public read')
    console.log('   - Test direct URL access in browser')
    
  } catch (error) {
    console.error('❌ Test execution failed:', error.message)
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  runPhotoUrlTests()
}

module.exports = { 
  runPhotoUrlTests,
  testTeacherPhotos,
  testStudentPhotos,
  testImageUrl
}
