// Script untuk debug masalah month di timeline
// Jalankan dengan: node scripts/debug-timeline-month.js

const BASE_URL = 'http://localhost:3001'

async function makeRequest(url, options = {}) {
  try {
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    })
    
    const data = await response.json()
    return { success: response.ok, data, status: response.status }
  } catch (error) {
    return { success: false, error: error.message, status: 0 }
  }
}

async function debugTimelineMonth() {
  console.log('🔍 Debugging Timeline Month Issue...')
  
  // 1. Get a student ID
  console.log('\n=== Getting Student ID ===')
  const studentsResult = await makeRequest(`${BASE_URL}/api/students`)
  
  if (!studentsResult.success || !studentsResult.data.students?.length) {
    console.log('❌ No students found')
    return
  }
  
  const studentId = studentsResult.data.students[0].id
  const studentName = studentsResult.data.students[0].name
  console.log(`✅ Using student: ${studentName} (${studentId})`)
  
  // 2. Get timeline data
  console.log('\n=== Getting Timeline Data ===')
  const timelineResult = await makeRequest(`${BASE_URL}/api/students/${studentId}/timeline`)
  
  if (!timelineResult.success) {
    console.log('❌ Failed to get timeline:', timelineResult.data?.error || timelineResult.error)
    return
  }
  
  console.log(`✅ Timeline API success`)
  console.log(`Total entries: ${timelineResult.data.timeline?.length || 0}`)
  
  // 3. Analyze month data
  console.log('\n=== Analyzing Month Data ===')
  
  if (!timelineResult.data.timeline || timelineResult.data.timeline.length === 0) {
    console.log('📝 No timeline entries found')
    
    // Check if we can get timeline entries directly from database
    console.log('\n=== Checking Timeline Entries Table ===')
    console.log('You may need to check the timeline_entries table in Supabase:')
    console.log('SELECT * FROM timeline_entries WHERE student_id = \'' + studentId + '\';')
    
    return
  }
  
  timelineResult.data.timeline.forEach((entry, index) => {
    console.log(`\n--- Entry ${index + 1} ---`)
    console.log(`ID: ${entry.id}`)
    console.log(`Month: "${entry.month}" (type: ${typeof entry.month})`)
    console.log(`Year: ${entry.year} (type: ${typeof entry.year})`)
    console.log(`Created: ${entry.created_at}`)
    
    // Test month parsing
    const monthNames = [
      'Januari', 'Februari', 'Maret', 'April', 'Mei', 'Juni',
      'Juli', 'Agustus', 'September', 'Oktober', 'November', 'Desember'
    ]
    
    let monthIndex = 0
    let monthParseMethod = 'default'
    
    if (entry.month) {
      if (typeof entry.month === 'string') {
        // Try to find month name
        const foundIndex = monthNames.findIndex(name => 
          name.toLowerCase() === entry.month.toString().toLowerCase()
        )
        
        if (foundIndex !== -1) {
          monthIndex = foundIndex
          monthParseMethod = 'string match'
        } else {
          // Try parsing as number
          const parsed = parseInt(entry.month)
          if (!isNaN(parsed) && parsed >= 1 && parsed <= 12) {
            monthIndex = parsed - 1
            monthParseMethod = 'string to number'
          }
        }
      } else {
        // If month is already a number
        const monthNum = Number(entry.month)
        if (!isNaN(monthNum) && monthNum >= 1 && monthNum <= 12) {
          monthIndex = monthNum - 1
          monthParseMethod = 'number'
        }
      }
    }
    
    const monthName = monthNames[monthIndex] || 'Bulan Tidak Diketahui'
    const displayText = `${monthName} ${entry.year}`
    
    console.log(`Parsed month index: ${monthIndex}`)
    console.log(`Parsed month name: ${monthName}`)
    console.log(`Parse method: ${monthParseMethod}`)
    console.log(`Display text: "${displayText}"`)
    
    if (displayText.includes('undefined')) {
      console.log('🚨 FOUND UNDEFINED ISSUE!')
      console.log('Raw data:', JSON.stringify({
        month: entry.month,
        year: entry.year,
        monthIndex,
        monthName
      }, null, 2))
    }
  })
  
  // 4. Check database schema
  console.log('\n=== Database Schema Check ===')
  console.log('Please verify in Supabase that timeline_entries table has:')
  console.log('- month column (should be VARCHAR or INTEGER)')
  console.log('- year column (should be INTEGER)')
  console.log('')
  console.log('SQL to check schema:')
  console.log('SELECT column_name, data_type FROM information_schema.columns')
  console.log('WHERE table_name = \'timeline_entries\' AND column_name IN (\'month\', \'year\');')
  
  // 5. Sample data check
  console.log('\n=== Sample Data Check ===')
  console.log('SQL to check sample data:')
  console.log('SELECT id, student_id, month, year, created_at FROM timeline_entries LIMIT 5;')
  
  console.log('\n✅ Debug completed!')
  console.log('\nIf month shows as "undefined 2025":')
  console.log('1. Check if month column has NULL values')
  console.log('2. Check if month is stored as string name vs number')
  console.log('3. Verify data was inserted correctly')
}

if (require.main === module) {
  debugTimelineMonth()
}

module.exports = { debugTimelineMonth }
