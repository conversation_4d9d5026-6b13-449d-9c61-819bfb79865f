// Script untuk testing API achievements setelah tabel dibuat
// Jalankan dengan: node scripts/test-achievements-api.js

const BASE_URL = 'http://localhost:3001'

// Helper function untuk HTTP request
async function makeRequest(url, options = {}) {
  try {
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    })
    
    const data = await response.json()
    
    return { 
      success: response.ok, 
      data, 
      status: response.status,
      statusText: response.statusText 
    }
  } catch (error) {
    return { 
      success: false, 
      error: error.message,
      status: 0,
      statusText: 'Network Error'
    }
  }
}

// Test GET achievements
async function testGetAchievements() {
  console.log('\n=== Testing GET /api/achievements ===')
  
  const result = await makeRequest(`${BASE_URL}/api/achievements`)
  
  if (result.success) {
    console.log('✅ GET Achievements: Success')
    console.log(`Status: ${result.status}`)
    console.log(`Total achievements: ${result.data.total || 0}`)
    console.log(`Achievements count: ${result.data.achievements?.length || 0}`)
    
    if (result.data.achievements && result.data.achievements.length > 0) {
      console.log('Sample achievement:', result.data.achievements[0])
    }
  } else {
    console.log('❌ GET Achievements: Failed')
    console.log(`Status: ${result.status} ${result.statusText}`)
    console.log('Error:', result.data?.error || result.error)
  }
  
  return result
}

// Test GET students (needed for achievement creation)
async function testGetStudents() {
  console.log('\n=== Testing GET /api/students ===')
  
  const result = await makeRequest(`${BASE_URL}/api/students`)
  
  if (result.success) {
    console.log('✅ GET Students: Success')
    console.log(`Students count: ${result.data.students?.length || 0}`)
    return result.data.students || []
  } else {
    console.log('❌ GET Students: Failed')
    console.log('Error:', result.data?.error || result.error)
    return []
  }
}

// Test GET subjects (needed for achievement creation)
async function testGetSubjects() {
  console.log('\n=== Testing GET /api/subjects ===')
  
  const result = await makeRequest(`${BASE_URL}/api/subjects`)
  
  if (result.success) {
    console.log('✅ GET Subjects: Success')
    console.log(`Subjects count: ${result.data.subjects?.length || 0}`)
    return result.data.subjects || []
  } else {
    console.log('❌ GET Subjects: Failed')
    console.log('Error:', result.data?.error || result.error)
    return []
  }
}

// Test GET teachers (needed for achievement creation)
async function testGetTeachers() {
  console.log('\n=== Testing GET /api/teachers ===')
  
  const result = await makeRequest(`${BASE_URL}/api/teachers`)
  
  if (result.success) {
    console.log('✅ GET Teachers: Success')
    console.log(`Teachers count: ${result.data.teachers?.length || 0}`)
    return result.data.teachers || []
  } else {
    console.log('❌ GET Teachers: Failed')
    console.log('Error:', result.data?.error || result.error)
    return []
  }
}

// Test POST achievement
async function testCreateAchievement(students, subjects, teachers) {
  console.log('\n=== Testing POST /api/achievements ===')
  
  if (!students.length || !subjects.length || !teachers.length) {
    console.log('❌ Cannot test POST: Missing required data (students, subjects, or teachers)')
    return null
  }
  
  const testAchievement = {
    student_id: students[0].id,
    subject_id: subjects[0].id,
    value: 'Test Achievement - Hafal Juz 30',
    grade: 'A',
    notes: 'Test notes - Sangat baik dalam menghafal',
    verified_by: teachers[0].id,
    achievement_date: new Date().toISOString().split('T')[0]
  }
  
  console.log('Creating achievement with data:', testAchievement)
  
  const result = await makeRequest(`${BASE_URL}/api/achievements`, {
    method: 'POST',
    body: JSON.stringify(testAchievement)
  })
  
  if (result.success) {
    console.log('✅ POST Achievement: Success')
    console.log('Created achievement:', result.data)
    return result.data
  } else {
    console.log('❌ POST Achievement: Failed')
    console.log(`Status: ${result.status} ${result.statusText}`)
    console.log('Error:', result.data?.error || result.error)
    return null
  }
}

// Test PUT achievement
async function testUpdateAchievement(achievementId) {
  console.log('\n=== Testing PUT /api/achievements ===')
  
  if (!achievementId) {
    console.log('❌ Cannot test PUT: No achievement ID provided')
    return null
  }
  
  const updateData = {
    value: 'Updated Test Achievement - Hafal Juz 29-30',
    grade: 'A+',
    notes: 'Updated notes - Excellent performance'
  }
  
  const result = await makeRequest(`${BASE_URL}/api/achievements?id=${achievementId}`, {
    method: 'PUT',
    body: JSON.stringify(updateData)
  })
  
  if (result.success) {
    console.log('✅ PUT Achievement: Success')
    console.log('Updated achievement:', result.data)
    return result.data
  } else {
    console.log('❌ PUT Achievement: Failed')
    console.log(`Status: ${result.status} ${result.statusText}`)
    console.log('Error:', result.data?.error || result.error)
    return null
  }
}

// Test DELETE achievement
async function testDeleteAchievement(achievementId) {
  console.log('\n=== Testing DELETE /api/achievements ===')
  
  if (!achievementId) {
    console.log('❌ Cannot test DELETE: No achievement ID provided')
    return false
  }
  
  const result = await makeRequest(`${BASE_URL}/api/achievements?id=${achievementId}`, {
    method: 'DELETE'
  })
  
  if (result.success) {
    console.log('✅ DELETE Achievement: Success')
    return true
  } else {
    console.log('❌ DELETE Achievement: Failed')
    console.log(`Status: ${result.status} ${result.statusText}`)
    console.log('Error:', result.data?.error || result.error)
    return false
  }
}

// Main test function
async function runAchievementsTests() {
  console.log('🚀 Starting Achievements API Tests...')
  console.log('Make sure your development server is running on http://localhost:3001')
  console.log('Make sure you have created the achievements table using scripts/create-achievements-table.sql')
  
  try {
    // Test basic GET first
    const getResult = await testGetAchievements()
    
    // Get required data for POST test
    const students = await testGetStudents()
    const subjects = await testGetSubjects()
    const teachers = await testGetTeachers()
    
    // Test POST (create)
    const createdAchievement = await testCreateAchievement(students, subjects, teachers)
    
    // Test PUT (update) if creation was successful
    let updatedAchievement = null
    if (createdAchievement && createdAchievement.data) {
      updatedAchievement = await testUpdateAchievement(createdAchievement.data.id)
    }
    
    // Test DELETE if creation was successful
    if (createdAchievement && createdAchievement.data) {
      await testDeleteAchievement(createdAchievement.data.id)
    }
    
    // Final GET to verify state
    console.log('\n=== Final State Check ===')
    await testGetAchievements()
    
    console.log('\n🎉 All tests completed!')
    console.log('\nNote: Some tests may fail due to:')
    console.log('- Authentication requirements (make sure you are logged in)')
    console.log('- Missing data (students, subjects, teachers)')
    console.log('- Database table not created (run scripts/create-achievements-table.sql)')
    console.log('- RLS policies blocking access')
    
  } catch (error) {
    console.error('❌ Test execution failed:', error.message)
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  runAchievementsTests()
}

module.exports = { 
  runAchievementsTests, 
  testGetAchievements, 
  testCreateAchievement,
  testUpdateAchievement,
  testDeleteAchievement
}
