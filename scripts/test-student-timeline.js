// Script untuk testing Student Timeline feature
// Jalankan dengan: node scripts/test-student-timeline.js

const BASE_URL = 'http://localhost:3001'

// Helper function untuk HTTP request
async function makeRequest(url, options = {}) {
  try {
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    })
    
    const data = await response.json()
    
    return { 
      success: response.ok, 
      data, 
      status: response.status,
      statusText: response.statusText 
    }
  } catch (error) {
    return { 
      success: false, 
      error: error.message,
      status: 0,
      statusText: 'Network Error'
    }
  }
}

// Test get students list to find a student ID
async function getTestStudentId() {
  console.log('\n=== Getting Test Student ID ===')
  
  const result = await makeRequest(`${BASE_URL}/api/students`)
  
  if (result.success && result.data.students?.length > 0) {
    const student = result.data.students[0]
    console.log(`✅ Found test student: ${student.name} (ID: ${student.id})`)
    return student.id
  } else {
    console.log('❌ No students found for testing')
    return null
  }
}

// Test student timeline API
async function testStudentTimelineAPI(studentId) {
  console.log(`\n=== Testing Student Timeline API ===`)
  console.log(`Student ID: ${studentId}`)
  
  const result = await makeRequest(`${BASE_URL}/api/students/${studentId}/timeline`)
  
  if (result.success) {
    console.log('✅ Timeline API: Success')
    console.log(`Status: ${result.status}`)
    console.log(`Student: ${result.data.student?.name}`)
    console.log(`Timeline entries: ${result.data.timeline?.length || 0}`)
    
    if (result.data.timeline && result.data.timeline.length > 0) {
      console.log('\n📋 Timeline Entries:')
      result.data.timeline.forEach((entry, index) => {
        console.log(`  ${index + 1}. ${entry.month}/${entry.year}`)
        console.log(`     - Subjects: ${entry.timeline_details?.length || 0}`)
        console.log(`     - Activities: ${entry.timeline_activities?.length || 0}`)
        console.log(`     - Behavior: ${entry.behavior_records?.length || 0}`)
      })
    } else {
      console.log('📝 No timeline entries found for this student')
    }
    
    return result.data
  } else {
    console.log('❌ Timeline API: Failed')
    console.log(`Status: ${result.status} ${result.statusText}`)
    console.log('Error:', result.data?.error || result.error)
    return null
  }
}

// Test timeline data structure
async function validateTimelineData(timelineData) {
  console.log('\n=== Validating Timeline Data Structure ===')
  
  if (!timelineData || !timelineData.timeline) {
    console.log('❌ No timeline data to validate')
    return false
  }
  
  let isValid = true
  
  // Check required fields
  const requiredFields = ['student', 'timeline', 'total']
  requiredFields.forEach(field => {
    if (!timelineData[field]) {
      console.log(`❌ Missing required field: ${field}`)
      isValid = false
    }
  })
  
  // Check student object
  if (timelineData.student) {
    const studentFields = ['id', 'name', 'student_id']
    studentFields.forEach(field => {
      if (!timelineData.student[field]) {
        console.log(`❌ Missing student field: ${field}`)
        isValid = false
      }
    })
  }
  
  // Check timeline entries
  if (timelineData.timeline && timelineData.timeline.length > 0) {
    timelineData.timeline.forEach((entry, index) => {
      const entryFields = ['id', 'student_id', 'month', 'year', 'created_at']
      entryFields.forEach(field => {
        if (!entry[field]) {
          console.log(`❌ Timeline entry ${index + 1} missing field: ${field}`)
          isValid = false
        }
      })
      
      // Check timeline_details structure
      if (entry.timeline_details) {
        entry.timeline_details.forEach((detail, detailIndex) => {
          const detailFields = ['id', 'subject_id', 'value', 'grade']
          detailFields.forEach(field => {
            if (!detail[field]) {
              console.log(`❌ Timeline detail ${detailIndex + 1} in entry ${index + 1} missing field: ${field}`)
              isValid = false
            }
          })
        })
      }
    })
  }
  
  if (isValid) {
    console.log('✅ Timeline data structure is valid')
  } else {
    console.log('❌ Timeline data structure has issues')
  }
  
  return isValid
}

// Test different student scenarios
async function testDifferentStudents() {
  console.log('\n=== Testing Different Student Scenarios ===')
  
  // Get all students
  const studentsResult = await makeRequest(`${BASE_URL}/api/students`)
  
  if (!studentsResult.success || !studentsResult.data.students) {
    console.log('❌ Cannot get students list')
    return
  }
  
  const students = studentsResult.data.students.slice(0, 3) // Test first 3 students
  
  for (const student of students) {
    console.log(`\n--- Testing student: ${student.name} ---`)
    const timelineData = await testStudentTimelineAPI(student.id)
    
    if (timelineData) {
      await validateTimelineData(timelineData)
    }
  }
}

// Test error scenarios
async function testErrorScenarios() {
  console.log('\n=== Testing Error Scenarios ===')
  
  // Test with invalid student ID
  console.log('\n--- Testing invalid student ID ---')
  const invalidResult = await makeRequest(`${BASE_URL}/api/students/invalid-id/timeline`)
  
  if (!invalidResult.success) {
    console.log(`✅ Correctly handled invalid ID: ${invalidResult.status} ${invalidResult.statusText}`)
  } else {
    console.log('❌ Should have failed with invalid ID')
  }
  
  // Test with non-existent student ID
  console.log('\n--- Testing non-existent student ID ---')
  const nonExistentResult = await makeRequest(`${BASE_URL}/api/students/00000000-0000-0000-0000-000000000000/timeline`)
  
  if (!nonExistentResult.success && nonExistentResult.status === 404) {
    console.log('✅ Correctly handled non-existent student: 404 Not Found')
  } else {
    console.log('❌ Should have returned 404 for non-existent student')
  }
}

// Test performance
async function testPerformance(studentId) {
  console.log('\n=== Testing Performance ===')
  
  const startTime = Date.now()
  const result = await makeRequest(`${BASE_URL}/api/students/${studentId}/timeline`)
  const endTime = Date.now()
  
  const responseTime = endTime - startTime
  
  console.log(`Response time: ${responseTime}ms`)
  
  if (responseTime < 1000) {
    console.log('✅ Good performance (< 1 second)')
  } else if (responseTime < 3000) {
    console.log('⚠️  Acceptable performance (1-3 seconds)')
  } else {
    console.log('❌ Poor performance (> 3 seconds)')
  }
  
  return responseTime
}

// Main test function
async function runStudentTimelineTests() {
  console.log('🚀 Starting Student Timeline Feature Tests...')
  console.log('Make sure your development server is running on http://localhost:3001')
  
  try {
    // Get a test student ID
    const studentId = await getTestStudentId()
    
    if (!studentId) {
      console.log('❌ Cannot proceed without a test student')
      return
    }
    
    // Test basic timeline API
    const timelineData = await testStudentTimelineAPI(studentId)
    
    // Validate data structure
    if (timelineData) {
      await validateTimelineData(timelineData)
    }
    
    // Test performance
    await testPerformance(studentId)
    
    // Test different students
    await testDifferentStudents()
    
    // Test error scenarios
    await testErrorScenarios()
    
    console.log('\n🎉 All Student Timeline tests completed!')
    
    console.log('\n📋 Manual Testing Checklist:')
    console.log('1. ✅ Navigate to /dashboard/students')
    console.log('2. ✅ Click on any student name')
    console.log('3. ✅ Click on "Timeline" tab')
    console.log('4. ✅ Verify timeline data displays correctly')
    console.log('5. ✅ Test refresh button')
    console.log('6. ✅ Test responsive design on mobile')
    console.log('7. ✅ Test with students that have no timeline data')
    console.log('8. ✅ Test with students that have rich timeline data')
    
  } catch (error) {
    console.error('❌ Test execution failed:', error.message)
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  runStudentTimelineTests()
}

module.exports = { 
  runStudentTimelineTests, 
  testStudentTimelineAPI,
  validateTimelineData,
  testPerformance
}
