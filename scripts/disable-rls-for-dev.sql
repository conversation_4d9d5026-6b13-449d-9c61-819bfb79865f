-- <PERSON><PERSON><PERSON> untuk disable RLS sementara untuk development
-- <PERSON><PERSON><PERSON><PERSON> UNTUK DEVELOPMENT - JANGAN GUNAKAN DI PRODUCTION!
-- Jalankan script ini di Supabase SQL Editor

-- Disable RLS untuk semua tabel
ALTER TABLE IF EXISTS profiles DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS students DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS parents DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS teachers DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS classes DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS subjects DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS achievements DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS class_students DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS student_parents DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS settings DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS timeline_entries DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS blog_posts DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS categories DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS post_categories DISABLE ROW LEVEL SECURITY;

-- Drop semua policy yang mungkin menyebabkan masalah
DO $$ 
DECLARE
    r RECORD;
BEGIN
    -- Drop all policies on all tables
    FOR r IN (SELECT schemaname, tablename, policyname FROM pg_policies WHERE schemaname = 'public') 
    LOOP
        EXECUTE 'DROP POLICY IF EXISTS ' || quote_ident(r.policyname) || ' ON ' || quote_ident(r.schemaname) || '.' || quote_ident(r.tablename);
    END LOOP;
END $$;

-- Pesan konfirmasi
SELECT 'RLS disabled for all tables. This is for DEVELOPMENT only!' as status;
