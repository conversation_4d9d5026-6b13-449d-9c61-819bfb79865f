-- <PERSON>ript untuk memperbaiki data yang sudah ada
-- Jalankan script ini SEBELUM unify-auth-system.sql

-- 1. Periksa data yang ada
SELECT 'Teachers without profiles' as issue, count(*) as count
FROM teachers t
LEFT JOIN profiles p ON p.id = t.user_id
WHERE p.id IS NULL

UNION ALL

SELECT 'Parents without profiles' as issue, count(*) as count
FROM parents pa
LEFT JOIN profiles p ON p.id = pa.user_id
WHERE p.id IS NULL;

-- 2. Tampilkan data teachers yang bermasalah
SELECT 
    'TEACHER' as type,
    t.user_id,
    t.name,
    t.email,
    'Missing profile' as issue
FROM teachers t
LEFT JOIN profiles p ON p.id = t.user_id
WHERE p.id IS NULL;

-- 3. Tampilkan data parents yang bermasalah
SELECT 
    'PARENT' as type,
    pa.user_id,
    pa.name,
    pa.email,
    'Missing profile' as issue
FROM parents pa
LEFT JOIN profiles p ON p.id = pa.user_id
WHERE p.id IS NULL;

-- 4. <PERSON>uat profiles untuk teachers yang tidak punya profile
-- HATI-HATI: Ini akan membuat profile dengan ID yang mungkin tidak ada di auth.users
-- Hanya jalankan jika Anda yakin data ini valid

-- Uncomment baris di bawah jika ingin membuat profiles otomatis:
/*
INSERT INTO profiles (id, email, name, role, created_at, updated_at)
SELECT DISTINCT 
    t.user_id,
    COALESCE(t.email, t.user_id::text || '@generated.com'),
    t.name,
    'teacher',
    NOW(),
    NOW()
FROM teachers t
LEFT JOIN profiles p ON p.id = t.user_id
WHERE p.id IS NULL
ON CONFLICT (id) DO NOTHING;
*/

-- 5. Buat profiles untuk parents yang tidak punya profile
/*
INSERT INTO profiles (id, email, name, role, created_at, updated_at)
SELECT DISTINCT 
    pa.user_id,
    COALESCE(pa.email, pa.user_id::text || '@generated.com'),
    pa.name,
    'parent',
    NOW(),
    NOW()
FROM parents pa
LEFT JOIN profiles p ON p.id = pa.user_id
WHERE p.id IS NULL
ON CONFLICT (id) DO NOTHING;
*/

-- 6. Alternatif: Hapus data yang tidak valid (HATI-HATI!)
-- Uncomment jika ingin menghapus data teachers/parents yang tidak punya profile valid:
/*
DELETE FROM teachers 
WHERE user_id NOT IN (SELECT id FROM profiles);

DELETE FROM parents 
WHERE user_id NOT IN (SELECT id FROM profiles);
*/

-- 7. Periksa lagi setelah perbaikan
SELECT 'Teachers after fix' as status, count(*) as count FROM teachers;
SELECT 'Parents after fix' as status, count(*) as count FROM parents;
SELECT 'Profiles total' as status, count(*) as count FROM profiles;
