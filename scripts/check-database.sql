-- Script untuk memeriksa dan memperbaiki struktur database
-- Jalankan script ini di Supabase SQL Editor

-- 1. <PERSON><PERSON>sa struktur tabel students
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'students' 
ORDER BY ordinal_position;

-- 2. <PERSON><PERSON> kolom profile_id tidak ada, tambahkan
-- ALTER TABLE students ADD COLUMN IF NOT EXISTS profile_id UUID REFERENCES profiles(id);

-- 3. Per<PERSON>sa struktur tabel parents
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'parents' 
ORDER BY ordinal_position;

-- 4. <PERSON><PERSON> kolom profile_id tidak ada di parents, tambahkan
-- ALTER TABLE parents ADD COLUMN IF NOT EXISTS profile_id UUID REFERENCES profiles(id);

-- 5. <PERSON><PERSON><PERSON> struktur tabel teachers
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'teachers' 
ORDER BY ordinal_position;

-- 6. <PERSON><PERSON> kolom profile_id tidak ada di teachers, tambahkan
-- ALTER TABLE teachers ADD COLUMN IF NOT EXISTS profile_id UUID REFERENCES profiles(id);

-- 7. Periksa apakah tabel profiles ada
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'profiles' 
ORDER BY ordinal_position;

-- 8. Periksa RLS policies yang mungkin menyebabkan infinite recursion
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual, with_check
FROM pg_policies 
WHERE tablename IN ('students', 'parents', 'teachers', 'profiles', 'users')
ORDER BY tablename, policyname;
