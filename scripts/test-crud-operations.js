// Script untuk testing CRUD operations
// Jalankan dengan: node scripts/test-crud-operations.js

const BASE_URL = 'http://localhost:3000'

// Helper function untuk melakukan HTTP request
async function makeRequest(url, options = {}) {
  try {
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    })
    
    const data = await response.json()
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${data.error || 'Unknown error'}`)
    }
    
    return { success: true, data, status: response.status }
  } catch (error) {
    return { success: false, error: error.message }
  }
}

// Test Teachers CRUD
async function testTeachersCRUD() {
  console.log('\n=== Testing Teachers CRUD ===')
  
  // Test CREATE
  console.log('\n1. Testing CREATE Teacher...')
  const createResult = await makeRequest(`${BASE_URL}/api/teachers`, {
    method: 'POST',
    body: JSON.stringify({
      name: 'Test Teacher',
      specialization: 'Mathematics',
      photo_url: null
    })
  })
  
  if (createResult.success) {
    console.log('✅ CREATE Teacher: Success')
    console.log('Created teacher:', createResult.data)
    
    const teacherId = createResult.data.teacher?.id
    
    if (teacherId) {
      // Test UPDATE
      console.log('\n2. Testing UPDATE Teacher...')
      const updateResult = await makeRequest(`${BASE_URL}/api/teachers`, {
        method: 'PUT',
        body: JSON.stringify({
          id: teacherId,
          name: 'Updated Test Teacher',
          specialization: 'Physics',
          photo_url: null
        })
      })
      
      if (updateResult.success) {
        console.log('✅ UPDATE Teacher: Success')
        console.log('Updated teacher:', updateResult.data)
      } else {
        console.log('❌ UPDATE Teacher: Failed')
        console.log('Error:', updateResult.error)
      }
      
      // Test DELETE
      console.log('\n3. Testing DELETE Teacher...')
      const deleteResult = await makeRequest(`${BASE_URL}/api/teachers?id=${teacherId}`, {
        method: 'DELETE'
      })
      
      if (deleteResult.success) {
        console.log('✅ DELETE Teacher: Success')
      } else {
        console.log('❌ DELETE Teacher: Failed')
        console.log('Error:', deleteResult.error)
      }
    }
  } else {
    console.log('❌ CREATE Teacher: Failed')
    console.log('Error:', createResult.error)
  }
}

// Test Parents CRUD
async function testParentsCRUD() {
  console.log('\n=== Testing Parents CRUD ===')
  
  // Test CREATE
  console.log('\n1. Testing CREATE Parent...')
  const createResult = await makeRequest(`${BASE_URL}/api/parents`, {
    method: 'POST',
    body: JSON.stringify({
      name: 'Test Parent',
      email: '<EMAIL>',
      phone: '+62812345678',
      address: 'Test Address',
      occupation: 'Test Occupation'
    })
  })
  
  if (createResult.success) {
    console.log('✅ CREATE Parent: Success')
    console.log('Created parent:', createResult.data)
    
    const parentId = createResult.data.parent?.id
    
    if (parentId) {
      // Test UPDATE
      console.log('\n2. Testing UPDATE Parent...')
      const updateResult = await makeRequest(`${BASE_URL}/api/parents`, {
        method: 'PUT',
        body: JSON.stringify({
          id: parentId,
          name: 'Updated Test Parent',
          email: '<EMAIL>',
          phone: '+62812345679',
          address: 'Updated Address',
          occupation: 'Updated Occupation'
        })
      })
      
      if (updateResult.success) {
        console.log('✅ UPDATE Parent: Success')
        console.log('Updated parent:', updateResult.data)
      } else {
        console.log('❌ UPDATE Parent: Failed')
        console.log('Error:', updateResult.error)
      }
      
      // Test DELETE
      console.log('\n3. Testing DELETE Parent...')
      const deleteResult = await makeRequest(`${BASE_URL}/api/parents?id=${parentId}`, {
        method: 'DELETE'
      })
      
      if (deleteResult.success) {
        console.log('✅ DELETE Parent: Success')
      } else {
        console.log('❌ DELETE Parent: Failed')
        console.log('Error:', deleteResult.error)
      }
    }
  } else {
    console.log('❌ CREATE Parent: Failed')
    console.log('Error:', createResult.error)
  }
}

// Test Subjects CRUD
async function testSubjectsCRUD() {
  console.log('\n=== Testing Subjects CRUD ===')
  
  // Test CREATE
  console.log('\n1. Testing CREATE Subject...')
  const createResult = await makeRequest(`${BASE_URL}/api/subjects`, {
    method: 'POST',
    body: JSON.stringify({
      name: 'Test Subject',
      category: 'Test Category',
      description: 'This is a test subject for CRUD testing',
      isActive: true,
      learningObjectives: ['Objective 1', 'Objective 2']
    })
  })
  
  if (createResult.success) {
    console.log('✅ CREATE Subject: Success')
    console.log('Created subject:', createResult.data)
    
    const subjectId = createResult.data.data?.id
    
    if (subjectId) {
      // Test UPDATE
      console.log('\n2. Testing UPDATE Subject...')
      const updateResult = await makeRequest(`${BASE_URL}/api/subjects`, {
        method: 'PUT',
        body: JSON.stringify({
          id: subjectId,
          name: 'Updated Test Subject',
          category: 'Updated Category',
          description: 'This is an updated test subject',
          isActive: true,
          learningObjectives: ['Updated Objective 1', 'Updated Objective 2']
        })
      })
      
      if (updateResult.success) {
        console.log('✅ UPDATE Subject: Success')
        console.log('Updated subject:', updateResult.data)
      } else {
        console.log('❌ UPDATE Subject: Failed')
        console.log('Error:', updateResult.error)
      }
      
      // Test DELETE
      console.log('\n3. Testing DELETE Subject...')
      const deleteResult = await makeRequest(`${BASE_URL}/api/subjects?id=${subjectId}`, {
        method: 'DELETE'
      })
      
      if (deleteResult.success) {
        console.log('✅ DELETE Subject: Success')
      } else {
        console.log('❌ DELETE Subject: Failed')
        console.log('Error:', deleteResult.error)
      }
    }
  } else {
    console.log('❌ CREATE Subject: Failed')
    console.log('Error:', createResult.error)
  }
}

// Main test function
async function runTests() {
  console.log('🚀 Starting CRUD Operations Test...')
  console.log('Make sure your development server is running on http://localhost:3000')
  
  try {
    await testTeachersCRUD()
    await testParentsCRUD()
    await testSubjectsCRUD()
    
    console.log('\n🎉 All tests completed!')
    console.log('\nNote: Some tests may fail due to authentication requirements.')
    console.log('Make sure you are logged in as an admin user when testing.')
    
  } catch (error) {
    console.error('❌ Test execution failed:', error.message)
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  runTests()
}

module.exports = { runTests, testTeachersCRUD, testParentsCRUD, testSubjectsCRUD }
