-- Script untuk membuat teacher record untuk admin users
-- Jalankan script ini jika admin user tidak bisa mengakses timeline

-- 1. Cek admin users yang belum punya teacher record
SELECT 
    p.id,
    p.full_name,
    p.role,
    'Missing teacher record' as issue
FROM profiles p
LEFT JOIN teachers t ON t.profile_id = p.id
WHERE p.role = 'admin' AND t.id IS NULL;

-- 2. Buat teacher record untuk semua admin users yang belum punya
INSERT INTO teachers (profile_id, teacher_id, name, specialization, status, created_at, updated_at)
SELECT 
    p.id,
    'ADMIN-' || extract(epoch from now())::bigint || '-' || substring(p.id::text, 1, 8),
    COALESCE(p.full_name, 'Administrator'),
    'Administrator',
    'active',
    NOW(),
    NOW()
FROM profiles p
LEFT JOIN teachers t ON t.profile_id = p.id
WHERE p.role = 'admin' AND t.id IS NULL
ON CONFLICT (teacher_id) DO NOTHING;

-- 3. <PERSON><PERSON><PERSON><PERSON><PERSON> hasil
SELECT 
    p.id as profile_id,
    p.full_name,
    p.role,
    t.id as teacher_id,
    t.teacher_id as teacher_code,
    t.name as teacher_name,
    t.specialization
FROM profiles p
LEFT JOIN teachers t ON t.profile_id = p.id
WHERE p.role = 'admin'
ORDER BY p.full_name;

-- 4. Pesan sukses
SELECT 'Admin teacher records created successfully!' as status;
