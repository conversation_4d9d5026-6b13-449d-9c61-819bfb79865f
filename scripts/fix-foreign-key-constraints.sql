-- <PERSON>ript untuk memperbaiki foreign key constraints yang bermasalah
-- Jalankan script ini di Supabase SQL Editor

-- 1. Periksa constraint yang ada
SELECT 
    tc.table_name, 
    tc.constraint_name, 
    tc.constraint_type,
    kcu.column_name,
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name 
FROM 
    information_schema.table_constraints AS tc 
    JOIN information_schema.key_column_usage AS kcu
      ON tc.constraint_name = kcu.constraint_name
      AND tc.table_schema = kcu.table_schema
    JOIN information_schema.constraint_column_usage AS ccu
      ON ccu.constraint_name = tc.constraint_name
      AND ccu.table_schema = tc.table_schema
WHERE tc.constraint_type = 'FOREIGN KEY' 
    AND tc.table_name IN ('parents', 'students', 'teachers', 'profiles');

-- 2. Drop foreign key constraints yang bermasalah (sementara untuk development)
-- HANYA UNTUK DEVELOPMENT - JANGAN GUNAKAN DI PRODUCTION!

-- Drop constraint parents -> users
ALTER TABLE parents DROP CONSTRAINT IF EXISTS parents_user_id_fkey;

-- Drop constraint students -> users  
ALTER TABLE students DROP CONSTRAINT IF EXISTS students_user_id_fkey;

-- Drop constraint teachers -> users
ALTER TABLE teachers DROP CONSTRAINT IF EXISTS teachers_user_id_fkey;

-- Drop constraint profiles -> users
ALTER TABLE profiles DROP CONSTRAINT IF EXISTS profiles_user_id_fkey;

-- 3. Alternatif: Ubah constraint menjadi nullable atau cascade
-- Uncomment jika ingin menggunakan pendekatan ini

-- ALTER TABLE parents DROP CONSTRAINT IF EXISTS parents_user_id_fkey;
-- ALTER TABLE parents ADD CONSTRAINT parents_user_id_fkey 
--     FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE SET NULL;

-- ALTER TABLE students DROP CONSTRAINT IF EXISTS students_user_id_fkey;
-- ALTER TABLE students ADD CONSTRAINT students_user_id_fkey 
--     FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE SET NULL;

-- 4. Pesan konfirmasi
SELECT 'Foreign key constraints removed for development. Use with caution!' as status;
