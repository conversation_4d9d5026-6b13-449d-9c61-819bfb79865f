-- <PERSON><PERSON>t untuk membuat ulang tabel tanpa foreign key constraints
-- <PERSON><PERSON><PERSON><PERSON> UNTUK DEVELOPMENT - JANGAN GUNAKAN DI PRODUCTION!
-- Jalankan script ini di Supabase SQL Editor

-- 1. Backup data yang ada (jika ada)
CREATE TABLE IF NOT EXISTS parents_backup AS SELECT * FROM parents;
CREATE TABLE IF NOT EXISTS students_backup AS SELECT * FROM students;
CREATE TABLE IF NOT EXISTS teachers_backup AS SELECT * FROM teachers;

-- 2. Drop tabel yang bermasalah
DROP TABLE IF EXISTS parents CASCADE;
DROP TABLE IF EXISTS students CASCADE;
DROP TABLE IF EXISTS teachers CASCADE;

-- 3. Buat ulang tabel parents tanpa foreign key
CREATE TABLE parents (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID, -- Tidak ada foreign key constraint
    name VARCHAR NOT NULL,
    phone VARCHAR NOT NULL,
    email VARCHAR,
    address TEXT,
    occupation VARCHAR,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 4. Buat ulang tabel students tanpa foreign key
CREATE TABLE students (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID, -- Tidak ada foreign key constraint
    student_id VARCHAR UNIQUE NOT NULL,
    name VARCHAR NOT NULL,
    gender VARCHAR,
    birth_date DATE,
    address TEXT,
    photo_url TEXT,
    batch VARCHAR,
    status VARCHAR DEFAULT 'active',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 5. Buat ulang tabel teachers tanpa foreign key
CREATE TABLE teachers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID, -- Tidak ada foreign key constraint
    teacher_id VARCHAR UNIQUE NOT NULL,
    name VARCHAR NOT NULL,
    specialization VARCHAR,
    photo_url TEXT,
    join_date DATE,
    status VARCHAR DEFAULT 'active',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 6. Buat tabel relasi jika belum ada
CREATE TABLE IF NOT EXISTS class_students (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    class_id UUID NOT NULL,
    student_id UUID NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(class_id, student_id)
);

CREATE TABLE IF NOT EXISTS student_parents (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    student_id UUID NOT NULL,
    parent_id UUID NOT NULL,
    relationship VARCHAR NOT NULL,
    is_primary BOOLEAN DEFAULT false,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(student_id, parent_id)
);

-- 7. Buat trigger untuk updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_parents_updated_at
    BEFORE UPDATE ON parents
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_students_updated_at
    BEFORE UPDATE ON students
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_teachers_updated_at
    BEFORE UPDATE ON teachers
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 8. Restore data jika ada
INSERT INTO parents SELECT * FROM parents_backup WHERE EXISTS (SELECT 1 FROM parents_backup);
INSERT INTO students SELECT * FROM students_backup WHERE EXISTS (SELECT 1 FROM students_backup);
INSERT INTO teachers SELECT * FROM teachers_backup WHERE EXISTS (SELECT 1 FROM teachers_backup);

-- 9. Drop backup tables
DROP TABLE IF EXISTS parents_backup;
DROP TABLE IF EXISTS students_backup;
DROP TABLE IF EXISTS teachers_backup;

-- 10. Pesan konfirmasi
SELECT 'Tables recreated without foreign key constraints for development!' as status;
