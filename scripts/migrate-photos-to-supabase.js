// Script untuk migrasi foto existing ke Supabase Storage
// Jalankan dengan: node scripts/migrate-photos-to-supabase.js

const { createClient } = require('@supabase/supabase-js')
const fs = require('fs')
const path = require('path')

// Supabase config
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Supabase environment variables not found')
  console.log('Make sure NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY are set')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseKey)

// Helper function untuk HTTP request
async function makeRequest(url, options = {}) {
  try {
    const response = await fetch(url, options)
    const data = await response.json()
    return { success: response.ok, data, status: response.status }
  } catch (error) {
    return { success: false, error: error.message, status: 0 }
  }
}

// Get all teachers with photo_url
async function getTeachersWithPhotos() {
  console.log('\n=== Getting Teachers with Photos ===')
  
  const result = await makeRequest('http://localhost:3001/api/teachers')
  
  if (result.success && result.data.teachers) {
    const teachersWithPhotos = result.data.teachers.filter(teacher => 
      teacher.photo_url && 
      teacher.photo_url.trim() !== '' &&
      !teacher.photo_url.includes('supabase') // Skip already migrated
    )
    
    console.log(`Found ${teachersWithPhotos.length} teachers with photos to migrate`)
    return teachersWithPhotos
  }
  
  console.log('❌ Failed to get teachers')
  return []
}

// Get all students with photo_url
async function getStudentsWithPhotos() {
  console.log('\n=== Getting Students with Photos ===')
  
  const result = await makeRequest('http://localhost:3001/api/students')
  
  if (result.success && result.data.students) {
    const studentsWithPhotos = result.data.students.filter(student => 
      student.photo_url && 
      student.photo_url.trim() !== '' &&
      !student.photo_url.includes('supabase') // Skip already migrated
    )
    
    console.log(`Found ${studentsWithPhotos.length} students with photos to migrate`)
    return studentsWithPhotos
  }
  
  console.log('❌ Failed to get students')
  return []
}

// Upload file to Supabase Storage
async function uploadToSupabase(filePath, fileName, category) {
  try {
    // Check if local file exists
    const fullPath = path.join(process.cwd(), 'public', filePath)
    
    if (!fs.existsSync(fullPath)) {
      console.log(`⚠️  File not found: ${fullPath}`)
      return null
    }
    
    // Read file
    const fileBuffer = fs.readFileSync(fullPath)
    
    // Determine content type
    const extension = path.extname(fileName).toLowerCase()
    let contentType = 'image/jpeg'
    
    switch (extension) {
      case '.png': contentType = 'image/png'; break
      case '.gif': contentType = 'image/gif'; break
      case '.webp': contentType = 'image/webp'; break
      default: contentType = 'image/jpeg'
    }
    
    // Upload to Supabase Storage
    const storagePath = `${category}/${fileName}`
    
    const { data, error } = await supabase.storage
      .from('photos')
      .upload(storagePath, fileBuffer, {
        contentType,
        upsert: true
      })
    
    if (error) {
      console.log(`❌ Upload failed for ${fileName}:`, error.message)
      return null
    }
    
    // Get public URL
    const { data: urlData } = supabase.storage
      .from('photos')
      .getPublicUrl(storagePath)
    
    console.log(`✅ Uploaded: ${fileName} -> ${urlData.publicUrl}`)
    return urlData.publicUrl
    
  } catch (error) {
    console.log(`❌ Error uploading ${fileName}:`, error.message)
    return null
  }
}

// Update teacher photo URL
async function updateTeacherPhoto(teacherId, newPhotoUrl) {
  try {
    const response = await fetch('http://localhost:3001/api/teachers', {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        id: teacherId,
        photo_url: newPhotoUrl
      })
    })
    
    if (response.ok) {
      console.log(`✅ Updated teacher ${teacherId} photo URL`)
      return true
    } else {
      console.log(`❌ Failed to update teacher ${teacherId}`)
      return false
    }
  } catch (error) {
    console.log(`❌ Error updating teacher ${teacherId}:`, error.message)
    return false
  }
}

// Update student photo URL
async function updateStudentPhoto(studentId, newPhotoUrl) {
  try {
    const response = await fetch('http://localhost:3001/api/students', {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        id: studentId,
        photo_url: newPhotoUrl
      })
    })
    
    if (response.ok) {
      console.log(`✅ Updated student ${studentId} photo URL`)
      return true
    } else {
      console.log(`❌ Failed to update student ${studentId}`)
      return false
    }
  } catch (error) {
    console.log(`❌ Error updating student ${studentId}:`, error.message)
    return false
  }
}

// Migrate teachers photos
async function migrateTeachersPhotos() {
  console.log('\n=== Migrating Teachers Photos ===')
  
  const teachers = await getTeachersWithPhotos()
  let migrated = 0
  
  for (const teacher of teachers) {
    console.log(`\nMigrating: ${teacher.name} (${teacher.photo_url})`)
    
    // Generate filename
    const fileName = `teacher_${teacher.id}_${Date.now()}.jpg`
    
    // Upload to Supabase
    const newUrl = await uploadToSupabase(teacher.photo_url, fileName, 'teachers')
    
    if (newUrl) {
      // Update database
      const updated = await updateTeacherPhoto(teacher.id, newUrl)
      if (updated) {
        migrated++
      }
    }
  }
  
  console.log(`\n✅ Migrated ${migrated}/${teachers.length} teacher photos`)
}

// Migrate students photos
async function migrateStudentsPhotos() {
  console.log('\n=== Migrating Students Photos ===')
  
  const students = await getStudentsWithPhotos()
  let migrated = 0
  
  for (const student of students) {
    console.log(`\nMigrating: ${student.name} (${student.photo_url})`)
    
    // Generate filename
    const fileName = `student_${student.id}_${Date.now()}.jpg`
    
    // Upload to Supabase
    const newUrl = await uploadToSupabase(student.photo_url, fileName, 'students')
    
    if (newUrl) {
      // Update database
      const updated = await updateStudentPhoto(student.id, newUrl)
      if (updated) {
        migrated++
      }
    }
  }
  
  console.log(`\n✅ Migrated ${migrated}/${students.length} student photos`)
}

// Main migration function
async function runPhotoMigration() {
  console.log('🚀 Starting Photo Migration to Supabase Storage...')
  console.log('Make sure:')
  console.log('1. Supabase Storage bucket "photos" exists')
  console.log('2. Storage policies are set up')
  console.log('3. Development server is running')
  console.log('')
  
  try {
    // Test Supabase connection
    const { data, error } = await supabase.storage.listBuckets()
    
    if (error) {
      console.error('❌ Supabase connection failed:', error.message)
      return
    }
    
    const photosBucket = data.find(bucket => bucket.name === 'photos')
    if (!photosBucket) {
      console.error('❌ Photos bucket not found. Please create it in Supabase Dashboard.')
      return
    }
    
    console.log('✅ Supabase Storage connection OK')
    
    // Migrate teachers
    await migrateTeachersPhotos()
    
    // Migrate students
    await migrateStudentsPhotos()
    
    console.log('\n🎉 Photo migration completed!')
    console.log('\n📋 Next steps:')
    console.log('1. Test photo display in frontend')
    console.log('2. Test new photo uploads')
    console.log('3. Clean up old photo files if needed')
    
  } catch (error) {
    console.error('❌ Migration failed:', error.message)
  }
}

// Run migration if this script is executed directly
if (require.main === module) {
  runPhotoMigration()
}

module.exports = { 
  runPhotoMigration,
  migrateTeachersPhotos,
  migrateStudentsPhotos
}
