-- <PERSON><PERSON>t untuk membuat tabel website_images jika belum ada
-- Jalankan script ini di Supabase SQL Editor

-- 1. Buat tabel website_images
CREATE TABLE IF NOT EXISTS website_images (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    key VARCHAR(255) UNIQUE NOT NULL,
    filename VA<PERSON>HAR(255) NOT NULL,
    original_name VARCHAR(255) NOT NULL,
    file_path TEXT NOT NULL,
    file_size INTEGER NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    alt_text TEXT,
    caption TEXT,
    category VARCHAR(100) DEFAULT 'general',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 2. Buat indexes untuk performa
CREATE INDEX IF NOT EXISTS idx_website_images_key ON website_images(key);
CREATE INDEX IF NOT EXISTS idx_website_images_category ON website_images(category);
CREATE INDEX IF NOT EXISTS idx_website_images_created_at ON website_images(created_at);

-- 3. <PERSON><PERSON><PERSON> trigger untuk updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_website_images_updated_at
    BEFORE UPDATE ON website_images
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 4. Set up Row Level Security (RLS)
ALTER TABLE website_images ENABLE ROW LEVEL SECURITY;

-- 5. Buat policies untuk RLS
-- Admin policies (full access)
CREATE POLICY website_images_admin_all ON website_images
    FOR ALL TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.role = 'admin'
        )
    );

-- Teacher policies (can upload and manage their own images)
CREATE POLICY website_images_teacher_all ON website_images
    FOR ALL TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM teachers 
            WHERE teachers.profile_id = auth.uid()
        )
    );

-- Public read access for serving images
CREATE POLICY website_images_public_read ON website_images
    FOR SELECT TO anon
    USING (true);

-- 6. Buat direktori uploads jika belum ada
-- Note: Ini harus dilakukan di server, bukan di database
-- mkdir -p public/uploads/students
-- mkdir -p public/uploads/teachers
-- mkdir -p public/uploads/general

-- 7. Verifikasi tabel telah dibuat
SELECT 
    'website_images' as table_name,
    COUNT(*) as total_records
FROM website_images;

-- 8. Periksa struktur tabel
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'website_images' 
ORDER BY ordinal_position;

SELECT 'Website Images table setup completed!' as status;
