#!/bin/bash

echo "=== Checking for remaining user_id references in parents-related files ==="
echo ""

echo "1. Checking app/dashboard/parents/ directory:"
find app/dashboard/parents/ -name "*.tsx" -o -name "*.ts" | xargs grep -n "user_id" || echo "   ✅ No user_id found"
echo ""

echo "2. Checking components/parents/ directory:"
find components/parents/ -name "*.tsx" -o -name "*.ts" | xargs grep -n "user_id" || echo "   ✅ No user_id found"
echo ""

echo "3. Checking API endpoints for parents:"
find app/api/ -name "*.ts" | xargs grep -l "parents" | xargs grep -n "user_id" || echo "   ✅ No user_id found in parents APIs"
echo ""

echo "4. Checking lib/supabase-crud.ts for parents functions:"
grep -n -A5 -B5 "parents.*user_id\|user_id.*parents" lib/supabase-crud.ts || echo "   ✅ No user_id found in parents functions"
echo ""

echo "5. Checking for any remaining .from('parents').select() with user_id:"
find . -name "*.ts" -o -name "*.tsx" | xargs grep -l "from('parents')" | xargs grep -n "user_id" || echo "   ✅ No user_id found in parents queries"
echo ""

echo "=== Summary ==="
echo "If all checks show ✅, then all user_id references have been successfully replaced with profile_id"
