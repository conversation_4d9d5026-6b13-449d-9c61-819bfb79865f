-- <PERSON>ript untuk menyatukan sistem auth menjadi satu
-- Menggunakan auth.users → profiles → teachers/parents

-- 1. Pastikan tabel profiles sudah benar
CREATE TABLE IF NOT EXISTS profiles (
    id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    email TEXT NOT NULL,
    phone TEXT,
    name TEXT NOT NULL,
    role TEXT NOT NULL CHECK (role IN ('admin', 'teacher', 'parent')),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 2. Backup data existing (jika ada)
CREATE TABLE IF NOT EXISTS teachers_backup AS SELECT * FROM teachers;
CREATE TABLE IF NOT EXISTS parents_backup AS SELECT * FROM parents;

-- 3. Migrate existing data dan update struktur teachers table
-- Pertama, buat profiles untuk semua user_id yang ada di teachers tapi belum ada di profiles
INSERT INTO profiles (id, email, name, role)
SELECT DISTINCT
    t.user_id,
    COALESCE(t.email, t.user_id::text || '@temp.com'),
    t.name,
    'teacher'
FROM teachers t
LEFT JOIN profiles p ON p.id = t.user_id
WHERE p.id IS NULL
ON CONFLICT (id) DO NOTHING;

-- Ganti user_id menjadi profile_id
ALTER TABLE teachers DROP CONSTRAINT IF EXISTS teachers_user_id_fkey;
ALTER TABLE teachers RENAME COLUMN user_id TO profile_id;
ALTER TABLE teachers ADD CONSTRAINT teachers_profile_id_fkey
    FOREIGN KEY (profile_id) REFERENCES profiles(id) ON DELETE CASCADE;

-- 4. Migrate existing data dan update struktur parents table
-- Pertama, buat profiles untuk semua user_id yang ada di parents tapi belum ada di profiles
INSERT INTO profiles (id, email, name, role)
SELECT DISTINCT
    p.user_id,
    COALESCE(p.email, p.user_id::text || '@temp.com'),
    p.name,
    'parent'
FROM parents p
LEFT JOIN profiles pr ON pr.id = p.user_id
WHERE pr.id IS NULL
ON CONFLICT (id) DO NOTHING;

-- Ganti user_id menjadi profile_id
ALTER TABLE parents DROP CONSTRAINT IF EXISTS parents_user_id_fkey;
ALTER TABLE parents RENAME COLUMN user_id TO profile_id;
ALTER TABLE parents ADD CONSTRAINT parents_profile_id_fkey
    FOREIGN KEY (profile_id) REFERENCES profiles(id) ON DELETE CASCADE;

-- 5. Hapus tabel users custom (jika tidak digunakan lagi)
-- HATI-HATI: Pastikan tidak ada data penting di tabel users
-- DROP TABLE IF EXISTS users CASCADE;

-- 6. Update RLS policies untuk menggunakan profiles
-- Drop policies lama
DROP POLICY IF EXISTS subjects_teacher_read ON subjects;
DROP POLICY IF EXISTS subject_objectives_teacher_read ON subject_objectives;
DROP POLICY IF EXISTS subject_teachers_teacher_read ON subject_teachers;

-- Buat policies baru
CREATE POLICY subjects_teacher_read ON subjects
  FOR SELECT TO authenticated
  USING (auth.uid() IN (SELECT profile_id FROM teachers));

CREATE POLICY subject_objectives_teacher_read ON subject_objectives
  FOR SELECT TO authenticated
  USING (auth.uid() IN (SELECT profile_id FROM teachers));

CREATE POLICY subject_teachers_teacher_read ON subject_teachers
  FOR SELECT TO authenticated
  USING (auth.uid() IN (SELECT profile_id FROM teachers));

-- 7. Buat function untuk auto-create profile saat user register
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS trigger AS $$
BEGIN
  INSERT INTO public.profiles (id, email, name, role)
  VALUES (
    new.id,
    new.email,
    COALESCE(new.raw_user_meta_data->>'full_name', new.email),
    COALESCE(new.raw_user_meta_data->>'role', 'parent')
  );
  RETURN new;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 8. Buat trigger untuk auto-create profile
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE PROCEDURE public.handle_new_user();

-- 9. Enable RLS pada profiles
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- 10. Buat policies untuk profiles
CREATE POLICY "Users can view own profile" ON profiles
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON profiles
  FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Admins can view all profiles" ON profiles
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- 11. Sample data untuk testing
-- Buat admin user (ganti dengan email yang sesuai)
-- INSERT INTO profiles (id, email, name, role) VALUES
-- ('your-admin-uuid-here', '<EMAIL>', 'Administrator', 'admin')
-- ON CONFLICT (id) DO UPDATE SET role = 'admin';

COMMIT;
