#!/bin/bash

# Script untuk memperbaiki masalah photo upload
# <PERSON><PERSON><PERSON> dengan: chmod +x scripts/fix-photo-upload.sh && ./scripts/fix-photo-upload.sh

echo "🔧 Fixing Photo Upload Issues..."

# 1. Create upload directories
echo "📁 Creating upload directories..."
mkdir -p public/uploads/students
mkdir -p public/uploads/teachers
mkdir -p public/uploads/general

# Set permissions (if on Unix-like system)
if [[ "$OSTYPE" == "linux-gnu"* ]] || [[ "$OSTYPE" == "darwin"* ]]; then
    chmod 755 public/uploads
    chmod 755 public/uploads/students
    chmod 755 public/uploads/teachers
    chmod 755 public/uploads/general
    echo "✅ Upload directories created with proper permissions"
else
    echo "✅ Upload directories created"
fi

# 2. Check if .gitkeep files exist (to keep empty directories in git)
echo "📝 Adding .gitkeep files..."
touch public/uploads/students/.gitkeep
touch public/uploads/teachers/.gitkeep
touch public/uploads/general/.gitkeep

# 3. Create a test image for testing
echo "🖼️ Creating test image..."
cat > public/uploads/test-image.txt << 'EOF'
This is a test file to verify upload directory is working.
You can delete this file after testing.
EOF

echo "✅ Photo upload fix completed!"
echo ""
echo "📋 Next steps:"
echo "1. Run: node scripts/test-photo-upload.js"
echo "2. In Supabase SQL Editor, run: scripts/create-website-images-table.sql"
echo "3. Test upload in browser forms"
echo "4. Check browser console for any remaining errors"
echo ""
echo "🔍 If still having issues:"
echo "- Check server logs with: npm run dev"
echo "- Verify authentication (user must be logged in)"
echo "- Check RLS policies in Supabase dashboard"
