-- <PERSON><PERSON>t untuk membuat tabel achievements
-- Jalankan script ini di Supabase SQL Editor

-- 1. Buat tabel achievements
CREATE TABLE IF NOT EXISTS achievements (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    student_id UUID NOT NULL,
    subject_id UUID NOT NULL,
    value TEXT NOT NULL,
    grade VARCHAR(10),
    notes TEXT,
    verified_by UUI<PERSON>,
    achievement_date DATE NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 2. Buat indexes untuk performa yang lebih baik
CREATE INDEX IF NOT EXISTS idx_achievements_student_id ON achievements(student_id);
CREATE INDEX IF NOT EXISTS idx_achievements_subject_id ON achievements(subject_id);
CREATE INDEX IF NOT EXISTS idx_achievements_verified_by ON achievements(verified_by);
CREATE INDEX IF NOT EXISTS idx_achievements_achievement_date ON achievements(achievement_date);

-- 3. <PERSON>uat trigger untuk updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_achievements_updated_at
    BEFORE UPDATE ON achievements
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 4. Set up Row Level Security (RLS)
ALTER TABLE achievements ENABLE ROW LEVEL SECURITY;

-- 5. Buat policies untuk RLS
-- Admin policies (full access)
CREATE POLICY achievements_admin_all ON achievements
    FOR ALL TO authenticated
    USING (
        auth.uid() IN (
            SELECT id FROM profiles WHERE role = 'admin'
        )
    );

-- Teacher policies (can read all, can insert/update their own verifications)
CREATE POLICY achievements_teacher_read ON achievements
    FOR SELECT TO authenticated
    USING (
        auth.uid() IN (
            SELECT profile_id FROM teachers
        )
    );

CREATE POLICY achievements_teacher_insert ON achievements
    FOR INSERT TO authenticated
    WITH CHECK (
        auth.uid() IN (
            SELECT profile_id FROM teachers WHERE id = verified_by
        )
    );

CREATE POLICY achievements_teacher_update ON achievements
    FOR UPDATE TO authenticated
    USING (
        auth.uid() IN (
            SELECT profile_id FROM teachers WHERE id = verified_by
        )
    );

-- Parent policies (can only read their children's achievements)
CREATE POLICY achievements_parent_read ON achievements
    FOR SELECT TO authenticated
    USING (
        auth.uid() IN (
            SELECT p.profile_id 
            FROM parents p
            JOIN student_parent sp ON p.id = sp.parent_id
            WHERE sp.student_id = achievements.student_id
        )
    );

-- 6. Insert sample data untuk testing
INSERT INTO achievements (student_id, subject_id, value, grade, notes, verified_by, achievement_date)
SELECT 
    s.id as student_id,
    sub.id as subject_id,
    'Hafal Juz 30' as value,
    'A' as grade,
    'Sangat baik dalam menghafal' as notes,
    t.id as verified_by,
    CURRENT_DATE as achievement_date
FROM students s
CROSS JOIN subjects sub
CROSS JOIN teachers t
WHERE s.name LIKE '%Test%' 
AND sub.name = 'Al-Quran'
AND t.name LIKE '%Test%'
LIMIT 1
ON CONFLICT DO NOTHING;

-- 7. Verifikasi tabel telah dibuat
SELECT 
    'achievements' as table_name,
    COUNT(*) as total_records
FROM achievements;

-- 8. Periksa struktur tabel
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'achievements' 
ORDER BY ordinal_position;
