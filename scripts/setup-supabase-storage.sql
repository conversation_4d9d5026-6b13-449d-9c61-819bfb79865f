-- Script untuk setup Supabase Storage bucket untuk photos
-- Jalankan script ini di Supabase SQL Editor

-- 1. Create storage bucket (if not exists)
-- Note: Bucket creation biasanya dilakukan via Supabase Dashboard
-- Tapi bisa juga via SQL jika diperlukan

-- 2. Set up storage policies untuk bucket 'photos'

-- Policy untuk authenticated users bisa upload
CREATE POLICY "Authenticated users can upload photos" ON storage.objects
  FOR INSERT TO authenticated
  WITH CHECK (bucket_id = 'photos');

-- Policy untuk authenticated users bisa update photos mereka
CREATE POLICY "Users can update their own photos" ON storage.objects
  FOR UPDATE TO authenticated
  USING (bucket_id = 'photos');

-- Policy untuk authenticated users bisa delete photos mereka  
CREATE POLICY "Users can delete their own photos" ON storage.objects
  FOR DELETE TO authenticated
  USING (bucket_id = 'photos');

-- Policy untuk public read access (agar foto bisa ditampilkan)
CREATE POLICY "Public can view photos" ON storage.objects
  FOR SELECT TO public
  USING (bucket_id = 'photos');

-- 3. Update website_images table untuk include storage_path
ALTER TABLE website_images 
ADD COLUMN IF NOT EXISTS storage_path TEXT;

-- 4. Create index untuk storage_path
CREATE INDEX IF NOT EXISTS idx_website_images_storage_path 
ON website_images(storage_path);

-- 5. Update existing records to use Supabase Storage URLs
-- Note: Ini hanya contoh, sesuaikan dengan data yang ada
/*
UPDATE website_images 
SET storage_path = CASE 
  WHEN file_path LIKE '/photos/%' THEN REPLACE(file_path, '/photos/', '')
  WHEN file_path LIKE '/uploads/%' THEN REPLACE(file_path, '/uploads/', '')
  ELSE file_path
END
WHERE storage_path IS NULL;
*/

-- 6. Verify setup
SELECT 'Storage setup completed!' as status;

-- 7. Check existing images
SELECT 
  key,
  filename,
  file_path,
  storage_path,
  category,
  created_at
FROM website_images 
ORDER BY created_at DESC 
LIMIT 5;

-- 8. Instructions for manual setup
SELECT 'Manual setup required:' as note;
SELECT '1. Go to Supabase Dashboard > Storage' as step_1;
SELECT '2. Create new bucket named "photos"' as step_2;
SELECT '3. Set bucket to Public' as step_3;
SELECT '4. Run this SQL script' as step_4;
SELECT '5. Update ImageUpload component to use /api/upload-supabase' as step_5;
