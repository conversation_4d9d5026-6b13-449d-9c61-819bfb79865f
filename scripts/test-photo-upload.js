// Script untuk testing photo upload functionality
// Jalankan dengan: node scripts/test-photo-upload.js

const BASE_URL = 'http://localhost:3001'
const fs = require('fs')
const path = require('path')

// Helper function untuk HTTP request
async function makeRequest(url, options = {}) {
  try {
    const response = await fetch(url, options)
    const data = await response.json()
    
    return { 
      success: response.ok, 
      data, 
      status: response.status,
      statusText: response.statusText 
    }
  } catch (error) {
    return { 
      success: false, 
      error: error.message,
      status: 0,
      statusText: 'Network Error'
    }
  }
}

// Create a test image file
function createTestImage() {
  // Create a simple 1x1 pixel PNG image in base64
  const pngData = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAI9jU77zgAAAABJRU5ErkJggg=='
  const buffer = Buffer.from(pngData, 'base64')
  
  // Create a File-like object
  const blob = new Blob([buffer], { type: 'image/png' })
  
  // Create FormData
  const formData = new FormData()
  formData.append('file', blob, 'test-image.png')
  
  return formData
}

// Test upload API directly
async function testUploadAPI() {
  console.log('\n=== Testing Upload API Directly ===')
  
  try {
    // Create test image
    const formData = createTestImage()
    formData.append('key', 'test_upload_' + Date.now())
    formData.append('category', 'students')
    formData.append('altText', 'Test image')
    formData.append('caption', 'Test caption')
    
    const result = await fetch(`${BASE_URL}/api/upload`, {
      method: 'POST',
      body: formData
    })
    
    const data = await result.json()
    
    if (result.ok) {
      console.log('✅ Upload API: Success')
      console.log('Response:', data)
      return data
    } else {
      console.log('❌ Upload API: Failed')
      console.log('Error:', data.error)
      return null
    }
  } catch (error) {
    console.log('❌ Upload API: Error')
    console.log('Error:', error.message)
    return null
  }
}

// Test website_images table
async function testWebsiteImagesTable() {
  console.log('\n=== Testing Website Images Table ===')
  
  // This would need to be done via Supabase client or SQL
  console.log('Please check in Supabase SQL Editor:')
  console.log('SELECT * FROM website_images ORDER BY created_at DESC LIMIT 5;')
  console.log('')
  console.log('If table does not exist, run:')
  console.log('\\i scripts/create-website-images-table.sql')
}

// Test file system permissions
async function testFileSystemPermissions() {
  console.log('\n=== Testing File System Permissions ===')
  
  const uploadsDir = path.join(process.cwd(), 'public', 'uploads')
  const studentsDir = path.join(uploadsDir, 'students')
  const teachersDir = path.join(uploadsDir, 'teachers')
  
  try {
    // Check if directories exist
    if (!fs.existsSync(uploadsDir)) {
      console.log('❌ uploads directory does not exist')
      console.log('Creating uploads directory...')
      fs.mkdirSync(uploadsDir, { recursive: true })
    } else {
      console.log('✅ uploads directory exists')
    }
    
    if (!fs.existsSync(studentsDir)) {
      console.log('❌ students directory does not exist')
      console.log('Creating students directory...')
      fs.mkdirSync(studentsDir, { recursive: true })
    } else {
      console.log('✅ students directory exists')
    }
    
    if (!fs.existsSync(teachersDir)) {
      console.log('❌ teachers directory does not exist')
      console.log('Creating teachers directory...')
      fs.mkdirSync(teachersDir, { recursive: true })
    } else {
      console.log('✅ teachers directory exists')
    }
    
    // Test write permissions
    const testFile = path.join(uploadsDir, 'test-write.txt')
    fs.writeFileSync(testFile, 'test')
    fs.unlinkSync(testFile)
    console.log('✅ Write permissions OK')
    
  } catch (error) {
    console.log('❌ File system error:', error.message)
  }
}

// Test image serving
async function testImageServing(imageData) {
  console.log('\n=== Testing Image Serving ===')
  
  if (!imageData || !imageData.url) {
    console.log('❌ No image URL to test')
    return
  }
  
  try {
    const response = await fetch(`${BASE_URL}${imageData.url}`)
    
    if (response.ok) {
      console.log('✅ Image serving: Success')
      console.log(`Image URL: ${BASE_URL}${imageData.url}`)
      console.log(`Content-Type: ${response.headers.get('content-type')}`)
    } else {
      console.log('❌ Image serving: Failed')
      console.log(`Status: ${response.status} ${response.statusText}`)
    }
  } catch (error) {
    console.log('❌ Image serving: Error')
    console.log('Error:', error.message)
  }
}

// Test form integration
async function testFormIntegration() {
  console.log('\n=== Testing Form Integration ===')
  
  console.log('Manual testing required:')
  console.log('1. Open browser and navigate to:')
  console.log('   - /dashboard/students/new (for student form)')
  console.log('   - /dashboard/teachers/new (for teacher form)')
  console.log('2. Try uploading an image')
  console.log('3. Check browser console for logs')
  console.log('4. Verify image appears in preview')
  console.log('5. Submit form and check if photo_url is saved')
}

// Debug upload issues
async function debugUploadIssues() {
  console.log('\n=== Debug Upload Issues ===')
  
  console.log('Common issues and solutions:')
  console.log('')
  console.log('1. "No file uploaded" error:')
  console.log('   - Check if file input is working')
  console.log('   - Verify FormData is created correctly')
  console.log('')
  console.log('2. "Database error" in upload:')
  console.log('   - Run: scripts/create-website-images-table.sql')
  console.log('   - Check RLS policies in Supabase')
  console.log('')
  console.log('3. "File not found" when serving:')
  console.log('   - Check if public/uploads directories exist')
  console.log('   - Verify file was actually saved')
  console.log('')
  console.log('4. Image not showing in form:')
  console.log('   - Check handleImageUpload function')
  console.log('   - Verify form.setValue is called')
  console.log('   - Check console logs for errors')
  console.log('')
  console.log('5. Form not saving photo_url:')
  console.log('   - Check API payload includes photo_url')
  console.log('   - Verify database column exists')
  console.log('   - Check API response')
}

// Main test function
async function runPhotoUploadTests() {
  console.log('🚀 Starting Photo Upload Tests...')
  console.log('Make sure your development server is running on http://localhost:3001')
  
  try {
    // Test file system
    await testFileSystemPermissions()
    
    // Test upload API
    const uploadResult = await testUploadAPI()
    
    // Test image serving
    if (uploadResult) {
      await testImageServing(uploadResult)
    }
    
    // Test database table
    await testWebsiteImagesTable()
    
    // Test form integration
    await testFormIntegration()
    
    // Debug guide
    await debugUploadIssues()
    
    console.log('\n🎉 Photo Upload tests completed!')
    
    console.log('\n📋 Next Steps:')
    console.log('1. If website_images table missing: run scripts/create-website-images-table.sql')
    console.log('2. Test upload manually in browser forms')
    console.log('3. Check browser console for detailed error messages')
    console.log('4. Verify uploaded files appear in public/uploads/')
    
  } catch (error) {
    console.error('❌ Test execution failed:', error.message)
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  runPhotoUploadTests()
}

module.exports = { 
  runPhotoUploadTests, 
  testUploadAPI,
  testFileSystemPermissions,
  testImageServing
}
