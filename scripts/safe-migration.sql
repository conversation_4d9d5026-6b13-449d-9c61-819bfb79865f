-- Script migrasi yang aman untuk menangani data existing
-- Jalankan script ini untuk memperbaiki masalah foreign key

BEGIN;

-- 1. Pastikan tabel profiles ada dengan struktur yang benar
CREATE TABLE IF NOT EXISTS profiles (
    id UUID PRIMARY KEY,
    user_id UUID NOT NULL,
    full_name TEXT NOT NULL,
    avatar_url TEXT,
    role TEXT NOT NULL CHECK (role IN ('admin', 'teacher', 'parent')),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 2. Backup data existing
CREATE TABLE IF NOT EXISTS teachers_backup AS SELECT * FROM teachers;
CREATE TABLE IF NOT EXISTS parents_backup AS SELECT * FROM parents;

-- 3. Per<PERSON><PERSON> dan tampilkan data yang bermasalah
DO $$
DECLARE
    missing_teachers INTEGER;
    missing_parents INTEGER;
BEGIN
    SELECT COUNT(*) INTO missing_teachers
    FROM teachers t
    LEFT JOIN profiles p ON p.id = t.user_id
    WHERE p.id IS NULL;
    
    SELECT COUNT(*) INTO missing_parents
    FROM parents pa
    LEFT JOIN profiles p ON p.id = pa.user_id
    WHERE p.id IS NULL;
    
    RAISE NOTICE 'Teachers without profiles: %', missing_teachers;
    RAISE NOTICE 'Parents without profiles: %', missing_parents;
END $$;

-- 4. Opsi A: Buat profiles untuk data yang hilang (RECOMMENDED)
-- Ini akan membuat profile dengan ID yang sama dengan user_id di teachers/parents
INSERT INTO profiles (id, user_id, full_name, role, created_at, updated_at)
SELECT DISTINCT
    t.user_id,
    t.user_id,
    t.name,
    'teacher',
    NOW(),
    NOW()
FROM teachers t
LEFT JOIN profiles p ON p.id = t.user_id
WHERE p.id IS NULL AND t.user_id IS NOT NULL
ON CONFLICT (id) DO UPDATE SET
    full_name = EXCLUDED.full_name,
    role = 'teacher',
    updated_at = NOW();

INSERT INTO profiles (id, user_id, full_name, role, created_at, updated_at)
SELECT DISTINCT
    pa.user_id,
    pa.user_id,
    pa.name,
    'parent',
    NOW(),
    NOW()
FROM parents pa
LEFT JOIN profiles p ON p.id = pa.user_id
WHERE p.id IS NULL AND pa.user_id IS NOT NULL
ON CONFLICT (id) DO UPDATE SET
    full_name = EXCLUDED.full_name,
    role = 'parent',
    updated_at = NOW();

-- 5. Sekarang aman untuk mengubah struktur tabel
-- Update teachers table
ALTER TABLE teachers DROP CONSTRAINT IF EXISTS teachers_user_id_fkey;

-- Jika kolom user_id sudah ada, rename ke profile_id
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'teachers' AND column_name = 'user_id') THEN
        ALTER TABLE teachers RENAME COLUMN user_id TO profile_id;
    END IF;
END $$;

-- Tambahkan constraint
ALTER TABLE teachers ADD CONSTRAINT teachers_profile_id_fkey 
    FOREIGN KEY (profile_id) REFERENCES profiles(id) ON DELETE CASCADE;

-- Update parents table
ALTER TABLE parents DROP CONSTRAINT IF EXISTS parents_user_id_fkey;

-- Jika kolom user_id sudah ada, rename ke profile_id
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'parents' AND column_name = 'user_id') THEN
        ALTER TABLE parents RENAME COLUMN user_id TO profile_id;
    END IF;
END $$;

-- Tambahkan constraint
ALTER TABLE parents ADD CONSTRAINT parents_profile_id_fkey 
    FOREIGN KEY (profile_id) REFERENCES profiles(id) ON DELETE CASCADE;

-- 6. Update RLS policies
DROP POLICY IF EXISTS subjects_teacher_read ON subjects;
DROP POLICY IF EXISTS subject_objectives_teacher_read ON subject_objectives;
DROP POLICY IF EXISTS subject_teachers_teacher_read ON subject_teachers;

-- Buat policies baru (jika tabel subjects ada)
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'subjects') THEN
        EXECUTE 'CREATE POLICY subjects_teacher_read ON subjects
                 FOR SELECT TO authenticated
                 USING (auth.uid() IN (SELECT profile_id FROM teachers))';
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'subject_objectives') THEN
        EXECUTE 'CREATE POLICY subject_objectives_teacher_read ON subject_objectives
                 FOR SELECT TO authenticated
                 USING (auth.uid() IN (SELECT profile_id FROM teachers))';
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'subject_teachers') THEN
        EXECUTE 'CREATE POLICY subject_teachers_teacher_read ON subject_teachers
                 FOR SELECT TO authenticated
                 USING (auth.uid() IN (SELECT profile_id FROM teachers))';
    END IF;
END $$;

-- 7. Enable RLS dan buat policies untuk profiles
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- Drop existing policies
DROP POLICY IF EXISTS "Users can view own profile" ON profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON profiles;
DROP POLICY IF EXISTS "Admins can view all profiles" ON profiles;

-- Create new policies
CREATE POLICY "Users can view own profile" ON profiles
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON profiles
  FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Admins can view all profiles" ON profiles
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- 8. Verifikasi hasil
DO $$
DECLARE
    teacher_count INTEGER;
    parent_count INTEGER;
    profile_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO teacher_count FROM teachers;
    SELECT COUNT(*) INTO parent_count FROM parents;
    SELECT COUNT(*) INTO profile_count FROM profiles;
    
    RAISE NOTICE 'Migration completed successfully!';
    RAISE NOTICE 'Teachers: %, Parents: %, Profiles: %', teacher_count, parent_count, profile_count;
END $$;

COMMIT;
