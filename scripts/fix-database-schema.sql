-- <PERSON><PERSON>t untuk memperbaiki database schema
-- Jalankan script ini di Supabase SQL Editor jika ada masalah dengan schema

-- 1. Pastikan tabel profiles ada
CREATE TABLE IF NOT EXISTS profiles (
    id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    email TEXT NOT NULL,
    phone TEXT,
    name TEXT NOT NULL,
    role TEXT NOT NULL CHECK (role IN ('admin', 'teacher', 'parent', 'student')),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 2. Tambahkan kolom profile_id ke tabel students jika belum ada
ALTER TABLE students ADD COLUMN IF NOT EXISTS profile_id UUID REFERENCES profiles(id);

-- 3. Tambahkan kolom profile_id ke tabel parents jika belum ada  
ALTER TABLE parents ADD COLUMN IF NOT EXISTS profile_id UUID REFERENCES profiles(id);

-- 4. Tambahkan kolom profile_id ke tabel teachers jika belum ada
ALTER TABLE teachers ADD COLUMN IF NOT EXISTS profile_id UUID REFERENCES profiles(id);

-- 5. Update trigger untuk updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 6. Tambahkan trigger untuk profiles
DROP TRIGGER IF EXISTS update_profiles_updated_at ON profiles;
CREATE TRIGGER update_profiles_updated_at
    BEFORE UPDATE ON profiles
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 7. Perbaiki RLS policies untuk menghindari infinite recursion
-- Hapus policy yang bermasalah terlebih dahulu
DROP POLICY IF EXISTS "Users can view own profile" ON profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON profiles;

-- Buat policy yang lebih sederhana
CREATE POLICY "Enable read access for authenticated users" ON profiles
    FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Enable insert for authenticated users" ON profiles
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY "Enable update for users based on id" ON profiles
    FOR UPDATE USING (auth.uid() = id);

-- 8. Policy untuk students
DROP POLICY IF EXISTS "Enable read access for all users" ON students;
CREATE POLICY "Enable read access for authenticated users" ON students
    FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Enable insert for authenticated users" ON students
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY "Enable update for authenticated users" ON students
    FOR UPDATE USING (auth.role() = 'authenticated');

-- 9. Policy untuk parents
DROP POLICY IF EXISTS "Enable read access for all users" ON parents;
CREATE POLICY "Enable read access for authenticated users" ON parents
    FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Enable insert for authenticated users" ON parents
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY "Enable update for authenticated users" ON parents
    FOR UPDATE USING (auth.role() = 'authenticated');

-- 10. Policy untuk teachers
DROP POLICY IF EXISTS "Enable read access for all users" ON teachers;
CREATE POLICY "Enable read access for authenticated users" ON teachers
    FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Enable insert for authenticated users" ON teachers
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY "Enable update for authenticated users" ON teachers
    FOR UPDATE USING (auth.role() = 'authenticated');

-- 11. Policy untuk classes
CREATE POLICY "Enable read access for authenticated users" ON classes
    FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Enable insert for authenticated users" ON classes
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY "Enable update for authenticated users" ON classes
    FOR UPDATE USING (auth.role() = 'authenticated');
