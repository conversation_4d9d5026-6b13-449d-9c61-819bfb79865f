-- <PERSON><PERSON>t untuk memperbaiki RLS policies untuk timeline tables
-- Agar admin dan teacher bisa insert/update timeline data

-- 1. Cek status RLS saat ini
SELECT schemaname, tablename, rowsecurity 
FROM pg_tables 
WHERE tablename IN ('timeline_entries', 'timeline_details', 'timeline_activities', 'behavior_records')
AND schemaname = 'public';

-- 2. Hapus policies lama yang mungkin bermasalah
DROP POLICY IF EXISTS "timeline_entries_admin_all" ON timeline_entries;
DROP POLICY IF EXISTS "timeline_entries_teacher_all" ON timeline_entries;
DROP POLICY IF EXISTS "timeline_details_admin_all" ON timeline_details;
DROP POLICY IF EXISTS "timeline_details_teacher_all" ON timeline_details;
DROP POLICY IF EXISTS "timeline_activities_admin_all" ON timeline_activities;
DROP POLICY IF EXISTS "timeline_activities_teacher_all" ON timeline_activities;
DROP POLICY IF EXISTS "behavior_records_admin_all" ON behavior_records;
DROP POLICY IF EXISTS "behavior_records_teacher_all" ON behavior_records;

-- 3. <PERSON>uat policies baru untuk timeline_entries
CREATE POLICY "timeline_entries_admin_all" ON timeline_entries
  FOR ALL TO authenticated
  USING (auth.uid() IN (SELECT id FROM profiles WHERE role = 'admin'))
  WITH CHECK (auth.uid() IN (SELECT id FROM profiles WHERE role = 'admin'));

CREATE POLICY "timeline_entries_teacher_all" ON timeline_entries
  FOR ALL TO authenticated
  USING (auth.uid() IN (SELECT profile_id FROM teachers))
  WITH CHECK (auth.uid() IN (SELECT profile_id FROM teachers));

-- 4. Buat policies baru untuk timeline_details
CREATE POLICY "timeline_details_admin_all" ON timeline_details
  FOR ALL TO authenticated
  USING (auth.uid() IN (SELECT id FROM profiles WHERE role = 'admin'))
  WITH CHECK (auth.uid() IN (SELECT id FROM profiles WHERE role = 'admin'));

CREATE POLICY "timeline_details_teacher_all" ON timeline_details
  FOR ALL TO authenticated
  USING (auth.uid() IN (SELECT profile_id FROM teachers))
  WITH CHECK (auth.uid() IN (SELECT profile_id FROM teachers));

-- 5. Buat policies baru untuk timeline_activities
CREATE POLICY "timeline_activities_admin_all" ON timeline_activities
  FOR ALL TO authenticated
  USING (auth.uid() IN (SELECT id FROM profiles WHERE role = 'admin'))
  WITH CHECK (auth.uid() IN (SELECT id FROM profiles WHERE role = 'admin'));

CREATE POLICY "timeline_activities_teacher_all" ON timeline_activities
  FOR ALL TO authenticated
  USING (auth.uid() IN (SELECT profile_id FROM teachers))
  WITH CHECK (auth.uid() IN (SELECT profile_id FROM teachers));

-- 6. Buat policies baru untuk behavior_records
CREATE POLICY "behavior_records_admin_all" ON behavior_records
  FOR ALL TO authenticated
  USING (auth.uid() IN (SELECT id FROM profiles WHERE role = 'admin'))
  WITH CHECK (auth.uid() IN (SELECT id FROM profiles WHERE role = 'admin'));

CREATE POLICY "behavior_records_teacher_all" ON behavior_records
  FOR ALL TO authenticated
  USING (auth.uid() IN (SELECT profile_id FROM teachers))
  WITH CHECK (auth.uid() IN (SELECT profile_id FROM teachers));

-- 7. Enable RLS jika belum aktif
ALTER TABLE timeline_entries ENABLE ROW LEVEL SECURITY;
ALTER TABLE timeline_details ENABLE ROW LEVEL SECURITY;
ALTER TABLE timeline_activities ENABLE ROW LEVEL SECURITY;
ALTER TABLE behavior_records ENABLE ROW LEVEL SECURITY;

-- 8. Verifikasi policies yang telah dibuat
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual, with_check
FROM pg_policies 
WHERE tablename IN ('timeline_entries', 'timeline_details', 'timeline_activities', 'behavior_records')
ORDER BY tablename, policyname;

-- 9. Test insert untuk memastikan policies bekerja
-- (Uncomment untuk test)
/*
-- Test sebagai admin
INSERT INTO timeline_details (timeline_id, subject_id, value, grade, notes) 
VALUES ('test-timeline-id', 'test-subject-id', 'test-value', 'test-grade', 'test-notes')
ON CONFLICT DO NOTHING;

-- Hapus test data
DELETE FROM timeline_details WHERE timeline_id = 'test-timeline-id';
*/

SELECT 'Timeline RLS policies updated successfully!' as status;
