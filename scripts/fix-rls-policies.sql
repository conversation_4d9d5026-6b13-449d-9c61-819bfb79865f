-- <PERSON><PERSON><PERSON> untuk memperba<PERSON> RLS policies yang menyebabkan infinite recursion
-- Jalankan script ini di Supabase SQL Editor

-- 1. Disable RLS sementara untuk semua tabel
ALTER TABLE profiles DISABLE ROW LEVEL SECURITY;
ALTER TABLE students DISABLE ROW LEVEL SECURITY;
ALTER TABLE parents DISABLE ROW LEVEL SECURITY;
ALTER TABLE teachers DISABLE ROW LEVEL SECURITY;
ALTER TABLE classes DISABLE ROW LEVEL SECURITY;
ALTER TABLE subjects DISABLE ROW LEVEL SECURITY;
ALTER TABLE achievements DISABLE ROW LEVEL SECURITY;
ALTER TABLE class_students DISABLE ROW LEVEL SECURITY;
ALTER TABLE student_parents DISABLE ROW LEVEL SECURITY;
ALTER TABLE settings DISABLE ROW LEVEL SECURITY;

-- 2. Drop semua policy yang bermasalah
DROP POLICY IF EXISTS "Users can view own profile" ON profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON profiles;
DROP POLICY IF EXISTS "Enable read access for authenticated users" ON profiles;
DROP POLICY IF EXISTS "Enable insert for authenticated users" ON profiles;
DROP POLICY IF EXISTS "Enable update for users based on id" ON profiles;

DROP POLICY IF EXISTS "Enable read access for all users" ON students;
DROP POLICY IF EXISTS "Enable read access for authenticated users" ON students;
DROP POLICY IF EXISTS "Enable insert for authenticated users" ON students;
DROP POLICY IF EXISTS "Enable update for authenticated users" ON students;

DROP POLICY IF EXISTS "Enable read access for all users" ON parents;
DROP POLICY IF EXISTS "Enable read access for authenticated users" ON parents;
DROP POLICY IF EXISTS "Enable insert for authenticated users" ON parents;
DROP POLICY IF EXISTS "Enable update for authenticated users" ON parents;

DROP POLICY IF EXISTS "Enable read access for all users" ON teachers;
DROP POLICY IF EXISTS "Enable read access for authenticated users" ON teachers;
DROP POLICY IF EXISTS "Enable insert for authenticated users" ON teachers;
DROP POLICY IF EXISTS "Enable update for authenticated users" ON teachers;

DROP POLICY IF EXISTS "Enable read access for authenticated users" ON classes;
DROP POLICY IF EXISTS "Enable insert for authenticated users" ON classes;
DROP POLICY IF EXISTS "Enable update for authenticated users" ON classes;

-- 3. Buat policy yang sederhana dan aman
-- Policy untuk profiles
CREATE POLICY "profiles_select_policy" ON profiles
    FOR SELECT USING (true);

CREATE POLICY "profiles_insert_policy" ON profiles
    FOR INSERT WITH CHECK (true);

CREATE POLICY "profiles_update_policy" ON profiles
    FOR UPDATE USING (true);

-- Policy untuk students
CREATE POLICY "students_select_policy" ON students
    FOR SELECT USING (true);

CREATE POLICY "students_insert_policy" ON students
    FOR INSERT WITH CHECK (true);

CREATE POLICY "students_update_policy" ON students
    FOR UPDATE USING (true);

-- Policy untuk parents
CREATE POLICY "parents_select_policy" ON parents
    FOR SELECT USING (true);

CREATE POLICY "parents_insert_policy" ON parents
    FOR INSERT WITH CHECK (true);

CREATE POLICY "parents_update_policy" ON parents
    FOR UPDATE USING (true);

-- Policy untuk teachers
CREATE POLICY "teachers_select_policy" ON teachers
    FOR SELECT USING (true);

CREATE POLICY "teachers_insert_policy" ON teachers
    FOR INSERT WITH CHECK (true);

CREATE POLICY "teachers_update_policy" ON teachers
    FOR UPDATE USING (true);

-- Policy untuk classes
CREATE POLICY "classes_select_policy" ON classes
    FOR SELECT USING (true);

CREATE POLICY "classes_insert_policy" ON classes
    FOR INSERT WITH CHECK (true);

CREATE POLICY "classes_update_policy" ON classes
    FOR UPDATE USING (true);

-- Policy untuk subjects
CREATE POLICY "subjects_select_policy" ON subjects
    FOR SELECT USING (true);

CREATE POLICY "subjects_insert_policy" ON subjects
    FOR INSERT WITH CHECK (true);

CREATE POLICY "subjects_update_policy" ON subjects
    FOR UPDATE USING (true);

-- Policy untuk achievements
CREATE POLICY "achievements_select_policy" ON achievements
    FOR SELECT USING (true);

CREATE POLICY "achievements_insert_policy" ON achievements
    FOR INSERT WITH CHECK (true);

CREATE POLICY "achievements_update_policy" ON achievements
    FOR UPDATE USING (true);

-- Policy untuk class_students
CREATE POLICY "class_students_select_policy" ON class_students
    FOR SELECT USING (true);

CREATE POLICY "class_students_insert_policy" ON class_students
    FOR INSERT WITH CHECK (true);

CREATE POLICY "class_students_update_policy" ON class_students
    FOR UPDATE USING (true);

-- Policy untuk student_parents
CREATE POLICY "student_parents_select_policy" ON student_parents
    FOR SELECT USING (true);

CREATE POLICY "student_parents_insert_policy" ON student_parents
    FOR INSERT WITH CHECK (true);

CREATE POLICY "student_parents_update_policy" ON student_parents
    FOR UPDATE USING (true);

-- Policy untuk settings
CREATE POLICY "settings_select_policy" ON settings
    FOR SELECT USING (true);

CREATE POLICY "settings_insert_policy" ON settings
    FOR INSERT WITH CHECK (true);

CREATE POLICY "settings_update_policy" ON settings
    FOR UPDATE USING (true);

-- 4. Enable RLS kembali
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE students ENABLE ROW LEVEL SECURITY;
ALTER TABLE parents ENABLE ROW LEVEL SECURITY;
ALTER TABLE teachers ENABLE ROW LEVEL SECURITY;
ALTER TABLE classes ENABLE ROW LEVEL SECURITY;
ALTER TABLE subjects ENABLE ROW LEVEL SECURITY;
ALTER TABLE achievements ENABLE ROW LEVEL SECURITY;
ALTER TABLE class_students ENABLE ROW LEVEL SECURITY;
ALTER TABLE student_parents ENABLE ROW LEVEL SECURITY;
ALTER TABLE settings ENABLE ROW LEVEL SECURITY;
