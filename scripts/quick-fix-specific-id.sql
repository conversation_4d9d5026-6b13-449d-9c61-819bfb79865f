-- Quick fix untuk ID spesifik yang bermasalah
-- ID: 22222222-2222-2222-2222-222222222222

-- 1. Buat profile untuk ID yang bermasalah
INSERT INTO profiles (id, user_id, full_name, role, created_at, updated_at) VALUES
('22222222-2222-2222-2222-222222222222', '22222222-2222-2222-2222-222222222222', '<PERSON><PERSON><PERSON>', 'teacher', NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
    full_name = '<PERSON><PERSON><PERSON> <PERSON>',
    role = 'teacher',
    updated_at = NOW();

-- 2. <PERSON><PERSON> ada ID lain yang bermasalah, tambahkan di sini
INSERT INTO profiles (id, user_id, full_name, role, created_at, updated_at) VALUES
('33333333-3333-3333-3333-333333333333', '33333333-3333-3333-3333-333333333333', '<PERSON><PERSON><PERSON>', 'teacher', NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
    full_name = '<PERSON><PERSON><PERSON>hmud',
    role = 'teacher',
    updated_at = NOW();

-- 3. Buat profile admin untuk user saat ini (ganti dengan ID Anda)
-- Uncomment dan ganti YOUR_USER_ID dengan ID user Anda yang sebenarnya
/*
INSERT INTO profiles (id, user_id, full_name, role, created_at, updated_at) VALUES
('YOUR_USER_ID', 'YOUR_USER_ID', 'Admin User', 'admin', NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
    full_name = 'Admin User',
    role = 'admin',
    updated_at = NOW();
*/

-- 4. Verifikasi
SELECT 'Profile created for:' as status, id, full_name, role FROM profiles 
WHERE id IN ('22222222-2222-2222-2222-222222222222', '33333333-3333-3333-3333-333333333333');
