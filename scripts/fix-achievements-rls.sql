-- Script untuk memperba<PERSON> RLS policies untuk tabel achievements
-- Jalankan script ini di Supabase SQL Editor

-- 1. Periksa RLS policies yang ada
SELECT 'Current RLS Policies for achievements:' as info;
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual
FROM pg_policies 
WHERE tablename = 'achievements'
ORDER BY policyname;

-- 2. Drop existing policies jika ada yang bermasalah
DROP POLICY IF EXISTS achievements_admin_all ON achievements;
DROP POLICY IF EXISTS achievements_teacher_read ON achievements;
DROP POLICY IF EXISTS achievements_teacher_insert ON achievements;
DROP POLICY IF EXISTS achievements_teacher_update ON achievements;
DROP POLICY IF EXISTS achievements_parent_read ON achievements;

-- 3. Pastikan RLS aktif
ALTER TABLE achievements ENABLE ROW LEVEL SECURITY;

-- 4. Buat policies baru yang benar

-- Admin policies (full access)
CREATE POLICY achievements_admin_all ON achievements
    FOR ALL TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.role = 'admin'
        )
    );

-- Teacher policies (can read all, can insert/update their own verifications)
CREATE POLICY achievements_teacher_read ON achievements
    FOR SELECT TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM teachers 
            WHERE teachers.profile_id = auth.uid()
        )
    );

CREATE POLICY achievements_teacher_insert ON achievements
    FOR INSERT TO authenticated
    WITH CHECK (
        EXISTS (
            SELECT 1 FROM teachers 
            WHERE teachers.id = achievements.verified_by 
            AND teachers.profile_id = auth.uid()
        )
    );

CREATE POLICY achievements_teacher_update ON achievements
    FOR UPDATE TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM teachers 
            WHERE teachers.id = achievements.verified_by 
            AND teachers.profile_id = auth.uid()
        )
    );

-- Parent policies (can only read their children's achievements)
CREATE POLICY achievements_parent_read ON achievements
    FOR SELECT TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM parents p
            JOIN student_parent sp ON p.id = sp.parent_id
            WHERE p.profile_id = auth.uid()
            AND sp.student_id = achievements.student_id
        )
    );

-- 5. Temporary policy untuk testing (REMOVE IN PRODUCTION)
-- Uncomment jika ingin testing tanpa RLS restrictions
/*
CREATE POLICY achievements_temp_all ON achievements
    FOR ALL TO authenticated
    USING (true)
    WITH CHECK (true);
*/

-- 6. Periksa policies yang baru dibuat
SELECT 'New RLS Policies for achievements:' as info;
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual
FROM pg_policies 
WHERE tablename = 'achievements'
ORDER BY policyname;

-- 7. Test query sederhana
SELECT 'Testing basic query:' as info;
SELECT COUNT(*) as total_achievements FROM achievements;

-- 8. Periksa foreign key constraints
SELECT 'Foreign key constraints:' as info;
SELECT 
    tc.table_name, 
    kcu.column_name, 
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name 
FROM 
    information_schema.table_constraints AS tc 
    JOIN information_schema.key_column_usage AS kcu
      ON tc.constraint_name = kcu.constraint_name
      AND tc.table_schema = kcu.table_schema
    JOIN information_schema.constraint_column_usage AS ccu
      ON ccu.constraint_name = tc.constraint_name
      AND ccu.table_schema = tc.table_schema
WHERE tc.constraint_type = 'FOREIGN KEY' 
AND tc.table_name = 'achievements';

-- 9. Insert sample data untuk testing
INSERT INTO achievements (student_id, subject_id, value, grade, notes, verified_by, achievement_date)
SELECT 
    s.id as student_id,
    sub.id as subject_id,
    'Sample Achievement - Hafal Juz 30' as value,
    'A' as grade,
    'Test achievement for debugging' as notes,
    t.id as verified_by,
    CURRENT_DATE as achievement_date
FROM students s
CROSS JOIN subjects sub
CROSS JOIN teachers t
WHERE s.name LIKE '%Test%' OR s.student_id LIKE '%001%'
AND sub.name = 'Al-Quran'
AND t.name LIKE '%Test%' OR t.teacher_id LIKE '%TCH%'
LIMIT 1
ON CONFLICT DO NOTHING;

-- 10. Verify sample data was inserted
SELECT 'Sample achievements:' as info;
SELECT 
    a.id,
    a.value,
    a.grade,
    s.name as student_name,
    sub.name as subject_name,
    t.name as teacher_name
FROM achievements a
JOIN students s ON a.student_id = s.id
JOIN subjects sub ON a.subject_id = sub.id
LEFT JOIN teachers t ON a.verified_by = t.id
LIMIT 5;

SELECT 'RLS Fix completed!' as status;
