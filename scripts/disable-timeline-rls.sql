-- Script alternatif: Disable RLS untuk timeline tables (untuk development)
-- Gunakan script ini jika RLS policies terlalu kompleks untuk di-debug

-- 1. Disable RLS untuk timeline tables
ALTER TABLE timeline_entries DISABLE ROW LEVEL SECURITY;
ALTER TABLE timeline_details DISABLE ROW LEVEL SECURITY;
ALTER TABLE timeline_activities DISABLE ROW LEVEL SECURITY;
ALTER TABLE behavior_records DISABLE ROW LEVEL SECURITY;

-- 2. Verifikasi status RLS
SELECT 
    schemaname, 
    tablename, 
    rowsecurity,
    CASE 
        WHEN rowsecurity THEN 'RLS ENABLED' 
        ELSE 'RLS DISABLED' 
    END as status
FROM pg_tables 
WHERE tablename IN ('timeline_entries', 'timeline_details', 'timeline_activities', 'behavior_records')
AND schemaname = 'public'
ORDER BY tablename;

SELECT 'Timeline RLS disabled for development!' as status;

-- CATATAN: 
-- Script ini men-disable RLS untuk development purposes
-- Untuk production, sebaiknya gunakan script fix-timeline-rls-policies.sql
-- yang membuat policies yang tepat
