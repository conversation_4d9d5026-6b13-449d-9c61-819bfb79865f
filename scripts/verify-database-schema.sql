-- <PERSON><PERSON><PERSON> untuk memverifikasi dan memperbaiki database schema
-- Jalankan script ini di Supabase SQL Editor

-- 1. <PERSON><PERSON><PERSON> struktur tabel teachers
SELECT 'TEACHERS TABLE STRUCTURE:' as info;
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'teachers' 
ORDER BY ordinal_position;

-- 2. <PERSON><PERSON><PERSON> struktur tabel parents
SELECT 'PARENTS TABLE STRUCTURE:' as info;
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'parents' 
ORDER BY ordinal_position;

-- 3. Per<PERSON>sa struktur tabel subjects
SELECT 'SUBJECTS TABLE STRUCTURE:' as info;
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'subjects' 
ORDER BY ordinal_position;

-- 4. <PERSON><PERSON><PERSON> struktur tabel profiles
SELECT 'PROFILES TABLE STRUCTURE:' as info;
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'profiles' 
ORDER BY ordinal_position;

-- 5. Periksa foreign key constraints
SELECT 'FOREIGN KEY CONSTRAINTS:' as info;
SELECT 
    tc.table_name, 
    kcu.column_name, 
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name 
FROM 
    information_schema.table_constraints AS tc 
    JOIN information_schema.key_column_usage AS kcu
      ON tc.constraint_name = kcu.constraint_name
      AND tc.table_schema = kcu.table_schema
    JOIN information_schema.constraint_column_usage AS ccu
      ON ccu.constraint_name = tc.constraint_name
      AND ccu.table_schema = tc.table_schema
WHERE tc.constraint_type = 'FOREIGN KEY' 
AND tc.table_name IN ('teachers', 'parents', 'subjects');

-- 6. Periksa data sample
SELECT 'SAMPLE DATA CHECK:' as info;
SELECT 'Teachers count:' as table_name, COUNT(*) as count FROM teachers
UNION ALL
SELECT 'Parents count:' as table_name, COUNT(*) as count FROM parents
UNION ALL
SELECT 'Subjects count:' as table_name, COUNT(*) as count FROM subjects
UNION ALL
SELECT 'Profiles count:' as table_name, COUNT(*) as count FROM profiles;

-- 7. Perbaikan jika diperlukan
-- Uncomment dan jalankan bagian ini jika ada masalah

/*
-- Pastikan tabel teachers memiliki kolom yang benar
ALTER TABLE teachers ADD COLUMN IF NOT EXISTS teacher_id VARCHAR UNIQUE;
ALTER TABLE teachers ADD COLUMN IF NOT EXISTS profile_id UUID;
ALTER TABLE teachers ADD COLUMN IF NOT EXISTS join_date DATE;
ALTER TABLE teachers ADD COLUMN IF NOT EXISTS status VARCHAR DEFAULT 'active';

-- Pastikan tabel parents memiliki kolom yang benar
ALTER TABLE parents ADD COLUMN IF NOT EXISTS profile_id UUID;

-- Pastikan tabel subjects memiliki kolom yang benar
ALTER TABLE subjects ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT true;

-- Update trigger untuk updated_at jika belum ada
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger untuk teachers
DROP TRIGGER IF EXISTS update_teachers_updated_at ON teachers;
CREATE TRIGGER update_teachers_updated_at
    BEFORE UPDATE ON teachers
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Trigger untuk parents
DROP TRIGGER IF EXISTS update_parents_updated_at ON parents;
CREATE TRIGGER update_parents_updated_at
    BEFORE UPDATE ON parents
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Trigger untuk subjects
DROP TRIGGER IF EXISTS update_subjects_updated_at ON subjects;
CREATE TRIGGER update_subjects_updated_at
    BEFORE UPDATE ON subjects
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();
*/

-- 8. Periksa RLS policies
SELECT 'RLS POLICIES:' as info;
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual
FROM pg_policies 
WHERE tablename IN ('teachers', 'parents', 'subjects')
ORDER BY tablename, policyname;

-- 9. Test insert sederhana (uncomment untuk test)
/*
-- Test insert teacher
INSERT INTO teachers (teacher_id, name, specialization, status, profile_id, created_at, updated_at)
VALUES ('TEST-001', 'Test Teacher', 'Test Subject', 'active', gen_random_uuid(), NOW(), NOW())
ON CONFLICT (teacher_id) DO NOTHING;

-- Test insert parent
INSERT INTO parents (name, email, phone, address, occupation, profile_id, created_at, updated_at)
VALUES ('Test Parent', '<EMAIL>', '+62123456789', 'Test Address', 'Test Job', gen_random_uuid(), NOW(), NOW());

-- Test insert subject
INSERT INTO subjects (name, category, description, is_active, created_at, updated_at)
VALUES ('Test Subject', 'Test Category', 'Test Description', true, NOW(), NOW());
*/

SELECT 'SCHEMA VERIFICATION COMPLETED' as status;
