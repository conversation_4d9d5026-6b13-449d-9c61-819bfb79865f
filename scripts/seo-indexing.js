#!/usr/bin/env node

/**
 * SEO Indexing Helper Script untuk PTQ Al Ihsan Website
 * Script ini membantu proses indexing ulang website ke search engine
 */

const https = require('https');
const fs = require('fs');
const path = require('path');

const BASE_URL = 'https://www.ptqalihsan.ac.id';

// Daftar halaman yang perlu diindex
const pages = [
  '/',
  '/tentang-kami',
  '/program',
  '/ppdb',
  '/kontak',
  '/blog',
  '/blog/1',
  '/blog/2',
  '/blog/3'
];

/**
 * Fungsi untuk ping Google untuk indexing
 */
function pingGoogle(url) {
  const pingUrl = `https://www.google.com/ping?sitemap=${encodeURIComponent(url)}`;
  
  return new Promise((resolve, reject) => {
    https.get(pingUrl, (res) => {
      if (res.statusCode === 200) {
        console.log(`✅ Berhasil ping Google untuk: ${url}`);
        resolve(true);
      } else {
        console.log(`❌ Gagal ping Google untuk: ${url} (Status: ${res.statusCode})`);
        resolve(false);
      }
    }).on('error', (err) => {
      console.log(`❌ Error ping Google untuk: ${url} - ${err.message}`);
      resolve(false);
    });
  });
}

/**
 * Fungsi untuk ping Bing untuk indexing
 */
function pingBing(url) {
  const pingUrl = `https://www.bing.com/ping?sitemap=${encodeURIComponent(url)}`;
  
  return new Promise((resolve, reject) => {
    https.get(pingUrl, (res) => {
      if (res.statusCode === 200) {
        console.log(`✅ Berhasil ping Bing untuk: ${url}`);
        resolve(true);
      } else {
        console.log(`❌ Gagal ping Bing untuk: ${url} (Status: ${res.statusCode})`);
        resolve(false);
      }
    }).on('error', (err) => {
      console.log(`❌ Error ping Bing untuk: ${url} - ${err.message}`);
      resolve(false);
    });
  });
}

/**
 * Fungsi untuk membuat sitemap sederhana
 */
function generateSitemap() {
  const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${pages.map(page => `  <url>
    <loc>${BASE_URL}${page}</loc>
    <lastmod>${new Date().toISOString().split('T')[0]}</lastmod>
    <changefreq>${page === '/' || page === '/ppdb' ? 'daily' : 'weekly'}</changefreq>
    <priority>${page === '/' ? '1.0' : page === '/ppdb' ? '0.9' : '0.8'}</priority>
  </url>`).join('\n')}
</urlset>`;

  return sitemap;
}

/**
 * Fungsi utama untuk indexing
 */
async function runIndexing() {
  console.log('🚀 Memulai proses indexing website PTQ Al Ihsan...\n');

  // 1. Generate sitemap
  console.log('📝 Membuat sitemap...');
  const sitemap = generateSitemap();
  const sitemapPath = path.join(__dirname, '../public/sitemap-backup.xml');
  fs.writeFileSync(sitemapPath, sitemap);
  console.log(`✅ Sitemap dibuat di: ${sitemapPath}\n`);

  // 2. Ping search engines untuk sitemap
  console.log('🔔 Mengirim ping ke search engines...');
  const sitemapUrl = `${BASE_URL}/sitemap.xml`;
  
  await pingGoogle(sitemapUrl);
  await pingBing(sitemapUrl);
  
  console.log('\n📊 Proses indexing selesai!');
  console.log('\n📋 Langkah selanjutnya:');
  console.log('1. Submit sitemap manual ke Google Search Console');
  console.log('2. Submit sitemap manual ke Bing Webmaster Tools');
  console.log('3. Periksa robots.txt di /robots.txt');
  console.log('4. Verifikasi structured data dengan Google Rich Results Test');
  console.log('5. Monitor indexing status di search console');
  
  console.log('\n🔗 Link penting:');
  console.log('- Google Search Console: https://search.google.com/search-console');
  console.log('- Bing Webmaster Tools: https://www.bing.com/webmasters');
  console.log('- Rich Results Test: https://search.google.com/test/rich-results');
  console.log(`- Sitemap URL: ${sitemapUrl}`);
  console.log(`- Robots.txt: ${BASE_URL}/robots.txt`);
}

// Jalankan script jika dipanggil langsung
if (require.main === module) {
  runIndexing().catch(console.error);
}

module.exports = {
  runIndexing,
  pingGoogle,
  pingBing,
  generateSitemap
}; 