// Script untuk debug masalah gambar di frontend
// Jalankan dengan: node scripts/debug-frontend-images.js

const BASE_URL = 'http://localhost:3001'

// Test specific teacher data
async function debugSpecificTeacher() {
  console.log('\n=== Debug Specific Teacher ===')
  
  // Test the teacher from your example
  const teacherData = {
    "id": "85e4443c-cc53-457d-8050-2a7026c848a2",
    "teacher_id": "TCH-1752679634126-4J0YKZ",
    "name": "tes",
    "specialization": "Agama",
    "photo_url": "https://labs-sptsa.xd1iar.easypanel.host/storage/v1/object/public/photos/teachers/teacher_85e4443c-cc53-457d-8050-2a7026c848a2_1752686331734_3surto.png",
    "join_date": "2025-07-16",
    "status": "active"
  }
  
  console.log('Teacher Data:')
  console.log(JSON.stringify(teacherData, null, 2))
  
  console.log('\nTesting photo URL accessibility:')
  try {
    const response = await fetch(teacherData.photo_url)
    
    if (response.ok) {
      console.log('✅ Photo URL is accessible')
      console.log(`Status: ${response.status}`)
      console.log(`Content-Type: ${response.headers.get('content-type')}`)
      console.log(`Content-Length: ${response.headers.get('content-length')} bytes`)
      
      // Test if it's a valid image
      const contentType = response.headers.get('content-type')
      if (contentType && contentType.startsWith('image/')) {
        console.log('✅ Valid image content type')
      } else {
        console.log('⚠️  Unexpected content type')
      }
    } else {
      console.log('❌ Photo URL not accessible')
      console.log(`Status: ${response.status} ${response.statusText}`)
    }
  } catch (error) {
    console.log('❌ Error accessing photo URL:', error.message)
  }
}

// Generate test HTML to verify image loading
async function generateTestHTML() {
  console.log('\n=== Generate Test HTML ===')
  
  const testUrl = "https://labs-sptsa.xd1iar.easypanel.host/storage/v1/object/public/photos/teachers/teacher_85e4443c-cc53-457d-8050-2a7026c848a2_1752686331734_3surto.png"
  
  const html = `
<!DOCTYPE html>
<html>
<head>
    <title>Image Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-container { margin: 20px 0; padding: 20px; border: 1px solid #ccc; }
        img { max-width: 200px; height: auto; border: 1px solid #ddd; }
        .error { color: red; }
        .success { color: green; }
    </style>
</head>
<body>
    <h1>Image Loading Test</h1>
    
    <div class="test-container">
        <h2>Direct IMG Tag</h2>
        <img src="${testUrl}" alt="Test Image" 
             onload="document.getElementById('img-status').innerHTML = '✅ Image loaded successfully'" 
             onerror="document.getElementById('img-status').innerHTML = '❌ Image failed to load'">
        <p id="img-status">Loading...</p>
    </div>
    
    <div class="test-container">
        <h2>Background Image</h2>
        <div style="width: 200px; height: 200px; background-image: url('${testUrl}'); background-size: cover; background-position: center; border: 1px solid #ddd;"></div>
    </div>
    
    <div class="test-container">
        <h2>Fetch Test</h2>
        <button onclick="testFetch()">Test Fetch</button>
        <p id="fetch-status"></p>
    </div>
    
    <script>
        async function testFetch() {
            const status = document.getElementById('fetch-status');
            status.innerHTML = 'Testing...';
            
            try {
                const response = await fetch('${testUrl}');
                if (response.ok) {
                    status.innerHTML = '✅ Fetch successful: ' + response.status;
                    status.className = 'success';
                } else {
                    status.innerHTML = '❌ Fetch failed: ' + response.status;
                    status.className = 'error';
                }
            } catch (error) {
                status.innerHTML = '❌ Fetch error: ' + error.message;
                status.className = 'error';
            }
        }
        
        // Auto-test fetch on load
        window.onload = function() {
            testFetch();
        };
    </script>
</body>
</html>
  `
  
  // Save to file
  const fs = require('fs')
  const path = require('path')
  
  const testFilePath = path.join(process.cwd(), 'public', 'image-test.html')
  fs.writeFileSync(testFilePath, html)
  
  console.log('✅ Test HTML generated: public/image-test.html')
  console.log('Open in browser: http://localhost:3001/image-test.html')
}

// Check browser console errors
async function checkBrowserConsoleErrors() {
  console.log('\n=== Browser Console Error Check ===')
  
  console.log('Common browser console errors and solutions:')
  console.log('')
  
  console.log('1. CORS Error:')
  console.log('   Error: "Access to fetch at ... has been blocked by CORS policy"')
  console.log('   Solution: Check Supabase Storage CORS settings')
  console.log('')
  
  console.log('2. Next.js Image Error:')
  console.log('   Error: "Invalid src prop ... hostname is not configured"')
  console.log('   Solution: Add domain to next.config.js (already done)')
  console.log('')
  
  console.log('3. Network Error:')
  console.log('   Error: "Failed to load resource: net::ERR_NAME_NOT_RESOLVED"')
  console.log('   Solution: Check if Supabase URL is correct and accessible')
  console.log('')
  
  console.log('4. 404 Not Found:')
  console.log('   Error: "Failed to load resource: the server responded with a status of 404"')
  console.log('   Solution: Check if file exists in Supabase Storage')
  console.log('')
  
  console.log('5. Avatar Component Issue:')
  console.log('   Error: Image loads but Avatar shows fallback')
  console.log('   Solution: Check AvatarImage src prop and onError handler')
}

// Test Avatar component specifically
async function testAvatarComponent() {
  console.log('\n=== Avatar Component Test ===')
  
  console.log('Avatar component issues to check:')
  console.log('')
  
  console.log('1. AvatarImage src prop:')
  console.log('   ✅ Should use: teacher.photo_url')
  console.log('   ❌ Not: hardcoded placeholder')
  console.log('')
  
  console.log('2. Fallback behavior:')
  console.log('   - AvatarImage should try to load the image first')
  console.log('   - AvatarFallback should only show if image fails')
  console.log('   - onError handler should provide fallback URL')
  console.log('')
  
  console.log('3. CSS/Styling issues:')
  console.log('   - Check if image is hidden by CSS')
  console.log('   - Verify z-index and positioning')
  console.log('   - Check if parent container has overflow:hidden')
  console.log('')
  
  console.log('4. React rendering:')
  console.log('   - Check if photo_url is available when component renders')
  console.log('   - Verify state updates trigger re-render')
  console.log('   - Check for conditional rendering logic')
}

// Generate debugging checklist
async function generateDebuggingChecklist() {
  console.log('\n=== Debugging Checklist ===')
  
  console.log('Step-by-step debugging process:')
  console.log('')
  
  console.log('□ 1. Verify data in database')
  console.log('   - Check if photo_url is saved correctly')
  console.log('   - Verify URL format is complete')
  console.log('')
  
  console.log('□ 2. Test URL accessibility')
  console.log('   - Copy URL from database')
  console.log('   - Paste in new browser tab')
  console.log('   - Should show image directly')
  console.log('')
  
  console.log('□ 3. Check Next.js configuration')
  console.log('   - Verify domain in next.config.js')
  console.log('   - Restart dev server after config change')
  console.log('')
  
  console.log('□ 4. Inspect frontend code')
  console.log('   - Check if components use photo_url prop')
  console.log('   - Verify no hardcoded placeholders')
  console.log('   - Check onError handlers')
  console.log('')
  
  console.log('□ 5. Browser developer tools')
  console.log('   - Open DevTools (F12)')
  console.log('   - Check Network tab for failed requests')
  console.log('   - Check Console tab for errors')
  console.log('   - Inspect element to see actual src attribute')
  console.log('')
  
  console.log('□ 6. Test with different browsers')
  console.log('   - Try Chrome, Firefox, Safari')
  console.log('   - Test in incognito/private mode')
  console.log('   - Check mobile browsers')
}

// Main debug function
async function runFrontendImageDebug() {
  console.log('🔍 Starting Frontend Image Debug...')
  console.log('This will help identify why images are not showing in the frontend')
  console.log('')
  
  try {
    // Debug specific teacher
    await debugSpecificTeacher()
    
    // Generate test HTML
    await generateTestHTML()
    
    // Check browser console errors
    await checkBrowserConsoleErrors()
    
    // Test Avatar component
    await testAvatarComponent()
    
    // Generate debugging checklist
    await generateDebuggingChecklist()
    
    console.log('\n🎉 Frontend image debug completed!')
    
    console.log('\n📋 Immediate Actions:')
    console.log('1. Open: http://localhost:3001/image-test.html')
    console.log('2. Check if image loads in test page')
    console.log('3. Open browser DevTools on teacher list page')
    console.log('4. Look for network errors or console errors')
    console.log('5. Restart dev server (npm run dev) after next.config.js changes')
    
  } catch (error) {
    console.error('❌ Debug execution failed:', error.message)
  }
}

// Run debug if this script is executed directly
if (require.main === module) {
  runFrontendImageDebug()
}

module.exports = { 
  runFrontendImageDebug,
  debugSpecificTeacher,
  generateTestHTML
}
