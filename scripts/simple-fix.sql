-- <PERSON><PERSON><PERSON> se<PERSON>hana untuk memperbaiki foreign key constraint
-- <PERSON><PERSON><PERSON> dengan struktur profiles: id, user_id, full_name, avatar_url, role

BEGIN;

-- 1. <PERSON><PERSON><PERSON> struktur tabel profiles yang ada
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_name = 'profiles' 
ORDER BY ordinal_position;

-- 2. <PERSON><PERSON><PERSON> profiles untuk semua teachers yang belum punya profile
INSERT INTO profiles (id, user_id, full_name, role, created_at, updated_at)
SELECT DISTINCT 
    t.user_id,
    t.user_id,
    t.name,
    'teacher',
    NOW(),
    NOW()
FROM teachers t
LEFT JOIN profiles p ON p.id = t.user_id
WHERE p.id IS NULL AND t.user_id IS NOT NULL
ON CONFLICT (id) DO UPDATE SET
    full_name = EXCLUDED.full_name,
    role = 'teacher',
    updated_at = NOW();

-- 3. <PERSON>uat profiles untuk semua parents yang belum punya profile
INSERT INTO profiles (id, user_id, full_name, role, created_at, updated_at)
SELECT DISTINCT 
    pa.user_id,
    pa.user_id,
    pa.name,
    'parent',
    NOW(),
    NOW()
FROM parents pa
LEFT JOIN profiles p ON p.id = pa.user_id
WHERE p.id IS NULL AND pa.user_id IS NOT NULL
ON CONFLICT (id) DO UPDATE SET
    full_name = EXCLUDED.full_name,
    role = 'parent',
    updated_at = NOW();

-- 4. Sekarang aman untuk mengubah struktur tabel teachers
-- Drop constraint lama jika ada
ALTER TABLE teachers DROP CONSTRAINT IF EXISTS teachers_user_id_fkey;

-- Rename kolom jika masih bernama user_id
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'teachers' AND column_name = 'user_id') THEN
        ALTER TABLE teachers RENAME COLUMN user_id TO profile_id;
    END IF;
END $$;

-- Tambahkan constraint baru
ALTER TABLE teachers ADD CONSTRAINT teachers_profile_id_fkey 
    FOREIGN KEY (profile_id) REFERENCES profiles(id) ON DELETE CASCADE;

-- 5. Lakukan hal yang sama untuk parents
ALTER TABLE parents DROP CONSTRAINT IF EXISTS parents_user_id_fkey;

DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'parents' AND column_name = 'user_id') THEN
        ALTER TABLE parents RENAME COLUMN user_id TO profile_id;
    END IF;
END $$;

ALTER TABLE parents ADD CONSTRAINT parents_profile_id_fkey 
    FOREIGN KEY (profile_id) REFERENCES profiles(id) ON DELETE CASCADE;

-- 6. Verifikasi hasil
SELECT 
    'Teachers' as table_name, 
    COUNT(*) as total_records,
    COUNT(profile_id) as records_with_profile_id
FROM teachers

UNION ALL

SELECT 
    'Parents' as table_name, 
    COUNT(*) as total_records,
    COUNT(profile_id) as records_with_profile_id
FROM parents

UNION ALL

SELECT 
    'Profiles' as table_name, 
    COUNT(*) as total_records,
    COUNT(id) as records_with_id
FROM profiles;

-- 7. Tampilkan sample data untuk verifikasi
SELECT 'TEACHERS SAMPLE' as info, profile_id, name FROM teachers LIMIT 3;
SELECT 'PARENTS SAMPLE' as info, profile_id, name FROM parents LIMIT 3;
SELECT 'PROFILES SAMPLE' as info, id, full_name, role FROM profiles LIMIT 5;

COMMIT;

-- Pesan sukses
SELECT 'Migration completed successfully!' as status;
