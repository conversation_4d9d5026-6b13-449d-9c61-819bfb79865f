import { createServerClient as createClient } from '@supabase/ssr'
import type { Database } from '@/types/supabase'
import { cookies } from 'next/headers'

// For App Router - works with Next.js 15+
export const createServerClient = async () => {
  const cookieStore = await cookies()

  return createClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value
        },
        set(name: string, value: string, options: any) {
          try {
            cookieStore.set({ name, value, ...options })
          } catch (error) {
            // Handle cookie setting error silently in edge runtime
          }
        },
        remove(name: string, options: any) {
          try {
            cookieStore.set({ name, value: '', ...options })
          } catch (error) {
            // Handle cookie removal error silently in edge runtime
          }
        },
      },
    }
  )
}

// For Pages Router (using cookies from request)
export const createPageServerClient = (context: { req: any; res: any }) => {
  if (!context) throw new Error('Context is required for createPageServerClient')
  
  return createClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name) {
          return context.req.cookies[name]
        },
        set(name, value, options) {
          context.res.cookie(name, value, options)
        },
        remove(name, options) {
          context.res.cookie(name, '', { ...options, maxAge: 0 })
        },
      },
    }
  )
}