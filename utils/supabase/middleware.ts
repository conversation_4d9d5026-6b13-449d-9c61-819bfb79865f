import { createMiddlewareClient } from '@supabase/auth-helpers-nextjs'
import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import type { Database } from '@/types/supabase'

export async function middleware(req: NextRequest) {
  // Buat response yang dapat dimodifikasi
  const res = NextResponse.next()
  
  // Buat middleware client Supabase
  const supabase = createMiddlewareClient<Database>({ req, res })
  
  // Refresh session jika ada
  await supabase.auth.getSession()
  
  // Cek apakah request menuju ke rute yang dilindungi
  const isProtectedRoute = req.nextUrl.pathname.startsWith('/dashboard')
  
  // Dapatkan session
  const {
    data: { session },
  } = await supabase.auth.getSession()
  
  // Jika tidak ada session dan mencoba mengakses rute yang dilindungi
  if (!session && isProtectedRoute) {
    const redirectUrl = new URL('/login', req.url)
    return NextResponse.redirect(redirectUrl)
  }
  
  // Jika ada session tapi mencoba mengakses halaman login/register
  if (session && (req.nextUrl.pathname === '/login' || req.nextUrl.pathname === '/register')) {
    const redirectUrl = new URL('/dashboard', req.url)
    return NextResponse.redirect(redirectUrl)
  }
  
  return res
}

// Tentukan pada rute mana middleware ini akan dijalankan
export const config = {
  matcher: [
    /*
     * Match semua request paths kecuali untuk:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!api|_next/static|_next/image|favicon.ico|public).*)',
  ],
}