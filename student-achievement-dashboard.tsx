"use client"

import { useState, useEffect } from "react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { CalendarDays, User, BookOpen, Clock, Award, BookText, AlertCircle, Loader2 } from "lucide-react"
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'
import { Alert, AlertDescription } from "@/components/ui/alert"

type Student = {
  id: string
  student_id: string
  name: string
  gender: "male" | "female"
  birth_date: string | null
  address: string | null
  photo_url: string | null
  batch: string | null
  status: "active" | "inactive" | "graduated" | "suspended"
}

type Achievement = {
  id: string
  subject_id: string
  value: string
  grade: string
  notes: string | null
  achievement_date: string
  subjects: { name: string } | { name: string }[] | null
}

type Props = {
  student: Student
}

export default function StudentAchievementDashboard({ student }: Props) {
  const [achievements, setAchievements] = useState<Achievement[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const supabase = createClientComponentClient()

  useEffect(() => {
    const fetchAchievements = async () => {
      try {
        setIsLoading(true)
        
        const { data, error } = await supabase
          .from('achievements')
          .select(`
            id,
            subject_id,
            value,
            grade,
            notes,
            achievement_date,
            subjects(name)
          `)
          .eq('student_id', student.id)
          .order('achievement_date', { ascending: false })
          
        if (error) {
          throw new Error("Gagal memuat data pencapaian")
        }
        
        setAchievements(data || [])
      } catch (err) {
        setError(err instanceof Error ? err.message : "Terjadi kesalahan")
        console.error("Error fetching achievements:", err)
      } finally {
        setIsLoading(false)
      }
    }
    
    fetchAchievements()
  }, [student.id, supabase])

  // Group achievements by subject
  const achievementsBySubject = achievements.reduce((acc, achievement) => {
    let subjectName = 'Lainnya'
    
    if (achievement.subjects) {
      if (Array.isArray(achievement.subjects)) {
        subjectName = achievement.subjects[0]?.name || 'Lainnya'
      } else {
        subjectName = achievement.subjects.name
      }
    }
    
    if (!acc[subjectName]) {
      acc[subjectName] = []
    }
    acc[subjectName].push(achievement)
    return acc
  }, {} as Record<string, Achievement[]>)

  // Format date for display
  const formatDate = (dateString: string | null) => {
    if (!dateString) return '-'
    const date = new Date(dateString)
    return new Intl.DateTimeFormat('id-ID', { 
      day: 'numeric', 
      month: 'long', 
      year: 'numeric' 
    }).format(date)
  }

  return (
    <div className="space-y-6">
      <Card className="border-none shadow-xl overflow-hidden">
        <div className="h-2 bg-emerald-600" />
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row gap-6 items-start">
            <Avatar className="h-24 w-24 border-4 border-emerald-100">
              <AvatarImage src={student.photo_url || ''} alt={student.name} />
              <AvatarFallback>
                <User className="h-12 w-12" />
              </AvatarFallback>
            </Avatar>
            <div className="space-y-4">
              <div>
                <h1 className="text-2xl font-bold">{student.name}</h1>
                <p className="text-gray-500">{student.student_id}</p>
              </div>

              <div className="flex flex-wrap gap-3">
                {student.batch && (
                  <Badge variant="outline" className="gap-1 pl-1.5 font-normal">
                    <CalendarDays className="h-3.5 w-3.5" />
                    <span>{student.batch}</span>
                  </Badge>
                )}
                <Badge variant="outline" className="gap-1 pl-1.5 font-normal">
                  <User className="h-3.5 w-3.5" />
                  <span>{student.gender === 'male' ? 'Laki-laki' : 'Perempuan'}</span>
                </Badge>
                <Badge variant="outline" className="gap-1 pl-1.5 font-normal text-emerald-600 bg-emerald-50 border-emerald-200">
                  <BookOpen className="h-3.5 w-3.5" />
                  <span>{student.status === 'active' ? 'Aktif' : student.status}</span>
                </Badge>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className="border-none shadow-xl">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Award className="h-5 w-5 text-emerald-600" />
            Pencapaian Santri
          </CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex justify-center items-center py-12">
              <Loader2 className="h-8 w-8 animate-spin text-emerald-600" />
            </div>
          ) : error ? (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          ) : Object.keys(achievementsBySubject).length > 0 ? (
            <Tabs defaultValue={Object.keys(achievementsBySubject)[0]} className="w-full">
              <TabsList className="mb-4 flex flex-wrap h-auto">
                {Object.keys(achievementsBySubject).map((subject) => (
                  <TabsTrigger key={subject} value={subject} className="mb-1">
                    {subject}
                  </TabsTrigger>
                ))}
              </TabsList>

              {Object.entries(achievementsBySubject).map(([subject, subjectAchievements]) => (
                <TabsContent key={subject} value={subject} className="space-y-4">
                  {subjectAchievements.map((achievement) => (
                    <Card key={achievement.id} className="overflow-hidden">
                      <div className="h-1 bg-emerald-600" />
                      <CardContent className="p-4">
                        <div className="flex flex-col md:flex-row justify-between gap-4">
                          <div>
                            <h3 className="font-semibold text-lg">{achievement.value}</h3>
                            {achievement.notes && (
                              <p className="text-gray-500 mt-1">{achievement.notes}</p>
                            )}
                          </div>
                          <div className="flex flex-col items-end gap-1">
                            <Badge className="bg-emerald-100 text-emerald-800 hover:bg-emerald-200 border-none">
                              Grade: {achievement.grade}
                            </Badge>
                            <div className="flex items-center gap-1 text-sm text-gray-500">
                              <Clock className="h-3.5 w-3.5" />
                              <span>{formatDate(achievement.achievement_date)}</span>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </TabsContent>
              ))}
            </Tabs>
          ) : (
            <div className="flex flex-col items-center justify-center py-12 text-center">
              <BookText className="h-12 w-12 text-gray-300 mb-2" />
              <h3 className="text-lg font-medium">Belum ada pencapaian</h3>
              <p className="text-gray-500 max-w-md">
                Belum ada data pencapaian untuk santri ini. Pencapaian akan ditampilkan saat data tersedia.
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

