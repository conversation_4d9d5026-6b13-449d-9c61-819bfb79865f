-- Website Settings Tables
-- This file contains the database schema for dynamic website content management

-- Create website_settings table for general settings
CREATE TABLE website_settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    key VARCHAR(255) UNIQUE NOT NULL,
    value TEXT,
    type VARCHAR(50) DEFAULT 'text' CHECK (type IN ('text', 'textarea', 'image', 'boolean', 'json')),
    category VARCHAR(100) DEFAULT 'general',
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create website_images table for image management
CREATE TABLE website_images (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    key VARCHAR(255) UNIQUE NOT NULL,
    filename VA<PERSON>HA<PERSON>(255) NOT NULL,
    original_name VA<PERSON>HA<PERSON>(255),
    file_path TEXT NOT NULL,
    file_size INTEGER,
    mime_type VARCHAR(100),
    alt_text TEXT,
    caption TEXT,
    category VARCHAR(100) DEFAULT 'general',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create website_content table for structured content
CREATE TABLE website_content (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    section VARCHAR(100) NOT NULL,
    key VARCHAR(255) NOT NULL,
    title VARCHAR(500),
    content TEXT,
    image_id UUID REFERENCES website_images(id),
    order_index INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(section, key)
);

-- Create indexes for better performance
CREATE INDEX idx_website_settings_key ON website_settings(key);
CREATE INDEX idx_website_settings_category ON website_settings(category);
CREATE INDEX idx_website_images_key ON website_images(key);
CREATE INDEX idx_website_images_category ON website_images(category);
CREATE INDEX idx_website_content_section ON website_content(section);
CREATE INDEX idx_website_content_key ON website_content(key);
CREATE INDEX idx_website_content_active ON website_content(is_active);

-- Create triggers for updated_at
CREATE TRIGGER update_website_settings_modtime
    BEFORE UPDATE ON website_settings
    FOR EACH ROW
    EXECUTE PROCEDURE update_modified_column();

CREATE TRIGGER update_website_images_modtime
    BEFORE UPDATE ON website_images
    FOR EACH ROW
    EXECUTE PROCEDURE update_modified_column();

CREATE TRIGGER update_website_content_modtime
    BEFORE UPDATE ON website_content
    FOR EACH ROW
    EXECUTE PROCEDURE update_modified_column();

-- Insert default website settings
INSERT INTO website_settings (key, value, type, category, description) VALUES
-- Site Information
('site_title', 'PTQ Al Ihsan - Pondok Tahfidzul Qur''an', 'text', 'site_info', 'Judul utama website'),
('site_description', 'Pondok Tahfidzul Qur''an yang berfokus pada pembentukan generasi Qur''ani dengan pemahaman Islam yang benar.', 'textarea', 'site_info', 'Deskripsi website'),
('site_keywords', 'pondok tahfidz, pesantren, pendidikan islam, tahfidz quran', 'text', 'site_info', 'Keywords untuk SEO'),

-- Hero Section
('hero_title', 'Pondok Tahfidzul Qur''an Al Ihsan, Menyiapkan Generasi Bertaqwa, Hebat dan Bermanfaat', 'text', 'hero', 'Judul utama hero section'),
('hero_subtitle', 'Setara jenjang SMP dan SMK', 'text', 'hero', 'Subjudul hero section'),
('hero_description', 'Pendidikan terbaik berbasis tahfidz dengan kurikulum komprehensif', 'textarea', 'hero', 'Deskripsi hero section'),

-- PPDB Information
('ppdb_status', 'open', 'text', 'ppdb', 'Status pendaftaran (open/closed/coming-soon)'),
('ppdb_batch', 'Batch 5', 'text', 'ppdb', 'Batch pendaftaran saat ini'),
('ppdb_date', '1 Maret - 30 Juni 2024', 'text', 'ppdb', 'Tanggal pendaftaran'),
('ppdb_announcement', 'Pendaftaran Batch 5 telah dibuka!', 'text', 'ppdb', 'Pengumuman PPDB'),

-- Contact Information
('contact_address', 'Desa Keji, Ungaran Barat, Kabupaten Semarang, Jawa Tengah, Indonesia', 'textarea', 'contact', 'Alamat lengkap'),
('contact_phone_1', '+62 822-2737-4455', 'text', 'contact', 'Nomor telepon utama'),
('contact_phone_2', '+62 813-2456-7890', 'text', 'contact', 'Nomor telepon kedua'),
('contact_email_1', '<EMAIL>', 'text', 'contact', 'Email utama'),
('contact_email_2', '<EMAIL>', 'text', 'contact', 'Email PPDB'),
('contact_website', 'https://www.ptqalihsan.ac.id', 'text', 'contact', 'Website URL'),

-- Operating Hours
('operating_hours', 'Senin - Jumat: 08.00 - 16.00 WIB
Sabtu: 08.00 - 12.00 WIB
Minggu & Hari Libur: Tutup', 'textarea', 'contact', 'Jam operasional'),

-- Social Media
('social_facebook', 'https://facebook.com/ptqalihsan', 'text', 'social', 'Facebook URL'),
('social_instagram', 'https://instagram.com/ptqalihsan', 'text', 'social', 'Instagram URL'),
('social_youtube', 'https://youtube.com/@ptqalihsan', 'text', 'social', 'YouTube URL'),
('social_whatsapp', 'https://wa.me/6282227374455', 'text', 'social', 'WhatsApp URL'),

-- Feature Toggles
('feature_ppdb_menu', 'true', 'boolean', 'features', 'Tampilkan menu PPDB'),
('feature_blog_menu', 'true', 'boolean', 'features', 'Tampilkan menu blog'),
('feature_contact_form', 'true', 'boolean', 'features', 'Aktifkan form kontak'),
('feature_parent_dashboard', 'true', 'boolean', 'features', 'Aktifkan portal orang tua'),
('feature_achievement_page', 'true', 'boolean', 'features', 'Tampilkan halaman pencapaian');

-- Insert default content sections
INSERT INTO website_content (section, key, title, content, order_index) VALUES
-- About Section
('about', 'vision', 'Visi Kami', 'Menjadi lembaga pendidikan Islam terdepan dalam mencetak generasi Qur''ani yang berakhlak mulia, cerdas, dan bermanfaat bagi umat.', 1),
('about', 'mission', 'Misi Kami', 'Menyelenggarakan pendidikan Islam berkualitas dengan fokus pada hafalan Al-Qur''an, pemahaman agama yang benar, dan pembentukan karakter Islami.', 2),
('about', 'history', 'Sejarah Singkat', 'PTQ Al Ihsan didirikan dengan visi mulia untuk mencetak generasi Qur''ani yang unggul dalam ilmu agama dan kehidupan.', 3),

-- Values Section
('values', 'tahfidz', 'Hafal Al Qur''an', 'Minimal 7 Juz dengan pemahaman yang baik', 1),
('values', 'aqidah', 'Penguasaan Aqidah', 'Matan Aqidah Ushulus Tsalasah', 2),
('values', 'hadits', 'Hafal Hadits Arbain', 'Hafal dan memahami 42 hadits pilihan', 3),
('values', 'fiqih', 'Fiqih Hidayatul Mutafaqqih', 'Hafalan mutun bab pilihan dan Pemahaman fiqih', 4),
('values', 'nahwu', 'Hafal Al Ajurumiyah', 'Dasar-dasar tata bahasa Arab', 5),
('values', 'kitab', 'Kitab Gundul', 'Kemampuan membaca kitab tanpa harakat', 6),
('values', 'speaking', 'Public Speaking', 'Kemampuan berpidato dan presentasi', 7),
('values', 'akhlak', 'Akhlak Mulia', 'Pembentukan karakter Islami', 8),

-- Programs Section
('programs', 'tahfidz_program', 'Tahfidz Al-Qur''an', 'Program hafalan Al-Qur''an dengan target minimal 7 juz selama masa pendidikan.', 1),
('programs', 'kitab_program', 'Kajian Kitab Kuning', 'Pembelajaran kitab-kitab klasik dengan metode yang mudah dipahami.', 2),
('programs', 'speaking_program', 'Public Speaking', 'Pelatihan kemampuan berbicara di depan umum, pidato, dan presentasi.', 3),

-- Facilities Section
('facilities', 'asrama', 'Asrama Nyaman', 'Asrama terpisah untuk santri putra dan putri dengan fasilitas lengkap.', 1),
('facilities', 'masjid', 'Masjid', 'Masjid sebagai pusat kegiatan ibadah dan pembelajaran Al-Qur''an.', 2),
('facilities', 'perpustakaan', 'Perpustakaan', 'Koleksi lengkap kitab-kitab dan buku penunjang pembelajaran.', 3),
('facilities', 'ruang_belajar', 'Ruang Belajar', 'Ruang belajar yang nyaman dan kondusif untuk kegiatan pembelajaran.', 4),
('facilities', 'lapangan_olahraga', 'Lapangan Olahraga', 'Fasilitas olahraga untuk menjaga kesehatan dan kebugaran santri.', 5),
('facilities', 'kantin', 'Kantin Sehat', 'Menyediakan makanan sehat dan bergizi untuk santri.', 6);

-- Enable RLS
ALTER TABLE website_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE website_images ENABLE ROW LEVEL SECURITY;
ALTER TABLE website_content ENABLE ROW LEVEL SECURITY;

-- Create policies for website settings (admin only)
CREATE POLICY website_settings_admin_all ON website_settings
    FOR ALL TO authenticated
    USING (auth.uid() IN (SELECT id FROM profiles WHERE role = 'admin'));

CREATE POLICY website_images_admin_all ON website_images
    FOR ALL TO authenticated
    USING (auth.uid() IN (SELECT id FROM profiles WHERE role = 'admin'));

CREATE POLICY website_content_admin_all ON website_content
    FOR ALL TO authenticated
    USING (auth.uid() IN (SELECT id FROM profiles WHERE role = 'admin'));

-- Create policies for public read access
CREATE POLICY website_settings_public_read ON website_settings
    FOR SELECT TO anon
    USING (true);

CREATE POLICY website_images_public_read ON website_images
    FOR SELECT TO anon
    USING (true);

CREATE POLICY website_content_public_read ON website_content
    FOR SELECT TO anon
    USING (is_active = true);
