-- Admin User Creation for PTQ Al Ihsan

-- NOTE: You can't directly insert users into auth.users table with plain SQL
-- Instead, use the Supabase Dashboard UI or API to create the user first
-- Then run the following SQL to set up the admin profile

-- 1. First create a user via Supabase Dashboard or API:
--    Email: <EMAIL>
--    Password: (use a strong password)

-- 2. After creating the user, get their UUID and insert it below
-- Replace 'YOUR-USER-UUID-HERE' with the actual UUID from the Supabase dashboard

-- Add user to profiles table with admin role
INSERT INTO public.profiles (user_id, full_name, role)
VALUES 
  ('12ec1368-e50e-4611-aa1e-29839268b3cd', 'Admin PTQ Al Ihsan', 'admin');

-- Optional: If you want to use SQL to create a user (requires service role access)
-- This approach requires the SUPABASE_SERVICE_ROLE_KEY in your environment variables
/*
-- Create admin user in auth.users table
INSERT INTO auth.users (
  id,
  email,
  encrypted_password,
  email_confirmed_at,
  raw_app_meta_data,
  raw_user_meta_data,
  created_at,
  updated_at
) VALUES (
  uuid_generate_v4(),
  '<EMAIL>',
  -- Warning: This is NOT how you should set passwords in production!
  -- Use Supabase Auth API or Dashboard UI instead
  crypt('change-this-password', gen_salt('bf')),
  now(),
  '{"provider":"email","providers":["email"]}',
  '{"name":"Admin PTQ Al Ihsan"}',
  now(),
  now()
);

-- Get the user's ID
DO $$
DECLARE
  admin_id uuid;
BEGIN
  SELECT id INTO admin_id FROM auth.users WHERE email = '<EMAIL>';
  
  -- Insert admin profile
  INSERT INTO public.profiles (user_id, full_name, role)
  VALUES (admin_id, 'Admin PTQ Al Ihsan', 'admin');
END
$$;
*/

-- Create stored procedure for adding users with appropriate roles
CREATE OR REPLACE PROCEDURE create_user_with_profile(
  p_email TEXT,
  p_password TEXT,
  p_full_name TEXT,
  p_role TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_user_id UUID;
BEGIN
  -- Only the superuser or service role should run this
  -- This procedure requires elevated privileges
  
  -- Create user in auth.users
  INSERT INTO auth.users (
    email,
    encrypted_password,
    email_confirmed_at,
    raw_app_meta_data,
    raw_user_meta_data,
    created_at,
    updated_at
  ) VALUES (
    p_email,
    crypt(p_password, gen_salt('bf')),
    now(),
    '{"provider":"email","providers":["email"]}',
    jsonb_build_object('full_name', p_full_name),
    now(),
    now()
  ) RETURNING id INTO v_user_id;
  
  -- Create profile with the role
  INSERT INTO public.profiles (
    user_id,
    full_name,
    role
  ) VALUES (
    v_user_id,
    p_full_name,
    p_role
  );
  
  -- Set the user's role in auth.users metadata
  UPDATE auth.users 
  SET raw_app_meta_data = raw_app_meta_data || jsonb_build_object('role', p_role)
  WHERE id = v_user_id;
END;
$$; 