"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Phone } from "lucide-react"

export function ParentAccessButton() {
  const [isOpen, setIsOpen] = useState(false)

  const handleClick = () => {
    setIsOpen(true)
    alert("Akses Orangtua diklik! Dialog seharusnya terbuka.")

    // Redirect to parent dashboard as a fallback
    // window.location.href = "/parent-dashboard"
  }

  return (
    <Button
      variant="outline"
      className="gap-2 bg-white text-emerald-800 hover:bg-emerald-50 border-emerald-200"
      onClick={handleClick}
    >
      <Phone className="h-4 w-4" />
      Akses Orangtua
    </Button>
  )
}
