"use client"

import { useState } from "react"
import { useRout<PERSON> } from "next/navigation"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { z } from "zod"
import { Loader2, Search, User } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { toast } from "@/components/ui/use-toast"

// Simulasi data orangtua yang sudah ada
const existingParents = [
  {
    id: "parent-1",
    name: "<PERSON><PERSON>",
    phone: "081234567890",
    email: "<EMAIL>",
    address: "Jl. <PERSON>a <PERSON> No. 123, Semarang",
    occupation: "Wiraswasta",
  },
  {
    id: "parent-2",
    name: "Siti Aminah",
    phone: "081234567891",
    email: "<EMAIL>",
    address: "Jl. Raya Ungaran No. 123, Semarang",
    occupation: "Guru",
  },
  {
    id: "parent-3",
    name: "Ahmad Hidayat",
    phone: "081234567892",
    email: "<EMAIL>",
    address: "Jl. Pahlawan No. 45, Semarang",
    occupation: "Dokter",
  },
  {
    id: "parent-4",
    name: "Dewi Kartika",
    phone: "081234567893",
    email: "<EMAIL>",
    address: "Jl. Diponegoro No. 78, Semarang",
    occupation: "Pengusaha",
  },
]

// Schema validasi form orangtua baru
const newParentFormSchema = z.object({
  name: z.string().min(1, { message: "Nama harus diisi" }),
  relationship: z.enum(["father", "mother", "guardian"], {
    required_error: "Hubungan harus dipilih",
  }),
  phone: z.string().min(1, { message: "Nomor telepon harus diisi" }),
  email: z.string().email({ message: "Format email tidak valid" }).optional().or(z.literal("")),
  address: z.string().min(1, { message: "Alamat harus diisi" }),
  occupation: z.string().optional(),
  is_primary: z.boolean().default(false),
})

export function AddParentForm({
  studentId,
  studentName,
}: {
  studentId: string
  studentName: string
}) {
  const router = useRouter()
  const [activeTab, setActiveTab] = useState("new")
  const [searchQuery, setSearchQuery] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)

  // Form untuk orangtua baru
  const form = useForm<z.infer<typeof newParentFormSchema>>({
    resolver: zodResolver(newParentFormSchema),
    defaultValues: {
      name: "",
      relationship: undefined,
      phone: "",
      email: "",
      address: "",
      occupation: "",
      is_primary: false,
    },
  })

  // Filter orangtua berdasarkan pencarian
  const filteredParents = existingParents.filter(
    (parent) =>
      parent.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      parent.phone.includes(searchQuery) ||
      parent.email.toLowerCase().includes(searchQuery.toLowerCase()),
  )

  // Handle submit form orangtua baru
  const onSubmit = async (values: z.infer<typeof newParentFormSchema>) => {
    setIsSubmitting(true)

    try {
      // Dalam implementasi nyata, ini akan menyimpan data ke Supabase
      console.log("Form Values:", values)
      console.log("Student ID:", studentId)

      // Simulasi delay
      await new Promise((resolve) => setTimeout(resolve, 1000))

      toast({
        title: "Orangtua berhasil ditambahkan",
        description: `${values.name} telah ditambahkan sebagai ${
          values.relationship === "father" ? "Ayah" : values.relationship === "mother" ? "Ibu" : "Wali"
        } untuk siswa ${studentName}.`,
      })

      // Redirect ke halaman detail siswa
      router.push(`/dashboard/students/${studentId}`)
    } catch (error) {
      console.error("Error submitting form:", error)
      toast({
        title: "Error",
        description: "Terjadi kesalahan saat menyimpan data. Silakan coba lagi.",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  // Handle pilih orangtua yang sudah ada
  const handleSelectExistingParent = async (
    parent: (typeof existingParents)[0],
    relationship: "father" | "mother" | "guardian",
    isPrimary: boolean,
  ) => {
    setIsSubmitting(true)

    try {
      // Dalam implementasi nyata, ini akan menyimpan relasi ke Supabase
      console.log("Selected Parent:", parent)
      console.log("Relationship:", relationship)
      console.log("Is Primary:", isPrimary)
      console.log("Student ID:", studentId)

      // Simulasi delay
      await new Promise((resolve) => setTimeout(resolve, 1000))

      toast({
        title: "Orangtua berhasil ditambahkan",
        description: `${parent.name} telah ditambahkan sebagai ${
          relationship === "father" ? "Ayah" : relationship === "mother" ? "Ibu" : "Wali"
        } untuk siswa ${studentName}.`,
      })

      // Redirect ke halaman detail siswa
      router.push(`/dashboard/students/${studentId}`)
    } catch (error) {
      console.error("Error selecting parent:", error)
      toast({
        title: "Error",
        description: "Terjadi kesalahan saat menyimpan data. Silakan coba lagi.",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Tambah Orangtua/Wali</CardTitle>
        <CardDescription>Tambahkan orangtua/wali baru atau pilih dari yang sudah ada</CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="new" value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="new">Orangtua Baru</TabsTrigger>
            <TabsTrigger value="existing">Orangtua yang Sudah Ada</TabsTrigger>
          </TabsList>

          <TabsContent value="new" className="space-y-4 pt-4">
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Nama Lengkap</FormLabel>
                      <FormControl>
                        <Input placeholder="Nama lengkap orangtua" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="relationship"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Hubungan</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Pilih hubungan" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="father">Ayah</SelectItem>
                          <SelectItem value="mother">Ibu</SelectItem>
                          <SelectItem value="guardian">Wali</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="phone"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Nomor Telepon</FormLabel>
                        <FormControl>
                          <Input placeholder="08xxxxxxxxxx" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Email</FormLabel>
                        <FormControl>
                          <Input placeholder="<EMAIL>" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="address"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Alamat</FormLabel>
                      <FormControl>
                        <Textarea placeholder="Alamat lengkap orangtua" className="min-h-[80px]" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="occupation"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Pekerjaan</FormLabel>
                      <FormControl>
                        <Input placeholder="Pekerjaan orangtua" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="is_primary"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                      <FormControl>
                        <input
                          type="checkbox"
                          className="h-4 w-4 mt-1"
                          checked={field.value}
                          onChange={field.onChange}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>Wali Utama</FormLabel>
                        <FormDescription>Jadikan sebagai wali utama yang akan dihubungi pertama kali</FormDescription>
                      </div>
                    </FormItem>
                  )}
                />

                <div className="flex justify-end space-x-2">
                  <Button type="button" variant="outline" asChild>
                    <a href={`/dashboard/students/${studentId}`}>Batal</a>
                  </Button>
                  <Button type="submit" disabled={isSubmitting}>
                    {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                    Simpan
                  </Button>
                </div>
              </form>
            </Form>
          </TabsContent>

          <TabsContent value="existing" className="space-y-4 pt-4">
            <div className="relative">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Cari orangtua..."
                className="pl-8"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>

            <div className="border rounded-md divide-y max-h-[400px] overflow-y-auto">
              {filteredParents.length > 0 ? (
                filteredParents.map((parent) => (
                  <div key={parent.id} className="p-4">
                    <div className="flex items-start gap-3">
                      <User className="h-10 w-10 text-muted-foreground bg-muted p-2 rounded-full" />
                      <div className="flex-1">
                        <h3 className="font-medium">{parent.name}</h3>
                        <p className="text-sm text-muted-foreground">{parent.phone}</p>
                        <p className="text-sm text-muted-foreground">{parent.email}</p>
                        <p className="text-sm text-muted-foreground mt-1">{parent.address}</p>
                        <p className="text-sm text-muted-foreground">{parent.occupation}</p>
                      </div>
                    </div>

                    <div className="mt-4 flex flex-col sm:flex-row gap-2">
                      <Select
                        onValueChange={(value) =>
                          handleSelectExistingParent(parent, value as "father" | "mother" | "guardian", false)
                        }
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Pilih hubungan" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="father">Tambahkan sebagai Ayah</SelectItem>
                          <SelectItem value="mother">Tambahkan sebagai Ibu</SelectItem>
                          <SelectItem value="guardian">Tambahkan sebagai Wali</SelectItem>
                        </SelectContent>
                      </Select>

                      <Button
                        variant="outline"
                        onClick={() => handleSelectExistingParent(parent, "guardian", true)}
                        disabled={isSubmitting}
                      >
                        {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                        Tambahkan sebagai Wali Utama
                      </Button>
                    </div>
                  </div>
                ))
              ) : (
                <div className="p-8 text-center">
                  <p className="text-muted-foreground">Tidak ada orangtua yang ditemukan</p>
                </div>
              )}
            </div>

            <div className="flex justify-end">
              <Button variant="outline" asChild>
                <a href={`/dashboard/students/${studentId}`}>Kembali</a>
              </Button>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
