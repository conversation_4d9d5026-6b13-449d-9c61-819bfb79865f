import { Skeleton } from "@/components/ui/skeleton"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"

export function StudentDetailSkeleton() {
  return (
    <div className="space-y-6">
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row gap-6">
            <div className="flex flex-col items-center space-y-4">
              <Skeleton className="h-40 w-40 rounded-full" />
              <Skeleton className="h-6 w-20 rounded-full" />
            </div>

            <div className="flex-1 space-y-4">
              <div>
                <Skeleton className="h-8 w-48" />
                <Skeleton className="h-4 w-24 mt-2" />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {Array.from({ length: 4 }).map((_, i) => (
                  <div key={i} className="flex items-start space-x-2">
                    <Skeleton className="h-5 w-5 mt-0.5" />
                    <div className="space-y-2">
                      <Skeleton className="h-4 w-24" />
                      <Skeleton className="h-4 w-32" />
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="parents">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="parents">Data Orangtua</TabsTrigger>
          <TabsTrigger value="achievements">Pencapaian</TabsTrigger>
          <TabsTrigger value="timeline">Timeline</TabsTrigger>
          <TabsTrigger value="attendance">Kehadiran</TabsTrigger>
        </TabsList>

        <TabsContent value="parents" className="space-y-4 pt-4">
          <div className="flex justify-between items-center">
            <Skeleton className="h-6 w-40" />
            <Skeleton className="h-9 w-36" />
          </div>

          {Array.from({ length: 2 }).map((_, i) => (
            <Card key={i}>
              <div className="p-6 space-y-4">
                <div className="flex justify-between items-start">
                  <div>
                    <Skeleton className="h-6 w-40" />
                    <Skeleton className="h-4 w-24 mt-2" />
                  </div>
                  <div className="flex gap-2">
                    <Skeleton className="h-9 w-16" />
                    <Skeleton className="h-9 w-32" />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                  {Array.from({ length: 4 }).map((_, j) => (
                    <div key={j} className="flex items-start space-x-2">
                      <Skeleton className="h-5 w-5 mt-0.5" />
                      <div className="space-y-2">
                        <Skeleton className="h-4 w-24" />
                        <Skeleton className="h-4 w-32" />
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </Card>
          ))}
        </TabsContent>

        <TabsContent value="achievements" className="space-y-4 pt-4">
          <Skeleton className="h-6 w-48" />
          <div className="text-center p-8 border rounded-lg">
            <Skeleton className="h-12 w-12 mx-auto rounded" />
            <Skeleton className="h-6 w-48 mx-auto mt-4" />
            <Skeleton className="h-4 w-64 mx-auto mt-2" />
          </div>
        </TabsContent>

        <TabsContent value="timeline" className="space-y-4 pt-4">
          <div className="flex justify-between items-center">
            <Skeleton className="h-6 w-48" />
            <Skeleton className="h-9 w-32" />
          </div>

          <div className="relative border-l-2 border-gray-200 pl-6 ml-4">
            {Array.from({ length: 3 }).map((_, i) => (
              <div key={i} className="relative pb-8">
                <div className="absolute -left-8 top-0 w-4 h-4 bg-gray-200 rounded-full"></div>
                <div className="bg-white rounded-lg border border-gray-200 p-6">
                  <div className="flex justify-between items-start mb-4">
                    <Skeleton className="h-6 w-32" />
                    <Skeleton className="h-5 w-20" />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    {Array.from({ length: 2 }).map((_, j) => (
                      <div key={j} className="border rounded-lg p-4">
                        <Skeleton className="h-5 w-24 mb-2" />
                        <div className="flex justify-between items-center mb-2">
                          <Skeleton className="h-4 w-32" />
                          <Skeleton className="h-5 w-8" />
                        </div>
                        <Skeleton className="h-3 w-full" />
                      </div>
                    ))}
                  </div>

                  <div className="bg-gray-50 p-4 rounded-lg">
                    <Skeleton className="h-4 w-32 mb-2" />
                    <div className="space-y-1">
                      <Skeleton className="h-3 w-full" />
                      <Skeleton className="h-3 w-3/4" />
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="attendance" className="space-y-4 pt-4">
          <Skeleton className="h-6 w-32" />
          <div className="text-center p-8 border rounded-lg">
            <Skeleton className="h-12 w-12 mx-auto rounded" />
            <Skeleton className="h-6 w-48 mx-auto mt-4" />
            <Skeleton className="h-4 w-64 mx-auto mt-2" />
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
