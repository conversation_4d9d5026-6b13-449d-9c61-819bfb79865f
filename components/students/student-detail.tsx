"use client"

import { useState, useEffect } from "react"
import Image from "next/image"
import { User, Calendar, MapPin, Phone, Mail, BookOpen, Users, UserPlus, UserMinus, Loader2 } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"

// Types
type Parent = {
  id: string
  name: string
  relationship: string
  phone: string
  email: string
  address: string
  occupation: string
  is_primary: boolean
}

type Class = {
  id: string
  name: string
  level: string
  class_id: string
  academic_year: string
}

type TimelineEntry = {
  id: string
  student_id: string
  month: string
  year: number
  created_at: string
  timeline_details: {
    id: string
    subject_id: string
    value: string
    grade: string
    notes: string | null
    subjects: {
      id: string
      name: string
      category: string
    }
  }[]
  timeline_activities: {
    id: string
    activity: string
  }[]
  behavior_records: {
    id: string
    behavior_grade: string
    notes: string | null
  }[]
}

type Student = {
  id: string
  student_id: string
  name: string
  gender: string
  birth_date: string
  address: string
  photo_url: string | null
  batch: string
  status: string
  user_id: string
  created_at: string
  updated_at: string
  classes?: Class[]
  parents?: Parent[]
}

export function StudentDetail({ id }: { id: string }) {
  const [student, setStudent] = useState<Student | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [timeline, setTimeline] = useState<TimelineEntry[]>([])
  const [timelineLoading, setTimelineLoading] = useState(false)

  // Fetch student data from API
  useEffect(() => {
    const fetchStudent = async () => {
      try {
        setLoading(true)

        // Fetch student basic data
        const studentResponse = await fetch(`/api/students?id=${id}`)
        if (!studentResponse.ok) {
          throw new Error('Failed to fetch student')
        }
        const studentData = await studentResponse.json()

        // Fetch student's parents
        let parentsData = []
        try {
          const parentsResponse = await fetch(`/api/students/${id}/parents`)
          if (parentsResponse.ok) {
            const parents = await parentsResponse.json()
            parentsData = parents.parents || []
          }
        } catch (err) {
          console.log('Parents endpoint not available yet')
        }

        // Fetch student's classes
        let classesData = []
        try {
          const classesResponse = await fetch(`/api/students/${id}/classes`)
          if (classesResponse.ok) {
            const classes = await classesResponse.json()
            classesData = classes.classes || []
          }
        } catch (err) {
          console.log('Classes endpoint not available yet')
        }

        const fullStudentData = {
          ...studentData.student,
          parents: parentsData,
          classes: classesData
        }

        setStudent(fullStudentData)
        setError(null)
      } catch (err) {
        console.error('Error fetching student:', err)
        setError('Gagal memuat data siswa')
      } finally {
        setLoading(false)
      }
    }

    fetchStudent()
  }, [id])

  // Fetch timeline data
  const fetchTimeline = async () => {
    try {
      setTimelineLoading(true)
      const response = await fetch(`/api/students/${id}/timeline`)
      if (response.ok) {
        const data = await response.json()
        setTimeline(data.timeline || [])
      }
    } catch (error) {
      console.error('Error fetching timeline:', error)
    } finally {
      setTimelineLoading(false)
    }
  }

  // Handle remove parent
  const handleRemoveParent = async (parentId: string) => {
    try {
      const response = await fetch(`/api/students/${id}/parents/${parentId}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        throw new Error('Failed to remove parent')
      }

      // Refresh student data
      if (student) {
        setStudent({
          ...student,
          parents: student.parents?.filter(p => p.id !== parentId) || []
        })
      }
    } catch (err) {
      console.error('Error removing parent:', err)
      setError('Gagal menghapus relasi orangtua')
    }
  }

  // Loading state
  if (loading) {
    return (
      <div className="space-y-6">
        <div className="p-8 text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-muted-foreground">Memuat data siswa...</p>
        </div>
      </div>
    )
  }

  // Error state
  if (error || !student) {
    return (
      <div className="space-y-6">
        <div className="p-8 text-center">
          <p className="text-destructive mb-4">{error || 'Data siswa tidak ditemukan'}</p>
          <Button onClick={() => window.location.reload()}>
            Coba Lagi
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row gap-6">
            <div className="flex flex-col items-center space-y-4">
              <div className="relative h-40 w-40 rounded-full overflow-hidden border-4 border-primary/10 bg-muted flex items-center justify-center">
                {student.photo_url ? (
                  <Image
                    src={student.photo_url}
                    alt={student.name}
                    fill
                    className="object-cover"
                    onError={(e) => {
                      // Hide image if failed to load and show fallback
                      e.currentTarget.style.display = 'none'
                    }}
                  />
                ) : (
                  <User className="h-16 w-16 text-muted-foreground" />
                )}
                {/* Fallback icon if image fails to load */}
                <User className="h-16 w-16 text-muted-foreground absolute inset-0 m-auto opacity-0 peer-[img:not([style*='display: none'])]:opacity-100" />
              </div>
              <Badge variant={student.status === "active" ? "default" : "secondary"} className="px-3 py-1">
                {student.status === "active" ? "Aktif" : "Tidak Aktif"}
              </Badge>
            </div>

            <div className="flex-1 space-y-4">
              <div>
                <h2 className="text-2xl font-bold">{student.name}</h2>
                <p className="text-muted-foreground">{student.student_id}</p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-start space-x-2">
                  <User className="h-5 w-5 text-muted-foreground mt-0.5" />
                  <div>
                    <p className="text-sm text-muted-foreground">Jenis Kelamin</p>
                    <p>{student.gender}</p>
                  </div>
                </div>

                <div className="flex items-start space-x-2">
                  <Calendar className="h-5 w-5 text-muted-foreground mt-0.5" />
                  <div>
                    <p className="text-sm text-muted-foreground">Tanggal Lahir</p>
                    <p>
                      {new Date(student.birth_date).toLocaleDateString("id-ID", {
                        day: "numeric",
                        month: "long",
                        year: "numeric",
                      })}
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-2">
                  <BookOpen className="h-5 w-5 text-muted-foreground mt-0.5" />
                  <div>
                    <p className="text-sm text-muted-foreground">Kelas</p>
                    <p>
                      {student.classes && student.classes.length > 0
                        ? student.classes.map(cls => `${cls.name} (${cls.level})`).join(', ')
                        : 'Belum ada kelas'
                      } ({student.batch})
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-2">
                  <MapPin className="h-5 w-5 text-muted-foreground mt-0.5" />
                  <div>
                    <p className="text-sm text-muted-foreground">Alamat</p>
                    <p>{student.address}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="parents">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="parents">Data Orangtua</TabsTrigger>
          <TabsTrigger value="achievements">Pencapaian</TabsTrigger>
          <TabsTrigger value="timeline">Timeline</TabsTrigger>
          <TabsTrigger value="attendance">Kehadiran</TabsTrigger>
        </TabsList>

        <TabsContent value="parents" className="space-y-4 pt-4">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-semibold">Data Orangtua/Wali</h3>
            <Button size="sm" asChild>
              <a href={`/dashboard/students/${id}/add-parent`}>
                <UserPlus className="h-4 w-4 mr-2" />
                Tambah Orangtua
              </a>
            </Button>
          </div>

          {(student.parents || []).map((parent) => (
            <Card key={parent.id}>
              <CardHeader className="pb-2">
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="flex items-center">
                      {parent.name}
                      {parent.is_primary && <Badge className="ml-2">Wali Utama</Badge>}
                    </CardTitle>
                    <CardDescription>{parent.relationship}</CardDescription>
                  </div>
                  <div className="flex gap-2">
                    <Button variant="outline" size="sm" asChild>
                      <a href={`/dashboard/parents/${parent.id}/edit`}>Edit</a>
                    </Button>
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button variant="outline" size="sm">
                          <UserMinus className="h-4 w-4 mr-2" />
                          Hapus Relasi
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>Hapus Relasi Orangtua</AlertDialogTitle>
                          <AlertDialogDescription>
                            Apakah Anda yakin ingin menghapus relasi dengan orangtua {parent.name}? Tindakan ini tidak
                            akan menghapus data orangtua, hanya menghapus hubungan dengan siswa ini.
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>Batal</AlertDialogCancel>
                          <AlertDialogAction
                            onClick={() => handleRemoveParent(parent.id)}
                            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                          >
                            Hapus Relasi
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-start space-x-2">
                    <Phone className="h-5 w-5 text-muted-foreground mt-0.5" />
                    <div>
                      <p className="text-sm text-muted-foreground">Telepon</p>
                      <p>{parent.phone}</p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-2">
                    <Mail className="h-5 w-5 text-muted-foreground mt-0.5" />
                    <div>
                      <p className="text-sm text-muted-foreground">Email</p>
                      <p>{parent.email}</p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-2">
                    <MapPin className="h-5 w-5 text-muted-foreground mt-0.5" />
                    <div>
                      <p className="text-sm text-muted-foreground">Alamat</p>
                      <p>{parent.address}</p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-2">
                    <User className="h-5 w-5 text-muted-foreground mt-0.5" />
                    <div>
                      <p className="text-sm text-muted-foreground">Pekerjaan</p>
                      <p>{parent.occupation}</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}

          {(!student.parents || student.parents.length === 0) && (
            <div className="text-center p-8 border rounded-lg">
              <Users className="h-12 w-12 mx-auto text-muted-foreground" />
              <h3 className="mt-4 text-lg font-medium">Belum ada data orangtua</h3>
              <p className="mt-2 text-sm text-muted-foreground">
                Siswa ini belum memiliki data orangtua yang terhubung.
              </p>
              <Button className="mt-4" asChild>
                <a href={`/dashboard/students/${id}/add-parent`}>
                  <UserPlus className="h-4 w-4 mr-2" />
                  Tambah Orangtua
                </a>
              </Button>
            </div>
          )}
        </TabsContent>

        <TabsContent value="achievements" className="space-y-4 pt-4">
          <h3 className="text-lg font-semibold">Pencapaian Akademik</h3>

          <div className="text-center p-8 border rounded-lg">
            <BookOpen className="h-12 w-12 mx-auto text-muted-foreground" />
            <h3 className="mt-4 text-lg font-medium">Fitur Pencapaian Segera Hadir</h3>
            <p className="mt-2 text-sm text-muted-foreground">
              Fitur pencapaian akademik siswa sedang dalam pengembangan.
            </p>
          </div>
        </TabsContent>

        <TabsContent value="timeline" className="space-y-4 pt-4">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-semibold">Timeline Perkembangan</h3>
            <Button
              size="sm"
              variant="outline"
              onClick={fetchTimeline}
              disabled={timelineLoading}
            >
              {timelineLoading ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Calendar className="h-4 w-4 mr-2" />
              )}
              Refresh Timeline
            </Button>
          </div>

          {timelineLoading ? (
            <div className="text-center p-8">
              <Loader2 className="h-8 w-8 mx-auto animate-spin text-muted-foreground" />
              <p className="mt-2 text-sm text-muted-foreground">Memuat timeline...</p>
            </div>
          ) : timeline.length > 0 ? (
            <div className="relative border-l-2 border-emerald-500 pl-6 ml-4">
              {timeline.map((entry, index) => (
                <TimelineEntryCard
                  key={entry.id}
                  entry={entry}
                  isLast={index === timeline.length - 1}
                />
              ))}
            </div>
          ) : (
            <div className="text-center p-8 border rounded-lg">
              <Calendar className="h-12 w-12 mx-auto text-muted-foreground" />
              <h3 className="mt-4 text-lg font-medium">Belum Ada Timeline</h3>
              <p className="mt-2 text-sm text-muted-foreground">
                Timeline perkembangan siswa belum tersedia. Klik refresh untuk memuat data terbaru.
              </p>
            </div>
          )}
        </TabsContent>

        <TabsContent value="attendance" className="space-y-4 pt-4">
          <h3 className="text-lg font-semibold">Kehadiran</h3>

          <div className="text-center p-8 border rounded-lg">
            <Calendar className="h-12 w-12 mx-auto text-muted-foreground" />
            <h3 className="mt-4 text-lg font-medium">Fitur Kehadiran Segera Hadir</h3>
            <p className="mt-2 text-sm text-muted-foreground">
              Fitur tracking kehadiran siswa sedang dalam pengembangan.
            </p>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}

// Helper function to get grade color
function getGradeColor(grade: string) {
  switch (grade?.toUpperCase()) {
    case 'A':
      return 'bg-emerald-100 text-emerald-800 border-emerald-200'
    case 'B':
      return 'bg-blue-100 text-blue-800 border-blue-200'
    case 'C':
      return 'bg-yellow-100 text-yellow-800 border-yellow-200'
    case 'D':
      return 'bg-orange-100 text-orange-800 border-orange-200'
    case 'E':
      return 'bg-red-100 text-red-800 border-red-200'
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200'
  }
}

// Timeline Entry Card Component
function TimelineEntryCard({ entry, isLast }: { entry: TimelineEntry; isLast: boolean }) {
  const monthNames = [
    'Januari', 'Februari', 'Maret', 'April', 'Mei', 'Juni',
    'Juli', 'Agustus', 'September', 'Oktober', 'November', 'Desember'
  ]

  const monthIndex = parseInt(entry.month) - 1
  const monthYear = `${monthNames[monthIndex]} ${entry.year}`

  return (
    <div className={cn("relative pb-8", !isLast && "border-b border-gray-100")}>
      {/* Timeline dot */}
      <div className="absolute -left-8 top-0 w-4 h-4 bg-emerald-500 rounded-full border-4 border-white shadow-md"></div>

      <div className="bg-white rounded-lg border border-gray-200 p-6 shadow-sm">
        <div className="flex justify-between items-start mb-4">
          <h3 className="text-xl font-bold text-gray-800">{monthYear}</h3>
          <Badge variant="outline" className="text-xs">
            {new Date(entry.created_at).toLocaleDateString('id-ID')}
          </Badge>
        </div>

        {/* Timeline Details (Subjects) */}
        {entry.timeline_details.length > 0 && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            {entry.timeline_details.map((detail) => (
              <Card key={detail.id} className="border-l-4 border-l-emerald-500">
                <CardHeader className="pb-2">
                  <CardTitle className="text-base">{detail.subjects?.name || 'Mata Pelajaran'}</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex justify-between items-center mb-2">
                    <p className="font-medium text-gray-900">{detail.value}</p>
                    <Badge className={cn("font-medium", getGradeColor(detail.grade))}>
                      {detail.grade}
                    </Badge>
                  </div>
                  {detail.notes && (
                    <p className="text-sm text-gray-600">{detail.notes}</p>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {/* Behavior Records */}
        {entry.behavior_records.length > 0 && (
          <div className="mb-4">
            <h4 className="font-medium mb-2 text-gray-800">Perilaku</h4>
            {entry.behavior_records.map((behavior) => (
              <div key={behavior.id} className="bg-blue-50 p-3 rounded-lg border border-blue-200">
                <div className="flex justify-between items-center mb-1">
                  <span className="font-medium text-blue-900">Nilai Perilaku</span>
                  <Badge className="bg-blue-100 text-blue-800 border-blue-200">
                    {behavior.behavior_grade}
                  </Badge>
                </div>
                {behavior.notes && (
                  <p className="text-sm text-blue-700">{behavior.notes}</p>
                )}
              </div>
            ))}
          </div>
        )}

        {/* Activities */}
        {entry.timeline_activities.length > 0 && (
          <div className="mt-4 bg-gray-50 p-4 rounded-lg border border-gray-200">
            <h4 className="font-medium mb-2 text-gray-800">Aktivitas & Prestasi</h4>
            <ul className="list-disc pl-5 space-y-1">
              {entry.timeline_activities.map((activity) => (
                <li key={activity.id} className="text-gray-700">
                  {activity.activity}
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* Show empty state if no data */}
        {entry.timeline_details.length === 0 &&
         entry.timeline_activities.length === 0 &&
         entry.behavior_records.length === 0 && (
          <div className="bg-gray-50 p-4 rounded-lg text-center">
            <p className="text-gray-500 text-sm">Belum ada data untuk bulan ini</p>
          </div>
        )}
      </div>
    </div>
  )
}
