'use client'

import Script from 'next/script'

interface OrganizationSchemaProps {
  name?: string
  url?: string
  logo?: string
  address?: {
    streetAddress: string
    addressLocality: string
    addressRegion: string
    postalCode?: string
    addressCountry: string
  }
  contactPoint?: {
    telephone: string
    contactType: string
    email?: string
  }[]
  sameAs?: string[]
}

interface EducationalOrganizationSchemaProps extends OrganizationSchemaProps {
  type?: 'EducationalOrganization' | 'School'
  description?: string
  foundingDate?: string
  programs?: string[]
}

export function OrganizationStructuredData({
  name = "PTQ Al Ihsan",
  url = "https://www.ptqalihsan.ac.id",
  logo = "https://www.ptqalihsan.ac.id/logo.svg",
  address = {
    streetAddress: "Desa Keji, Ungaran Barat",
    addressLocality: "Kabupaten Semarang",
    addressRegion: "Jawa Tengah",
    addressCountry: "ID"
  },
  contactPoint = [
    {
      telephone: "+62-822-2737-4455",
      contactType: "customer service",
      email: "<EMAIL>"
    },
    {
      telephone: "+62-813-2456-7890", 
      contactType: "admissions",
      email: "<EMAIL>"
    }
  ],
  sameAs = [
    "https://facebook.com/ptqalihsan",
    "https://instagram.com/ptqalihsan",
    "https://youtube.com/@ptqalihsan"
  ]
}: OrganizationSchemaProps) {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "EducationalOrganization",
    "name": name,
    "alternateName": "Pondok Tahfidz Quran Al Ihsan",
    "url": url,
    "logo": logo,
    "description": "Pondok Tahfidz Quran Al Ihsan Ungaran - Lembaga pendidikan Islam berbasis tahfidz Al-Quran dengan metode pembelajaran terpadu untuk membentuk generasi Qur'ani yang berakhlak mulia.",
    "address": {
      "@type": "PostalAddress",
      ...address
    },
    "contactPoint": contactPoint.map(contact => ({
      "@type": "ContactPoint",
      ...contact
    })),
    "sameAs": sameAs,
    "educationalCredentialAwarded": "Setara SMP",
    "hasProgram": [
      {
        "@type": "EducationalProgram",
        "name": "Program Tahfidz Al-Qur'an",
        "description": "Program hafalan Al-Qur'an dengan target minimal 7 juz"
      },
      {
        "@type": "EducationalProgram", 
        "name": "Pendidikan Karakter Islami",
        "description": "Pembentukan akhlak mulia berdasarkan nilai-nilai Islam"
      },
      {
        "@type": "EducationalProgram",
        "name": "Kajian Kitab Kuning",
        "description": "Pembelajaran kitab-kitab klasik Islam"
      }
    ],
    "audience": {
      "@type": "Audience",
      "audienceType": "Santri usia SMP"
    }
  }

  return (
    <Script
      id="organization-structured-data"
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(structuredData)
      }}
    />
  )
}

export function WebsiteStructuredData({
  url = "https://www.ptqalihsan.ac.id",
  name = "PTQ Al Ihsan",
  searchUrl = "https://www.ptqalihsan.ac.id/search?q={search_term_string}"
}: {
  url?: string
  name?: string
  searchUrl?: string
}) {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": name,
    "url": url,
    "potentialAction": {
      "@type": "SearchAction",
      "target": {
        "@type": "EntryPoint",
        "urlTemplate": searchUrl
      },
      "query-input": "required name=search_term_string"
    }
  }

  return (
    <Script
      id="website-structured-data"
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(structuredData)
      }}
    />
  )
}

export function BreadcrumbStructuredData({
  items
}: {
  items: Array<{
    name: string
    url: string
  }>
}) {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": items.map((item, index) => ({
      "@type": "ListItem",
      "position": index + 1,
      "name": item.name,
      "item": item.url
    }))
  }

  return (
    <Script
      id="breadcrumb-structured-data"
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(structuredData)
      }}
    />
  )
} 