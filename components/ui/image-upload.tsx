"use client"

import { useState, useRef } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Upload, X, Image as ImageIcon } from "lucide-react"
import { toast } from "@/components/ui/use-toast"

interface ImageUploadProps {
  imageKey: string
  category: string
  currentImage?: string
  onUploadSuccess: (data: { file_path: string }) => void
  label?: string
  description?: string
  className?: string
}

export function ImageUpload({
  imageKey,
  category,
  currentImage,
  onUploadSuccess,
  label = "Upload Image",
  description,
  className = ""
}: ImageUploadProps) {
  const [isUploading, setIsUploading] = useState(false)
  const [previewUrl, setPreviewUrl] = useState<string | null>(currentImage || null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    // Validate file type
    if (!file.type.startsWith('image/')) {
      toast({
        title: "Error",
        description: "Please select a valid image file",
        variant: "destructive"
      })
      return
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      toast({
        title: "Error", 
        description: "File size must be less than 5MB",
        variant: "destructive"
      })
      return
    }

    setIsUploading(true)

    try {
      // Create preview URL
      const preview = URL.createObjectURL(file)
      setPreviewUrl(preview)

      // For now, we'll just use a placeholder path
      // In a real implementation, you would upload to your storage service
      const fileName = `${category}/${imageKey}_${Date.now()}.${file.name.split('.').pop()}`
      const filePath = `/uploads/${fileName}`

      // Simulate upload delay
      await new Promise(resolve => setTimeout(resolve, 1000))

      // Call success callback
      onUploadSuccess({ file_path: filePath })

      toast({
        title: "Success",
        description: "Image uploaded successfully"
      })

    } catch (error) {
      console.error('Upload error:', error)
      toast({
        title: "Error",
        description: "Failed to upload image",
        variant: "destructive"
      })
      setPreviewUrl(currentImage || null)
    } finally {
      setIsUploading(false)
    }
  }

  const handleRemove = () => {
    setPreviewUrl(null)
    onUploadSuccess({ file_path: "" })
    if (fileInputRef.current) {
      fileInputRef.current.value = ""
    }
  }

  const handleClick = () => {
    fileInputRef.current?.click()
  }

  return (
    <div className={`space-y-4 ${className}`}>
      <div className="space-y-2">
        <Label>{label}</Label>
        {description && (
          <p className="text-sm text-muted-foreground">{description}</p>
        )}
      </div>

      <div className="space-y-4">
        {/* Preview */}
        {previewUrl && (
          <div className="relative inline-block">
            <div className="relative w-32 h-32 border-2 border-dashed border-gray-300 rounded-lg overflow-hidden">
              <img
                src={previewUrl}
                alt="Preview"
                className="w-full h-full object-cover"
              />
              <Button
                type="button"
                variant="destructive"
                size="sm"
                className="absolute top-1 right-1 h-6 w-6 p-0"
                onClick={handleRemove}
              >
                <X className="h-3 w-3" />
              </Button>
            </div>
          </div>
        )}

        {/* Upload Button */}
        {!previewUrl && (
          <div
            className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center cursor-pointer hover:border-gray-400 transition-colors"
            onClick={handleClick}
          >
            <ImageIcon className="mx-auto h-12 w-12 text-gray-400" />
            <div className="mt-2">
              <Button
                type="button"
                variant="outline"
                disabled={isUploading}
                className="pointer-events-none"
              >
                <Upload className="mr-2 h-4 w-4" />
                {isUploading ? "Uploading..." : "Choose Image"}
              </Button>
            </div>
            <p className="mt-2 text-sm text-gray-500">
              PNG, JPG, GIF up to 5MB
            </p>
          </div>
        )}

        {/* Change Button */}
        {previewUrl && (
          <Button
            type="button"
            variant="outline"
            onClick={handleClick}
            disabled={isUploading}
          >
            <Upload className="mr-2 h-4 w-4" />
            {isUploading ? "Uploading..." : "Change Image"}
          </Button>
        )}

        {/* Hidden File Input */}
        <Input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          onChange={handleFileSelect}
          className="hidden"
        />
      </div>
    </div>
  )
}
