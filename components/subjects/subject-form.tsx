"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { zod<PERSON>esol<PERSON> } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { z } from "zod"
import { Plus, X, Loader2 } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Card, CardContent } from "@/components/ui/card"
import { useToast } from "@/components/ui/use-toast"

// Type for the subjects from the API
export type Subject = {
  id: string;
  name: string;
  category: string;
  description: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

// Form validation schema
const subjectFormSchema = z.object({
  id: z.string().optional(),
  name: z.string().min(2, {
    message: "Nama subject harus minimal 2 karakter",
  }),
  category: z.string().min(1, {
    message: "Kategori harus dipilih",
  }),
  description: z.string().min(10, {
    message: "Deskripsi harus minimal 10 karakter",
  }),
  isActive: z.boolean().default(true),
  learningObjectives: z.array(z.string()).optional(),
})

export type SubjectFormValues = z.infer<typeof subjectFormSchema>

// Default values for the form
const defaultValues: Partial<SubjectFormValues> = {
  name: "",
  category: "",
  description: "",
  isActive: true,
  learningObjectives: [],
}

export function SubjectForm({ subjectId }: { subjectId?: string }) {
  const router = useRouter()
  const { toast } = useToast()
  const [newObjective, setNewObjective] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  
  // Initialize form
  const form = useForm<SubjectFormValues>({
    resolver: zodResolver(subjectFormSchema),
    defaultValues,
  })

  // Fetch subject data if editing an existing subject
  useEffect(() => {
    const fetchSubject = async () => {
      if (!subjectId) return
      
      try {
        setIsLoading(true)
        
        // Fetch subject details
        const response = await fetch(`/api/subjects/${subjectId}`)
        
        if (!response.ok) {
          throw new Error("Failed to fetch subject")
        }
        
        const data = await response.json()
        
        if (data.subject) {
          // Map API fields to form values
          form.reset({
            id: data.subject.id,
            name: data.subject.name,
            category: data.subject.category,
            description: data.subject.description,
            isActive: data.subject.is_active,
            learningObjectives: data.objectives?.map((obj: any) => obj.objective) || [],
          })
        }
      } catch (error) {
        toast({
          title: "Error",
          description: "Gagal memuat data subject",
          variant: "destructive",
        })
      } finally {
        setIsLoading(false)
      }
    }
    
    fetchSubject()
  }, [subjectId, form, toast])

  // Handle form submission
  async function onSubmit(data: SubjectFormValues) {
    try {
      setIsSubmitting(true)
      
      const url = subjectId ? `/api/subjects` : `/api/subjects`
      const method = subjectId ? "PUT" : "POST"
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })
      
      if (!response.ok) {
        throw new Error(subjectId ? "Failed to update subject" : "Failed to create subject")
      }
      
      toast({
        title: "Success",
        description: subjectId ? "Subject berhasil diperbarui" : "Subject baru berhasil dibuat",
      })
      
      // Redirect back to subjects list
      router.push("/dashboard/subjects")
      router.refresh() // Refresh the page to show updated data
    } catch (error) {
      toast({
        title: "Error",
        description: subjectId ? "Gagal memperbarui subject" : "Gagal membuat subject baru",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  // Add a new learning objective
  const addObjective = () => {
    if (newObjective.trim() === "") return

    const currentObjectives = form.getValues("learningObjectives") || []
    form.setValue("learningObjectives", [...currentObjectives, newObjective])
    setNewObjective("")
  }

  // Remove a learning objective
  const removeObjective = (index: number) => {
    const currentObjectives = form.getValues("learningObjectives") || []
    const updatedObjectives = currentObjectives.filter((_, i) => i !== index)
    form.setValue("learningObjectives", updatedObjectives)
  }

  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-12">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <span className="ml-2">Memuat data subject...</span>
      </div>
    )
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <div className="grid gap-6 md:grid-cols-2">
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Nama Subject</FormLabel>
                <FormControl>
                  <Input placeholder="Masukkan nama subject" {...field} />
                </FormControl>
                <FormDescription>Nama subject yang akan ditampilkan</FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="category"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Kategori</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Pilih kategori" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="Keagamaan">Keagamaan</SelectItem>
                    <SelectItem value="Bahasa">Bahasa</SelectItem>
                    <SelectItem value="Akademik">Akademik</SelectItem>
                    <SelectItem value="Keterampilan">Keterampilan</SelectItem>
                    <SelectItem value="Lainnya">Lainnya</SelectItem>
                  </SelectContent>
                </Select>
                <FormDescription>Kategori dari subject</FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Deskripsi</FormLabel>
              <FormControl>
                <Textarea placeholder="Masukkan deskripsi subject" className="min-h-[120px]" {...field} />
              </FormControl>
              <FormDescription>Deskripsi singkat tentang subject ini</FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="isActive"
          render={({ field }) => (
            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
              <div className="space-y-0.5">
                <FormLabel className="text-base">Status Aktif</FormLabel>
                <FormDescription>Subject akan ditampilkan jika status aktif</FormDescription>
              </div>
              <FormControl>
                <Switch checked={field.value} onCheckedChange={field.onChange} />
              </FormControl>
            </FormItem>
          )}
        />

        <div>
          <h3 className="text-lg font-medium mb-4">Tujuan Pembelajaran</h3>
          <div className="flex gap-2 mb-4">
            <Input
              placeholder="Tambahkan tujuan pembelajaran"
              value={newObjective}
              onChange={(e) => setNewObjective(e.target.value)}
              className="flex-1"
            />
            <Button type="button" onClick={addObjective}>
              <Plus className="h-4 w-4 mr-2" />
              Tambah
            </Button>
          </div>

          <Card>
            <CardContent className="p-4">
              {form.watch("learningObjectives")?.length ? (
                <div className="space-y-2">
                  {form.watch("learningObjectives")?.map((objective, index) => (
                    <div key={index} className="flex items-center justify-between gap-2 p-2 rounded-md bg-muted/50">
                      <span>{objective}</span>
                      <Button type="button" variant="ghost" size="icon" onClick={() => removeObjective(index)}>
                        <X className="h-4 w-4" />
                        <span className="sr-only">Hapus</span>
                      </Button>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-4 text-muted-foreground">
                  Belum ada tujuan pembelajaran yang ditambahkan
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        <div className="flex gap-4 justify-end">
          <Button type="button" variant="outline" onClick={() => router.push("/dashboard/subjects")}>
            Batal
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {subjectId ? "Update Subject" : "Simpan Subject"}
          </Button>
        </div>
      </form>
    </Form>
  )
}
