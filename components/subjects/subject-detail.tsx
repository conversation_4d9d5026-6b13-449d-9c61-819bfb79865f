"use client"

import { useState } from "react"
import Link from "next/link"
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, User<PERSON>inus } from "lucide-react"

import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"

// Mock data for a subject
const mockSubject = {
  id: "1",
  name: "Al-Quran",
  category: "Keagamaan",
  description:
    "Pembelajaran Al-Quran dan tajwid untuk santri dengan fokus pada bacaan yang benar dan pemahaman dasar tentang isi Al-Quran. Subject ini mencakup pembelajaran makhraj huruf, tajwi<PERSON>, dan kelancaran membaca.",
  isActive: true,
  learningObjectives: [
    "Santri mampu membaca Al-Quran dengan tajwid yang benar",
    "Santri memahami hukum-hukum bacaan dalam Al-Quran",
    "Santri dapat menghafal juz 30 dengan baik",
    "Santri memahami arti dasar dari ayat-ayat yang dibaca",
  ],
  teachers: [
    {
      id: "t1",
      name: "Ustadz Ahmad",
      role: "Guru Utama",
      isPrimary: true,
      avatar: "/placeholder.svg?height=40&width=40",
    },
    {
      id: "t2",
      name: "Ustadzah Fatimah",
      role: "Guru Pendamping",
      isPrimary: false,
      avatar: "/placeholder.svg?height=40&width=40",
    },
    {
      id: "t3",
      name: "Ustadz Mahmud",
      role: "Guru Pengganti",
      isPrimary: false,
      avatar: "/placeholder.svg?height=40&width=40",
    },
  ],
}

export function SubjectDetail({ id }: { id: string }) {
  const [activeTab, setActiveTab] = useState("overview")

  // In a real implementation, we would fetch the subject data based on the ID
  const subject = mockSubject

  // Handle remove teacher (mock implementation)
  const handleRemoveTeacher = (teacherId: string) => {
    console.log(`Remove teacher ${teacherId} from subject ${id}`)
    // In a real implementation, this would call an API to remove the teacher
  }

  if (!subject) {
    return (
      <div className="flex justify-center items-center h-[400px]">
        <p>Subject tidak ditemukan</p>
      </div>
    )
  }

  return (
    <Tabs defaultValue="overview" value={activeTab} onValueChange={setActiveTab}>
      <TabsList className="mb-6">
        <TabsTrigger value="overview">Ikhtisar</TabsTrigger>
        <TabsTrigger value="teachers">Guru ({subject.teachers.length})</TabsTrigger>
      </TabsList>

      <TabsContent value="overview">
        <div className="grid gap-6 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle>Informasi Subject</CardTitle>
              <CardDescription>Detail dasar tentang subject</CardDescription>
            </CardHeader>
            <CardContent>
              <dl className="space-y-4">
                <div>
                  <dt className="text-sm font-medium text-muted-foreground">Nama</dt>
                  <dd className="text-base">{subject.name}</dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-muted-foreground">Kategori</dt>
                  <dd className="text-base">{subject.category}</dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-muted-foreground">Status</dt>
                  <dd>
                    <Badge variant={subject.isActive ? "default" : "secondary"}>
                      {subject.isActive ? "Aktif" : "Tidak Aktif"}
                    </Badge>
                  </dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-muted-foreground">Deskripsi</dt>
                  <dd className="text-base">{subject.description}</dd>
                </div>
              </dl>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Tujuan Pembelajaran</CardTitle>
              <CardDescription>Tujuan yang ingin dicapai dalam subject ini</CardDescription>
            </CardHeader>
            <CardContent>
              {subject.learningObjectives.length > 0 ? (
                <ul className="space-y-2">
                  {subject.learningObjectives.map((objective, index) => (
                    <li key={index} className="flex gap-2">
                      <span className="text-primary font-medium">{index + 1}.</span>
                      <span>{objective}</span>
                    </li>
                  ))}
                </ul>
              ) : (
                <p className="text-muted-foreground">Belum ada tujuan pembelajaran yang ditambahkan</p>
              )}
            </CardContent>
          </Card>
        </div>
      </TabsContent>

      <TabsContent value="teachers">
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <div>
                <CardTitle>Guru yang Ditugaskan</CardTitle>
                <CardDescription>Daftar guru yang mengajar subject ini</CardDescription>
              </div>
              <Button asChild>
                <Link href={`/dashboard/subjects/${id}/add-teacher`}>
                  <UserCheck className="mr-2 h-4 w-4" />
                  Tambah Guru
                </Link>
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            {subject.teachers.length > 0 ? (
              <div className="space-y-4">
                {subject.teachers.map((teacher) => (
                  <div key={teacher.id} className="flex items-center justify-between p-4 rounded-lg border">
                    <div className="flex items-center gap-4">
                      <Avatar>
                        <AvatarImage src={teacher.avatar || "/placeholder.svg"} alt={teacher.name} />
                        <AvatarFallback>{teacher.name.substring(0, 2)}</AvatarFallback>
                      </Avatar>
                      <div>
                        <div className="font-medium">{teacher.name}</div>
                        <div className="flex items-center gap-2 mt-1">
                          <Badge variant={teacher.isPrimary ? "default" : "outline"}>{teacher.role}</Badge>
                          {teacher.isPrimary && <Badge variant="secondary">Guru Utama</Badge>}
                        </div>
                      </div>
                    </div>
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <UserMinus className="h-4 w-4" />
                          <span className="sr-only">Hapus Guru</span>
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>Hapus Guru dari Subject</AlertDialogTitle>
                          <AlertDialogDescription>
                            Apakah Anda yakin ingin menghapus {teacher.name} dari subject ini?
                            {teacher.isPrimary && (
                              <div className="mt-2 text-destructive font-medium">
                                Perhatian: Guru ini adalah guru utama untuk subject ini.
                              </div>
                            )}
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>Batal</AlertDialogCancel>
                          <AlertDialogAction
                            onClick={() => handleRemoveTeacher(teacher.id)}
                            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                          >
                            Hapus
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <UserCheck className="mx-auto h-12 w-12 text-muted-foreground" />
                <h3 className="mt-4 text-lg font-semibold">Belum ada guru</h3>
                <p className="mt-2 text-sm text-muted-foreground">Belum ada guru yang ditugaskan untuk subject ini</p>
                <Button asChild className="mt-4">
                  <Link href={`/dashboard/subjects/${id}/add-teacher`}>Tambah Guru</Link>
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      </TabsContent>
    </Tabs>
  )
}
