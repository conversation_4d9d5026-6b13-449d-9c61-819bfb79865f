"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { Eye, Pencil, Trash2, Search, ChevronLeft, ChevronRight, BookText, Loader2 } from "lucide-react"
import { useRouter } from "next/navigation"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { useToast } from "@/components/ui/use-toast"
import { Subject as SubjectType } from "@/lib/api/subjects"

// Subject type definition that matches our API
type Subject = {
  id: string;
  name: string;
  category: string;
  description: string;
  is_active: boolean;
  teacherCount: number;
  created_at: string;
  updated_at: string;
}

interface SubjectListProps {
  initialSubjects?: SubjectType[];
}

export function SubjectList({ initialSubjects = [] }: SubjectListProps) {
  const router = useRouter()
  const { toast } = useToast()
  const [subjects, setSubjects] = useState<Subject[]>(
    initialSubjects.map(subject => ({
      ...subject,
      teacherCount: 0 // We'll update this with the API call
    }))
  )
  const [loading, setLoading] = useState(initialSubjects.length === 0)
  const [error, setError] = useState<string | null>(null)
  const [searchQuery, setSearchQuery] = useState("")
  const [currentPage, setCurrentPage] = useState(1)
  const [totalCount, setTotalCount] = useState(initialSubjects.length)
  const [debouncedSearch, setDebouncedSearch] = useState("")
  const itemsPerPage = 10

  // Handle search input with debounce
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearch(searchQuery)
      setCurrentPage(1) // Reset to first page on new search
    }, 300)

    return () => clearTimeout(timer)
  }, [searchQuery])

  // Fetch subjects data from API when the search term changes
  useEffect(() => {
    // Skip the initial fetch if we have initialSubjects and no search term
    if (initialSubjects.length > 0 && !debouncedSearch && !loading) {
      return
    }
    
    const fetchSubjects = async () => {
      try {
        setLoading(true)
        setError(null)
        
        const params = new URLSearchParams()
        if (debouncedSearch) {
          params.append('search', debouncedSearch)
        }
        
        const response = await fetch(`/api/subjects?${params.toString()}`)
        
        if (!response.ok) {
          throw new Error('Failed to fetch subjects')
        }
        
        const data = await response.json()
        setSubjects(data.subjects || [])
        setTotalCount(data.subjects?.length || 0)
        setLoading(false)
      } catch (err) {
        setError('Error fetching subjects. Please try again later.')
        setLoading(false)
      }
    }

    fetchSubjects()
  }, [debouncedSearch, initialSubjects.length, loading])

  // Handle client-side pagination
  const totalPages = Math.ceil(totalCount / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const paginatedSubjects = subjects.slice(startIndex, startIndex + itemsPerPage)

  // Handle delete subject
  const handleDeleteSubject = async (id: string) => {
    try {
      setLoading(true)
      
      const response = await fetch(`/api/subjects?id=${id}`, {
        method: 'DELETE',
      })
      
      if (!response.ok) {
        throw new Error('Failed to delete subject')
      }
      
      // Update the UI by removing the deleted subject
      setSubjects(subjects.filter(subject => subject.id !== id))
      setTotalCount(prev => prev - 1)
      
      toast({
        title: "Subject berhasil dihapus",
        description: "Subject telah berhasil dihapus dari sistem",
      })
    } catch (err) {
      toast({
        title: "Gagal menghapus subject",
        description: "Terjadi kesalahan saat menghapus subject. Silakan coba lagi.",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="relative w-full max-w-sm">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Cari subject..."
            className="w-full pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            disabled={loading}
          />
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
            disabled={currentPage === 1 || loading}
          >
            <ChevronLeft className="h-4 w-4" />
            Prev
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setCurrentPage((prev) => Math.min(prev + 1, totalPages))}
            disabled={currentPage === totalPages || totalPages === 0 || loading}
          >
            Next
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {loading ? (
        <div className="flex flex-col items-center justify-center py-12 text-center">
          <Loader2 className="h-12 w-12 text-muted-foreground animate-spin" />
          <h3 className="mt-4 text-lg font-semibold">Memuat data...</h3>
          <p className="mt-2 text-sm text-muted-foreground">
            Sedang mengambil data subject dari database
          </p>
        </div>
      ) : error ? (
        <div className="flex flex-col items-center justify-center py-12 text-center">
          <div className="text-destructive">
            <h3 className="text-lg font-semibold">{error}</h3>
            <Button onClick={() => router.refresh()} className="mt-4">
              Coba Lagi
            </Button>
          </div>
        </div>
      ) : subjects.length === 0 ? (
        <div className="flex flex-col items-center justify-center py-12 text-center">
          <BookText className="h-12 w-12 text-muted-foreground" />
          <h3 className="mt-4 text-lg font-semibold">Tidak ada subject</h3>
          <p className="mt-2 text-sm text-muted-foreground">
            {searchQuery 
              ? "Tidak ada subject yang sesuai dengan pencarian Anda" 
              : "Tidak ada subject yang ditemukan. Silakan tambahkan subject baru."}
          </p>
          <Button asChild className="mt-4">
            <Link href="/dashboard/subjects/new">Tambah Subject</Link>
          </Button>
        </div>
      ) : (
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Nama Subject</TableHead>
                <TableHead>Kategori</TableHead>
                <TableHead className="hidden md:table-cell">Deskripsi</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Guru</TableHead>
                <TableHead className="text-right">Aksi</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {paginatedSubjects.map((subject) => (
                <TableRow key={subject.id}>
                  <TableCell className="font-medium">{subject.name}</TableCell>
                  <TableCell>{subject.category}</TableCell>
                  <TableCell className="hidden md:table-cell max-w-[200px] truncate">{subject.description}</TableCell>
                  <TableCell>
                    <Badge variant={subject.is_active ? "default" : "secondary"}>
                      {subject.is_active ? "Aktif" : "Tidak Aktif"}
                    </Badge>
                  </TableCell>
                  <TableCell>{subject.teacherCount} guru</TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end gap-2">
                      <Button variant="ghost" size="icon" asChild>
                        <Link href={`/dashboard/subjects/${subject.id}`}>
                          <Eye className="h-4 w-4" />
                          <span className="sr-only">Lihat</span>
                        </Link>
                      </Button>
                      <Button variant="ghost" size="icon" asChild>
                        <Link href={`/dashboard/subjects/${subject.id}/edit`}>
                          <Pencil className="h-4 w-4" />
                          <span className="sr-only">Edit</span>
                        </Link>
                      </Button>
                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <Button variant="ghost" size="icon">
                            <Trash2 className="h-4 w-4" />
                            <span className="sr-only">Hapus</span>
                          </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle>Hapus Subject</AlertDialogTitle>
                            <AlertDialogDescription>
                              Apakah Anda yakin ingin menghapus subject "{subject.name}"? Tindakan ini tidak dapat
                              dibatalkan.
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel>Batal</AlertDialogCancel>
                            <AlertDialogAction
                              onClick={() => handleDeleteSubject(subject.id)}
                              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                            >
                              Hapus
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      )}

      {subjects.length > 0 && (
        <div className="flex justify-center text-sm text-muted-foreground">
          Halaman {currentPage} dari {totalPages} ({totalCount} subject)
        </div>
      )}
    </div>
  )
}
