"use client"

import { useState } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { z } from "zod"
import { Search, UserCheck } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"

// Form validation schema
const teacherFormSchema = z.object({
  teacherId: z.string().optional(),
  name: z
    .string()
    .min(2, {
      message: "Nama guru harus minimal 2 karakter",
    })
    .optional(),
  role: z.enum(["Guru Utama", "Guru Pendamping", "Guru Pengganti"], {
    required_error: "Peran guru harus dipilih",
  }),
  isPrimary: z.boolean().default(false),
})

type TeacherFormValues = z.infer<typeof teacherFormSchema>

// Mock data for existing teachers
const mockTeachers = [
  {
    id: "t1",
    name: "Ustadz Ahmad",
    subject: "Al-Quran, Hadits",
    avatar: "/placeholder.svg?height=40&width=40",
  },
  {
    id: "t2",
    name: "Ustadzah Fatimah",
    subject: "Bahasa Arab, Akhlak",
    avatar: "/placeholder.svg?height=40&width=40",
  },
  {
    id: "t3",
    name: "Ustadz Mahmud",
    subject: "Fiqih, Tauhid",
    avatar: "/placeholder.svg?height=40&width=40",
  },
  {
    id: "t4",
    name: "Ustadzah Aisyah",
    subject: "Al-Quran, Tahfidz",
    avatar: "/placeholder.svg?height=40&width=40",
  },
  {
    id: "t5",
    name: "Ustadz Ibrahim",
    subject: "Hadits, Sirah",
    avatar: "/placeholder.svg?height=40&width=40",
  },
]

export function AddTeacherForm({ subjectId }: { subjectId: string }) {
  const router = useRouter()
  const [activeTab, setActiveTab] = useState("existing")
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedTeacher, setSelectedTeacher] = useState<string | null>(null)

  // Filter teachers based on search query
  const filteredTeachers = mockTeachers.filter(
    (teacher) =>
      teacher.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      teacher.subject.toLowerCase().includes(searchQuery.toLowerCase()),
  )

  // Initialize form
  const form = useForm<TeacherFormValues>({
    resolver: zodResolver(teacherFormSchema),
    defaultValues: {
      role: "Guru Pendamping",
      isPrimary: false,
    },
  })

  // Handle form submission
  function onSubmit(data: TeacherFormValues) {
    // If using existing teacher, add the selected teacher ID
    if (activeTab === "existing" && selectedTeacher) {
      data.teacherId = selectedTeacher
    }

    console.log("Form submitted:", data)
    // In a real implementation, this would save the data to the database

    // Redirect back to subject detail
    router.push(`/dashboard/subjects/${subjectId}`)
  }

  return (
    <Tabs defaultValue="existing" value={activeTab} onValueChange={setActiveTab}>
      <TabsList className="mb-6">
        <TabsTrigger value="existing">Pilih Guru yang Ada</TabsTrigger>
        <TabsTrigger value="new">Tambah Guru Baru</TabsTrigger>
      </TabsList>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
          <TabsContent value="existing">
            <Card>
              <CardContent className="p-6">
                <div className="mb-6">
                  <div className="relative">
                    <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      type="search"
                      placeholder="Cari guru..."
                      className="pl-8"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                    />
                  </div>
                </div>

                <div className="space-y-4">
                  <RadioGroup value={selectedTeacher || ""} onValueChange={setSelectedTeacher}>
                    {filteredTeachers.length > 0 ? (
                      filteredTeachers.map((teacher) => (
                        <div
                          key={teacher.id}
                          className={`flex items-center justify-between p-4 rounded-lg border ${
                            selectedTeacher === teacher.id ? "border-primary" : ""
                          }`}
                        >
                          <div className="flex items-center gap-4">
                            <RadioGroupItem value={teacher.id} id={teacher.id} />
                            <Avatar>
                              <AvatarImage src={teacher.avatar || "/placeholder.svg"} alt={teacher.name} />
                              <AvatarFallback>{teacher.name.substring(0, 2)}</AvatarFallback>
                            </Avatar>
                            <div>
                              <label htmlFor={teacher.id} className="font-medium cursor-pointer">
                                {teacher.name}
                              </label>
                              <p className="text-sm text-muted-foreground">{teacher.subject}</p>
                            </div>
                          </div>
                        </div>
                      ))
                    ) : (
                      <div className="text-center py-8">
                        <UserCheck className="mx-auto h-12 w-12 text-muted-foreground" />
                        <h3 className="mt-4 text-lg font-semibold">Tidak ada guru</h3>
                        <p className="mt-2 text-sm text-muted-foreground">
                          Tidak ada guru yang ditemukan dengan kriteria pencarian tersebut
                        </p>
                      </div>
                    )}
                  </RadioGroup>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="new">
            <Card>
              <CardContent className="p-6">
                <div className="space-y-6">
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Nama Guru</FormLabel>
                        <FormControl>
                          <Input placeholder="Masukkan nama guru" {...field} />
                        </FormControl>
                        <FormDescription>Nama lengkap guru</FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <Card>
            <CardContent className="p-6">
              <h3 className="text-lg font-medium mb-4">Informasi Penugasan</h3>
              <div className="space-y-6">
                <FormField
                  control={form.control}
                  name="role"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Peran Guru</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Pilih peran guru" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="Guru Utama">Guru Utama</SelectItem>
                          <SelectItem value="Guru Pendamping">Guru Pendamping</SelectItem>
                          <SelectItem value="Guru Pengganti">Guru Pengganti</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormDescription>Peran guru dalam subject ini</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="isPrimary"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                      <div className="space-y-0.5">
                        <FormLabel className="text-base">Guru Utama</FormLabel>
                        <FormDescription>
                          Tetapkan sebagai guru utama yang bertanggung jawab untuk subject ini
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch checked={field.value} onCheckedChange={field.onChange} />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>
            </CardContent>
          </Card>

          <div className="flex gap-4 justify-end">
            <Button type="button" variant="outline" onClick={() => router.push(`/dashboard/subjects/${subjectId}`)}>
              Batal
            </Button>
            <Button
              type="submit"
              disabled={(activeTab === "existing" && !selectedTeacher) || (activeTab === "new" && !form.watch("name"))}
            >
              Simpan
            </Button>
          </div>
        </form>
      </Form>
    </Tabs>
  )
}
