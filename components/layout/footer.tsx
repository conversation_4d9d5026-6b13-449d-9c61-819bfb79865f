export default function Footer() {
  return (
    <footer className="bg-gray-100 border-t py-8">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div>
            <h3 className="font-bold text-lg mb-4 text-emerald-700">PTQ Al I<PERSON></h3>
            <p className="text-gray-600 mb-4">
              Lembaga pendidikan Al-Quran yang mengembangkan generasi Qur'ani dengan pemahaman Islam yang benar.
            </p>
          </div>

          <div>
            <h3 className="font-bold text-lg mb-4 text-emerald-700">Info Kontak</h3>
            <address className="not-italic text-gray-600">
              <p className="mb-2">Jl. Pendidikan No. 123</p>
              <p className="mb-2">Kota Surabaya, 60123</p>
              <p className="mb-2">Email: <EMAIL></p>
              <p>Telepon: (031) 1234-5678</p>
            </address>
          </div>

          <div>
            <h3 className="font-bold text-lg mb-4 text-emerald-700">Tautan</h3>
            <ul className="space-y-2 text-gray-600">
              <li>
                <a href="#" className="hover:text-emerald-700 transition-colors">
                  Tentang Kami
                </a>
              </li>
              <li>
                <a href="#" className="hover:text-emerald-700 transition-colors">
                  Program Pendidikan
                </a>
              </li>
              <li>
                <a href="#" className="hover:text-emerald-700 transition-colors">
                  Berita & Kegiatan
                </a>
              </li>
              <li>
                <a href="#" className="hover:text-emerald-700 transition-colors">
                  Hubungi Kami
                </a>
              </li>
            </ul>
          </div>
        </div>

        <div className="border-t border-gray-200 mt-8 pt-6 text-center text-gray-500 text-sm">
          &copy; {new Date().getFullYear()} PTQ Al Ihsan. Hak Cipta Dilindungi.
        </div>
      </div>
    </footer>
  )
}

