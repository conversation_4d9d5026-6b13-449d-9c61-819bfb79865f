"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Menu, X, ChevronDown } from "lucide-react"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import Image from "next/image"
import { createBrowserClient } from "@/utils/supabase/client"
import { Session } from "@supabase/supabase-js"
import { useFeatureFlags } from "@/hooks/use-website-settings"

export default function Navigation({ initialSession = null }) {
  const [session, setSession] = useState<Session | null>(initialSession)
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const pathname = usePathname()

  // Load feature flags
  const featureFlags = useFeatureFlags()
  
  // Check session on client-side
  useEffect(() => {
    const supabase = createBrowserClient()
    
    // Get initial session if not provided
    if (!initialSession) {
      supabase.auth.getSession().then(({ data: { session } }) => {
        return setSession(session)
      })
    }
    
    // Listen for auth changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange((_event, session) => {
      setSession(session)
    })
    
    return () => subscription.unsubscribe()
  }, [initialSession])

  const isActive = (path: string) => {
    return pathname === path
  }

  // Build navigation items based on feature flags
  const navItems = [
    { name: "Beranda", href: "/" },
    { name: "Tentang Kami", href: "/tentang-kami" },
    {
      name: "Program",
      href: "/program",
      children: [
        { name: "Program Unggulan", href: "/program" },
        { name: "Ekstrakurikuler", href: "/program#ekstrakurikuler" },
        { name: "Jadwal Kegiatan", href: "/program#jadwal" },
      ],
    },
    ...(featureFlags.ppdbMenu ? [{ name: "PPDB", href: "/ppdb" }] : []),
    ...(featureFlags.blogMenu ? [{ name: "Blog", href: "/blog" }] : []),
    { name: "Kontak", href: "/kontak" },
  ]

  return (
    <header className="sticky top-0 z-50 bg-gradient-to-r from-emerald-700 to-emerald-800 text-white shadow-lg">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16 md:h-20">
          {/* Logo and title */}
          <Link href="/" className="flex items-center gap-3">
            <div className="bg-white/10 p-2 rounded-full relative h-10 w-10">
              <Image
                src="/logo.svg"
                alt="Logo"
                fill
                className="object-contain p-1"
                priority
              />
            </div>
            <div className="hidden md:block">
              <h1 className="font-bold text-xl">PTQ Al Ihsan</h1>
              <p className="text-xs text-white/70">Ungaran</p>
            </div>
          </Link>

          {/* Mobile menu button */}
          <button
            className="md:hidden p-2"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            aria-label={isMenuOpen ? "Close menu" : "Open menu"}
          >
            {isMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
          </button>

          {/* Desktop navigation */}
          <div className="hidden md:flex items-center gap-6">
            <nav className="flex gap-1">
              {navItems.map((item) =>
                item.children ? (
                  <DropdownMenu key={item.name}>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="ghost"
                        className={`text-white hover:bg-white/10 flex items-center gap-1 ${isActive(item.href) ? "bg-white/10" : ""}`}
                      >
                        {item.name}
                        <ChevronDown className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="center" className="w-48">
                      {item.children.map((child) => (
                        <DropdownMenuItem key={child.name} asChild>
                          <Link href={child.href}>{child.name}</Link>
                        </DropdownMenuItem>
                      ))}
                    </DropdownMenuContent>
                  </DropdownMenu>
                ) : (
                  <Button
                    key={item.name}
                    variant="ghost"
                    asChild
                    className={`text-white hover:bg-white/10 ${isActive(item.href) ? "bg-white/10" : ""}`}
                  >
                    <Link href={item.href}>{item.name}</Link>
                  </Button>
                ),
              )}
            </nav>

            <div className="flex gap-3">
              {featureFlags.parentDashboard && (
                <Button
                  asChild
                  variant="outline"
                  className="text-white border-white/20 hover:bg-white/10 hover:text-white"
                >
                  <Link href={session ? "/dashboard" : "/login"}>
                    {session ? "Dashboard" : "Portal Orang Tua"}
                  </Link>
                </Button>
              )}
              {featureFlags.ppdbMenu && (
                <Button asChild className="bg-white text-emerald-800 hover:bg-emerald-100">
                  <Link href="/ppdb">Daftar Sekarang</Link>
                </Button>
              )}
            </div>
          </div>
        </div>

        {/* Mobile navigation */}
        {isMenuOpen && (
          <div className="md:hidden py-4 border-t border-white/10">
            <nav className="flex flex-col gap-2">
              {navItems.map((item) => (
                <div key={item.name}>
                  {item.children ? (
                    <>
                      <div className="px-4 py-2 font-medium">{item.name}</div>
                      <div className="pl-6 border-l border-white/10 ml-4 mb-2">
                        {item.children.map((child) => (
                          <Link
                            key={child.name}
                            href={child.href}
                            className="block px-4 py-2 hover:bg-white/10 rounded-md"
                            onClick={() => setIsMenuOpen(false)}
                          >
                            {child.name}
                          </Link>
                        ))}
                      </div>
                    </>
                  ) : (
                    <Link
                      href={item.href}
                      className={`px-4 py-2 hover:bg-white/10 rounded-md block ${
                        isActive(item.href) ? "bg-white/10 font-medium" : ""
                      }`}
                      onClick={() => setIsMenuOpen(false)}
                    >
                      {item.name}
                    </Link>
                  )}
                </div>
              ))}
              <div className="border-t border-white/10 my-2"></div>
              {featureFlags.parentDashboard && (
                <Link
                  href={session ? "/dashboard" : "/login"}
                  className="px-4 py-2 hover:bg-white/10 rounded-md block"
                  onClick={() => setIsMenuOpen(false)}
                >
                  {session ? "Dashboard" : "Portal Orang Tua"}
                </Link>
              )}
              {featureFlags.ppdbMenu && (
                <Link
                  href="/ppdb"
                  className="mx-4 mt-2 py-2 bg-white text-emerald-800 rounded-md block text-center font-medium"
                  onClick={() => setIsMenuOpen(false)}
                >
                  Daftar Sekarang
                </Link>
              )}
            </nav>
          </div>
        )}
      </div>
    </header>
  )
}

