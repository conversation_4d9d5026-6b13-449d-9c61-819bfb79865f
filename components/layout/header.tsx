"use client"

import Link from "next/link"
import Image from 'next/image'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { GraduationCap, User, Bell, Menu } from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { useState } from "react"

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)

  return (
    <header className="sticky top-0 z-50 bg-gradient-to-r from-emerald-700 to-emerald-800 text-white shadow-lg">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16 md:h-20">
          {/* Logo and title */}
          <Link href="/dashboard" className="flex items-center gap-3">
            <div className="bg-white/10 p-2 rounded-full">
            <Image
                src="/logo.svg"
                alt="Logo"
                fill
                className="object-contain"
                priority
              />
            </div>
            <div className="hidden md:block">
              <h1 className="font-bold text-xl">PTQ Al Ihsan</h1>
              <p className="text-xs text-white/70">Portal Orang Tua</p>
            </div>
          </Link>

          {/* Mobile menu button */}
          <button className="md:hidden p-2" onClick={() => setIsMenuOpen(!isMenuOpen)}>
            <Menu className="h-6 w-6" />
          </button>

          {/* Desktop navigation */}
          <div className="hidden md:flex items-center gap-6">
            <nav className="flex gap-3">
              <Button variant="ghost" asChild className="text-white hover:bg-white/10">
                <Link href="/dashboard">Dashboard</Link>
              </Button>
              <Button variant="ghost" asChild className="text-white hover:bg-white/10">
                <Link href="#">Jadwal</Link>
              </Button>
              <Button variant="ghost" asChild className="text-white hover:bg-white/10">
                <Link href="#">Pengumuman</Link>
              </Button>
            </nav>

            <div className="flex items-center gap-2">
              <Button variant="ghost" size="icon" className="text-white hover:bg-white/10 relative">
                <Bell className="h-5 w-5" />
                <span className="absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full"></span>
              </Button>

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="relative h-10 w-10 rounded-full">
                    <Avatar className="h-10 w-10 border-2 border-white/20">
                      <AvatarImage src="/placeholder.svg?height=40&width=40" alt="User avatar" />
                      <AvatarFallback>OT</AvatarFallback>
                    </Avatar>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-56" align="end">
                  <div className="flex items-center justify-start gap-2 p-2">
                    <div className="flex flex-col space-y-1 leading-none">
                      <p className="font-medium">Orang Tua Santri</p>
                      <p className="text-sm text-muted-foreground"><EMAIL></p>
                    </div>
                  </div>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem asChild>
                    <Link href="/profile">
                      <User className="mr-2 h-4 w-4" />
                      <span>Profil</span>
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link href="/settings">
                      <User className="mr-2 h-4 w-4" />
                      <span>Pengaturan</span>
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem>
                    <Link href="/logout" className="w-full flex items-center">
                      Keluar
                    </Link>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </div>

        {/* Mobile navigation */}
        {isMenuOpen && (
          <div className="md:hidden py-4 border-t border-white/10">
            <nav className="flex flex-col gap-2">
              <Link
                href="/dashboard"
                className="px-4 py-2 hover:bg-white/10 rounded-md"
                onClick={() => setIsMenuOpen(false)}
              >
                Dashboard
              </Link>
              <Link href="#" className="px-4 py-2 hover:bg-white/10 rounded-md" onClick={() => setIsMenuOpen(false)}>
                Jadwal
              </Link>
              <Link href="#" className="px-4 py-2 hover:bg-white/10 rounded-md" onClick={() => setIsMenuOpen(false)}>
                Pengumuman
              </Link>
              <div className="border-t border-white/10 my-2"></div>
              <Link
                href="/profile"
                className="px-4 py-2 hover:bg-white/10 rounded-md"
                onClick={() => setIsMenuOpen(false)}
              >
                Profil
              </Link>
              <Link
                href="/settings"
                className="px-4 py-2 hover:bg-white/10 rounded-md"
                onClick={() => setIsMenuOpen(false)}
              >
                Pengaturan
              </Link>
              <Link
                href="/logout"
                className="px-4 py-2 hover:bg-white/10 rounded-md"
                onClick={() => setIsMenuOpen(false)}
              >
                Keluar
              </Link>
            </nav>
          </div>
        )}
      </div>
    </header>
  )
}

