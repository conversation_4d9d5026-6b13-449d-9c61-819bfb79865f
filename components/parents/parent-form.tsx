"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Loader2, Save, User } from "lucide-react"

const parentFormSchema = z.object({
  name: z.string().min(1, "Nama wajib diisi"),
  phone: z.string().optional(),
  email: z.string().email("Format email tidak valid").optional().or(z.literal("")),
  address: z.string().optional(),
  occupation: z.string().optional(),
})

type ParentFormValues = z.infer<typeof parentFormSchema>

interface ParentFormProps {
  parentId?: string
}

export function ParentForm({ parentId }: ParentFormProps) {
  const router = useRouter()
  const [loading, setLoading] = useState(false)
  const [loadingData, setLoadingData] = useState(!!parentId)
  
  const isEditMode = !!parentId

  const form = useForm<ParentFormValues>({
    resolver: zodResolver(parentFormSchema),
    defaultValues: {
      name: "",
      phone: "",
      email: "",
      address: "",
      occupation: "",
    },
  })

  // Fetch parent data for edit mode
  useEffect(() => {
    const fetchParentData = async () => {
      if (isEditMode && parentId) {
        setLoadingData(true)
        try {
          console.log("Fetching parent data for:", parentId)

          const response = await fetch(`/api/parents?id=${parentId}`)
          if (response.ok) {
            const data = await response.json()
            console.log("API response:", data)

            // Handle different response formats
            const parent = data.parent || data.parents?.[0] || data

            console.log("Loaded parent data:", parent)

            if (parent && parent.name) {
              // Populate form with parent data
              form.setValue("name", parent.name || "")
              form.setValue("phone", parent.phone || "")
              form.setValue("email", parent.email || "")
              form.setValue("address", parent.address || "")
              form.setValue("occupation", parent.occupation || "")
            } else {
              console.error("Parent data not found in response:", data)
            }
          } else {
            console.error("Failed to fetch parent data:", response.status)
          }
        } catch (error) {
          console.error("Error fetching parent data:", error)
        } finally {
          setLoadingData(false)
        }
      }
    }

    fetchParentData()
  }, [isEditMode, parentId, form])

  const onSubmit = async (values: ParentFormValues) => {
    setLoading(true)
    try {
      console.log("Submitting parent form:", values)

      const url = isEditMode ? `/api/parents?id=${parentId}` : '/api/parents'
      const method = isEditMode ? 'PUT' : 'POST'

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(values),
      })

      if (response.ok) {
        const result = await response.json()
        console.log("Parent form submitted successfully:", result)
        
        if (isEditMode) {
          router.push(`/dashboard/parents/${parentId}`)
        } else {
          router.push('/dashboard/parents')
        }
      } else {
        const error = await response.json()
        console.error("Failed to submit parent form:", error)
        alert(`Gagal ${isEditMode ? 'mengupdate' : 'menambahkan'} data orangtua: ${error.error}`)
      }
    } catch (error) {
      console.error("Error submitting parent form:", error)
      alert(`Terjadi kesalahan saat ${isEditMode ? 'mengupdate' : 'menambahkan'} data orangtua`)
    } finally {
      setLoading(false)
    }
  }

  if (loadingData) {
    return (
      <div className="flex items-center justify-center py-12">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Memuat data orangtua...</span>
      </div>
    )
  }

  return (
    <div className="max-w-2xl mx-auto">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                {isEditMode ? "Edit Data Orangtua" : "Tambah Orangtua Baru"}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Nama Lengkap *</FormLabel>
                    <FormControl>
                      <Input placeholder="Masukkan nama lengkap" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="phone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Nomor Telepon</FormLabel>
                      <FormControl>
                        <Input placeholder="Contoh: 08123456789" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email</FormLabel>
                      <FormControl>
                        <Input placeholder="Contoh: <EMAIL>" type="email" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="occupation"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Pekerjaan</FormLabel>
                    <FormControl>
                      <Input placeholder="Contoh: Guru, Dokter, Wiraswasta" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="address"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Alamat</FormLabel>
                    <FormControl>
                      <Textarea 
                        placeholder="Masukkan alamat lengkap"
                        className="min-h-[100px]"
                        {...field} 
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          <div className="flex gap-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.back()}
              disabled={loading}
              className="flex-1"
            >
              Batal
            </Button>
            <Button type="submit" disabled={loading} className="flex-1">
              {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              <Save className="mr-2 h-4 w-4" />
              {isEditMode ? "Update Data" : "Simpan Data"}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  )
}
