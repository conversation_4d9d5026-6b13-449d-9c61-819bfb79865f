"use client"

import { useState, useCallback } from "react"
import { useDropzone } from "react-dropzone"
import { Upload, Download, FileSpreadsheet, AlertCircle, CheckCircle, X, Loader2 } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"

interface ExcelImportProps {
  classId: string
  className: string
  month: string
  year: string
  onImportSuccess: () => void
}

interface ImportResult {
  student_id: string
  student_name: string
  timeline_entry_id: string
  status: string
}

interface ImportResponse {
  message: string
  results: ImportResult[]
  errors: string[]
  summary: {
    total_rows: number
    successful: number
    failed: number
  }
}

export default function ExcelImport({ classId, className, month, year, onImportSuccess }: ExcelImportProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [isUploading, setIsUploading] = useState(false)
  const [isDownloadingTemplate, setIsDownloadingTemplate] = useState(false)
  const [importResult, setImportResult] = useState<ImportResponse | null>(null)
  const [uploadedFile, setUploadedFile] = useState<File | null>(null)

  // Download template
  const handleDownloadTemplate = async () => {
    setIsDownloadingTemplate(true)
    try {
      const response = await fetch(`/api/timeline/template?class_id=${classId}`)
      if (response.ok) {
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `Template_Timeline_${className}_${month}_${year}.xlsx`
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)
      } else {
        alert('Gagal mengunduh template')
      }
    } catch (error) {
      console.error('Error downloading template:', error)
      alert('Terjadi kesalahan saat mengunduh template')
    } finally {
      setIsDownloadingTemplate(false)
    }
  }

  // Handle file drop
  const onDrop = useCallback((acceptedFiles: File[]) => {
    const file = acceptedFiles[0]
    if (file) {
      setUploadedFile(file)
      setImportResult(null)
    }
  }, [])

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
      'application/vnd.ms-excel': ['.xls']
    },
    maxFiles: 1
  })

  // Handle import
  const handleImport = async () => {
    if (!uploadedFile) return

    setIsUploading(true)
    try {
      const formData = new FormData()
      formData.append('file', uploadedFile)
      formData.append('class_id', classId)
      formData.append('month', month)
      formData.append('year', year)

      const response = await fetch('/api/timeline/import', {
        method: 'POST',
        body: formData
      })

      const result = await response.json()
      
      if (response.ok) {
        setImportResult(result)
        if (result.summary.successful > 0) {
          onImportSuccess()
        }
      } else {
        alert(`Gagal import: ${result.error}`)
      }
    } catch (error) {
      console.error('Error importing file:', error)
      alert('Terjadi kesalahan saat import file')
    } finally {
      setIsUploading(false)
    }
  }

  // Reset state when dialog closes
  const handleOpenChange = (open: boolean) => {
    setIsOpen(open)
    if (!open) {
      setUploadedFile(null)
      setImportResult(null)
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogTrigger asChild>
        <Button variant="outline" className="gap-2">
          <FileSpreadsheet className="h-4 w-4" />
          Import Excel
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Import Timeline dari Excel</DialogTitle>
          <DialogDescription>
            Import data timeline untuk {className} - {month} {year}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Download Template Section */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm">1. Download Template</CardTitle>
              <CardDescription>
                Download template Excel yang sudah berisi daftar siswa dan format yang benar
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button 
                onClick={handleDownloadTemplate}
                disabled={isDownloadingTemplate}
                className="w-full gap-2"
              >
                {isDownloadingTemplate ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Download className="h-4 w-4" />
                )}
                Download Template Excel
              </Button>
            </CardContent>
          </Card>

          {/* Upload Section */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm">2. Upload File Excel</CardTitle>
              <CardDescription>
                Upload file Excel yang sudah diisi dengan data timeline
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div
                {...getRootProps()}
                className={`border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors ${
                  isDragActive 
                    ? 'border-emerald-500 bg-emerald-50' 
                    : 'border-gray-300 hover:border-emerald-400'
                }`}
              >
                <input {...getInputProps()} />
                <Upload className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                {uploadedFile ? (
                  <div>
                    <p className="text-sm font-medium text-emerald-600">{uploadedFile.name}</p>
                    <p className="text-xs text-gray-500">
                      {(uploadedFile.size / 1024 / 1024).toFixed(2)} MB
                    </p>
                  </div>
                ) : (
                  <div>
                    <p className="text-sm text-gray-600">
                      {isDragActive 
                        ? 'Drop file Excel di sini...' 
                        : 'Drag & drop file Excel atau klik untuk pilih file'
                      }
                    </p>
                    <p className="text-xs text-gray-400 mt-1">
                      Mendukung format .xlsx dan .xls
                    </p>
                  </div>
                )}
              </div>

              {uploadedFile && (
                <div className="mt-4 flex gap-2">
                  <Button 
                    onClick={handleImport}
                    disabled={isUploading}
                    className="flex-1 gap-2"
                  >
                    {isUploading ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <Upload className="h-4 w-4" />
                    )}
                    {isUploading ? 'Mengimport...' : 'Import Data'}
                  </Button>
                  <Button 
                    variant="outline" 
                    onClick={() => setUploadedFile(null)}
                    disabled={isUploading}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Import Result */}
          {importResult && (
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm flex items-center gap-2">
                  {importResult.summary.failed === 0 ? (
                    <CheckCircle className="h-4 w-4 text-green-500" />
                  ) : (
                    <AlertCircle className="h-4 w-4 text-amber-500" />
                  )}
                  Hasil Import
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Summary */}
                <div className="flex gap-2">
                  <Badge variant="outline" className="text-blue-600">
                    Total: {importResult.summary.total_rows}
                  </Badge>
                  <Badge variant="outline" className="text-green-600">
                    Berhasil: {importResult.summary.successful}
                  </Badge>
                  {importResult.summary.failed > 0 && (
                    <Badge variant="outline" className="text-red-600">
                      Gagal: {importResult.summary.failed}
                    </Badge>
                  )}
                </div>

                {/* Success Message */}
                {importResult.summary.successful > 0 && (
                  <Alert>
                    <CheckCircle className="h-4 w-4" />
                    <AlertDescription>
                      {importResult.summary.successful} data timeline berhasil diimport!
                    </AlertDescription>
                  </Alert>
                )}

                {/* Errors */}
                {importResult.errors.length > 0 && (
                  <div>
                    <h4 className="text-sm font-medium text-red-600 mb-2">Error:</h4>
                    <ScrollArea className="h-32 w-full border rounded p-2">
                      <div className="space-y-1">
                        {importResult.errors.map((error, index) => (
                          <p key={index} className="text-xs text-red-600">
                            {error}
                          </p>
                        ))}
                      </div>
                    </ScrollArea>
                  </div>
                )}

                {/* Successful imports */}
                {importResult.results.length > 0 && (
                  <div>
                    <h4 className="text-sm font-medium text-green-600 mb-2">
                      Berhasil diimport ({importResult.results.length}):
                    </h4>
                    <ScrollArea className="h-32 w-full border rounded p-2">
                      <div className="space-y-1">
                        {importResult.results.map((result, index) => (
                          <p key={index} className="text-xs text-green-600">
                            ✓ {result.student_name}
                          </p>
                        ))}
                      </div>
                    </ScrollArea>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Instructions */}
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription className="text-xs">
              <strong>Petunjuk:</strong> Download template terlebih dahulu, isi data sesuai format, 
              lalu upload kembali. Pastikan tidak mengubah header kolom dan hapus baris contoh sebelum import.
            </AlertDescription>
          </Alert>
        </div>
      </DialogContent>
    </Dialog>
  )
}
