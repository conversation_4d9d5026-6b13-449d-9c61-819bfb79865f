"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { 
  GraduationCap, 
  Users, 
  User, 
  Calendar, 
  Edit, 
  Trash2, 
  Search,
  Eye,
  BookOpen
} from "lucide-react"
import Link from "next/link"
import { formatDistanceToNow } from "date-fns"

type Class = {
  id: string
  class_id: string
  name: string
  level: string
  academic_year: string
  homeroom_teacher_id: string | null
  created_at: string
  updated_at: string
  studentCount: number
  homeroomTeacher: {
    id: string
    name: string
    email: string
  } | null
}

interface ClassListProps {
  initialClasses: Class[]
}

export function ClassList({ initialClasses }: ClassListProps) {
  const [classes, setClasses] = useState<Class[]>(initialClasses)
  const [searchQuery, setSearchQuery] = useState("")
  const [loading, setLoading] = useState(false)

  // Filter classes based on search query
  const filteredClasses = classes.filter(cls =>
    cls.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    cls.level.toLowerCase().includes(searchQuery.toLowerCase()) ||
    cls.academic_year.includes(searchQuery) ||
    cls.class_id.toLowerCase().includes(searchQuery.toLowerCase()) ||
    (cls.homeroomTeacher?.name.toLowerCase().includes(searchQuery.toLowerCase()))
  )

  // Group classes by level
  const groupedClasses = filteredClasses.reduce((acc, cls) => {
    if (!acc[cls.level]) {
      acc[cls.level] = []
    }
    acc[cls.level].push(cls)
    return acc
  }, {} as Record<string, Class[]>)

  const handleDelete = async (classId: string) => {
    if (!confirm("Apakah Anda yakin ingin menghapus kelas ini?")) {
      return
    }

    setLoading(true)
    try {
      const response = await fetch(`/api/classes?id=${classId}`, {
        method: 'DELETE',
      })

      if (response.ok) {
        setClasses(prev => prev.filter(c => c.id !== classId))
      } else {
        alert("Gagal menghapus kelas")
      }
    } catch (error) {
      console.error("Error deleting class:", error)
      alert("Terjadi kesalahan saat menghapus kelas")
    } finally {
      setLoading(false)
    }
  }

  const getLevelColor = (level: string) => {
    switch (level.toLowerCase()) {
      case 'sd': return 'bg-green-100 text-green-800'
      case 'smp': return 'bg-blue-100 text-blue-800'
      case 'sma': return 'bg-purple-100 text-purple-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <div className="space-y-6">
      {/* Search Bar */}
      <div className="flex items-center space-x-2">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            placeholder="Cari kelas berdasarkan nama, tingkat, tahun ajaran, atau wali kelas..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <BookOpen className="h-8 w-8 text-blue-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-muted-foreground">Total Kelas</p>
                <p className="text-2xl font-bold">{classes.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Users className="h-8 w-8 text-green-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-muted-foreground">Total Siswa</p>
                <p className="text-2xl font-bold">{classes.reduce((sum, cls) => sum + cls.studentCount, 0)}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <User className="h-8 w-8 text-purple-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-muted-foreground">Dengan Wali Kelas</p>
                <p className="text-2xl font-bold">{classes.filter(c => c.homeroomTeacher).length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Search className="h-8 w-8 text-orange-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-muted-foreground">Hasil Pencarian</p>
                <p className="text-2xl font-bold">{filteredClasses.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Classes by Level */}
      {Object.keys(groupedClasses).length > 0 ? (
        Object.entries(groupedClasses).map(([level, levelClasses]) => (
          <div key={level} className="space-y-4">
            <div className="flex items-center gap-2">
              <h2 className="text-xl font-semibold">{level.toUpperCase()}</h2>
              <Badge variant="secondary">{levelClasses.length} kelas</Badge>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {levelClasses.map((cls) => (
                <Card key={cls.id} className="hover:shadow-lg transition-shadow">
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="p-2 bg-blue-100 rounded-lg">
                          <GraduationCap className="h-6 w-6 text-blue-600" />
                        </div>
                        <div>
                          <CardTitle className="text-lg">{cls.name}</CardTitle>
                          <div className="flex items-center gap-2">
                            <Badge className={getLevelColor(cls.level)}>
                              {cls.level}
                            </Badge>
                            <Badge variant="outline">
                              {cls.studentCount} siswa
                            </Badge>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="space-y-2">
                      <div className="flex items-center text-sm text-muted-foreground">
                        <BookOpen className="h-4 w-4 mr-2" />
                        ID: {cls.class_id}
                      </div>
                      <div className="flex items-center text-sm text-muted-foreground">
                        <Calendar className="h-4 w-4 mr-2" />
                        {cls.academic_year}
                      </div>
                      <div className="flex items-center text-sm text-muted-foreground">
                        <User className="h-4 w-4 mr-2" />
                        {cls.homeroomTeacher ? cls.homeroomTeacher.name : "Belum ada wali kelas"}
                      </div>
                    </div>

                    <div className="text-xs text-muted-foreground">
                      Dibuat {formatDistanceToNow(new Date(cls.created_at), {
                        addSuffix: true
                      })}
                    </div>

                    <div className="flex gap-2 pt-2">
                      <Button variant="outline" size="sm" asChild className="flex-1">
                        <Link href={`/dashboard/classes/${cls.id}`}>
                          <Eye className="h-4 w-4 mr-1" />
                          Detail
                        </Link>
                      </Button>
                      <Button variant="outline" size="sm" asChild className="flex-1">
                        <Link href={`/dashboard/classes/${cls.id}/edit`}>
                          <Edit className="h-4 w-4 mr-1" />
                          Edit
                        </Link>
                      </Button>
                      <Button 
                        variant="outline" 
                        size="sm" 
                        onClick={() => handleDelete(cls.id)}
                        disabled={loading}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        ))
      ) : (
        <div className="text-center py-12">
          <BookOpen className="h-12 w-12 mx-auto text-muted-foreground" />
          <h3 className="mt-4 text-lg font-medium">Tidak ada kelas ditemukan</h3>
          <p className="mt-2 text-sm text-muted-foreground">
            {searchQuery ? "Coba ubah kata kunci pencarian" : "Mulai dengan menambahkan kelas baru"}
          </p>
        </div>
      )}
    </div>
  )
}
