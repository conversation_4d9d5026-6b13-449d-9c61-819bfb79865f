"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { 
  User, 
  Mail, 
  GraduationCap, 
  Users, 
  Edit, 
  Trash2, 
  Search,
  Eye,
  UserCheck
} from "lucide-react"
import Link from "next/link"
import { formatDistanceToNow } from "date-fns"

type Teacher = {
  id: string
  teacher_id: string
  name: string
  specialization: string
  photo_url: string
  join_date: string
  status: string
  created_at: string
  updated_at: string
  classCount: number
}

interface TeacherListProps {
  initialTeachers: Teacher[]
}

export function TeacherList({ initialTeachers }: TeacherListProps) {
  const [teachers, setTeachers] = useState<Teacher[]>(initialTeachers)
  const [searchQuery, setSearchQuery] = useState("")
  const [loading, setLoading] = useState(false)

  // Filter teachers based on search query
  const filteredTeachers = teachers.filter(teacher =>
    teacher.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    teacher.teacher_id.toLowerCase().includes(searchQuery.toLowerCase()) ||
    teacher.specialization?.toLowerCase().includes(searchQuery.toLowerCase())
  )

  const handleDelete = async (teacherId: string) => {
    if (!confirm("Apakah Anda yakin ingin menghapus data guru ini?")) {
      return
    }

    setLoading(true)
    try {
      const response = await fetch(`/api/teachers?id=${teacherId}`, {
        method: 'DELETE',
      })

      if (response.ok) {
        setTeachers(prev => prev.filter(t => t.id !== teacherId))
      } else {
        alert("Gagal menghapus data guru")
      }
    } catch (error) {
      console.error("Error deleting teacher:", error)
      alert("Terjadi kesalahan saat menghapus data")
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* Search Bar */}
      <div className="flex items-center space-x-2">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            placeholder="Cari guru berdasarkan nama atau email..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <UserCheck className="h-8 w-8 text-blue-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-muted-foreground">Total Guru</p>
                <p className="text-2xl font-bold">{teachers.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <GraduationCap className="h-8 w-8 text-green-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-muted-foreground">Wali Kelas</p>
                <p className="text-2xl font-bold">{teachers.filter(t => t.classCount > 0).length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Search className="h-8 w-8 text-purple-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-muted-foreground">Hasil Pencarian</p>
                <p className="text-2xl font-bold">{filteredTeachers.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Teacher Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredTeachers.map((teacher) => (
          <Card key={teacher.id} className="hover:shadow-lg transition-shadow">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <Avatar className="h-12 w-12">
                    <AvatarImage
                      src={teacher.photo_url || `/placeholder.svg?height=48&width=48`}
                      alt={teacher.name}
                      onError={(e) => {
                        // Fallback to placeholder if image fails to load
                        e.currentTarget.src = `/placeholder.svg?height=48&width=48`
                      }}
                    />
                    <AvatarFallback>
                      {teacher.name.split(' ').map(n => n[0]).join('').toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <CardTitle className="text-lg">{teacher.name}</CardTitle>
                    <div className="flex items-center gap-2">
                      <Badge variant={teacher.classCount > 0 ? "default" : "secondary"}>
                        {teacher.classCount > 0 ? `Wali ${teacher.classCount} Kelas` : "Guru"}
                      </Badge>
                    </div>
                  </div>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="space-y-2">
                <div className="flex items-center text-sm text-muted-foreground">
                  <Mail className="h-4 w-4 mr-2" />
                  {teacher.teacher_id}
                </div>
                <div className="flex items-center text-sm text-muted-foreground">
                  <GraduationCap className="h-4 w-4 mr-2" />
                  {teacher.specialization || "Belum ada spesialisasi"}
                </div>
                <div className="flex items-center text-sm text-muted-foreground">
                  <UserCheck className="h-4 w-4 mr-2" />
                  {teacher.classCount > 0 ? `Wali ${teacher.classCount} kelas` : "Belum menjadi wali kelas"}
                </div>

              </div>

              <div className="text-xs text-muted-foreground">
                Terdaftar {formatDistanceToNow(new Date(teacher.created_at), { 
                  addSuffix: true
                })}
              </div>

              <div className="flex gap-2 pt-2">
                <Button variant="outline" size="sm" asChild className="flex-1">
                  <Link href={`/dashboard/teachers/${teacher.id}`}>
                    <Eye className="h-4 w-4 mr-1" />
                    Detail
                  </Link>
                </Button>
                <Button variant="outline" size="sm" asChild className="flex-1">
                  <Link href={`/dashboard/teachers/${teacher.id}/edit`}>
                    <Edit className="h-4 w-4 mr-1" />
                    Edit
                  </Link>
                </Button>
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={() => handleDelete(teacher.id)}
                  disabled={loading}
                  className="text-red-600 hover:text-red-700"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredTeachers.length === 0 && (
        <div className="text-center py-12">
          <UserCheck className="h-12 w-12 mx-auto text-muted-foreground" />
          <h3 className="mt-4 text-lg font-medium">Tidak ada guru ditemukan</h3>
          <p className="mt-2 text-sm text-muted-foreground">
            {searchQuery ? "Coba ubah kata kunci pencarian" : "Mulai dengan menambahkan guru baru"}
          </p>
        </div>
      )}
    </div>
  )
}
