"use client"

import { useState, useRef } from "react"
import Image from "next/image"
import { Upload, X, Loader2, ImageIcon } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"

interface ImageUploadProps {
  imageKey: string
  category?: string
  currentImage?: string
  altText?: string
  caption?: string
  onUploadSuccess?: (imageData: any) => void
  onUploadError?: (error: string) => void
  className?: string
  label?: string
  description?: string
}

export function ImageUpload({
  imageKey,
  category = "general",
  currentImage,
  altText: initialAltText = "",
  caption: initialCaption = "",
  onUploadSuccess,
  onUploadError,
  className = "",
  label = "Upload Gambar",
  description = "Pilih gambar untuk diupload (max 5MB)"
}: ImageUploadProps) {
  const [uploading, setUploading] = useState(false)
  const [preview, setPreview] = useState<string | null>(currentImage || null)
  const [altText, setAltText] = useState(initialAltText)
  const [caption, setCaption] = useState(initialCaption)
  const [error, setError] = useState<string | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif']
    if (!allowedTypes.includes(file.type)) {
      setError('Tipe file tidak valid. Hanya JPEG, PNG, WebP, dan GIF yang diizinkan.')
      return
    }

    // Validate file size (max 5MB)
    const maxSize = 5 * 1024 * 1024 // 5MB
    if (file.size > maxSize) {
      setError('Ukuran file terlalu besar. Maksimal 5MB.')
      return
    }

    setError(null)

    // Create preview
    const reader = new FileReader()
    reader.onload = (e) => {
      setPreview(e.target?.result as string)
    }
    reader.readAsDataURL(file)
  }

  const handleUpload = async () => {
    const file = fileInputRef.current?.files?.[0]
    if (!file) {
      setError('Pilih file terlebih dahulu')
      return
    }

    setUploading(true)
    setError(null)

    try {
      const formData = new FormData()
      formData.append('file', file)
      formData.append('key', imageKey)
      formData.append('category', category)
      formData.append('altText', altText)
      formData.append('caption', caption)

      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Upload gagal')
      }

      console.log('Upload API response:', data)

      // Set preview URL
      const imageUrl = data.url || data.image?.file_path || data.file_path
      if (imageUrl) {
        setPreview(imageUrl)
      }

      // Call success callback with consistent data format
      onUploadSuccess?.({
        file_path: data.image?.file_path || data.url || data.file_path,
        url: data.url || data.image?.file_path || data.file_path,
        image: data.image,
        ...data
      })

    } catch (error: any) {
      console.error('Upload error:', error)
      const errorMessage = error.message || 'Terjadi kesalahan saat upload'
      setError(errorMessage)
      onUploadError?.(errorMessage)
    } finally {
      setUploading(false)
    }
  }

  const handleRemove = () => {
    setPreview(null)
    setAltText("")
    setCaption("")
    if (fileInputRef.current) {
      fileInputRef.current.value = ""
    }
  }

  const triggerFileSelect = () => {
    fileInputRef.current?.click()
  }

  return (
    <div className={`space-y-4 ${className}`}>
      <div>
        <Label className="text-sm font-medium">{label}</Label>
        {description && (
          <p className="text-sm text-gray-500 mt-1">{description}</p>
        )}
      </div>

      {/* File Input */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleFileSelect}
        className="hidden"
      />

      {/* Preview or Upload Area */}
      {preview ? (
        <Card>
          <CardContent className="p-4">
            <div className="relative">
              <div className="relative w-full h-48 rounded-lg overflow-hidden bg-gray-100">
                <Image
                  src={preview}
                  alt={altText || "Preview"}
                  fill
                  className="object-cover"
                />
              </div>
              <Button
                variant="destructive"
                size="sm"
                className="absolute top-2 right-2"
                onClick={handleRemove}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </CardContent>
        </Card>
      ) : (
        <Card>
          <CardContent className="p-6">
            <div
              className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center cursor-pointer hover:border-emerald-400 transition-colors"
              onClick={triggerFileSelect}
            >
              <ImageIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600 mb-2">Klik untuk memilih gambar</p>
              <p className="text-sm text-gray-500">atau drag & drop file di sini</p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Alt Text and Caption */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor={`alt-${imageKey}`}>Alt Text</Label>
          <Input
            id={`alt-${imageKey}`}
            value={altText}
            onChange={(e) => setAltText(e.target.value)}
            placeholder="Deskripsi gambar untuk aksesibilitas"
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor={`caption-${imageKey}`}>Caption</Label>
          <Input
            id={`caption-${imageKey}`}
            value={caption}
            onChange={(e) => setCaption(e.target.value)}
            placeholder="Caption gambar (opsional)"
          />
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Upload Button */}
      <div className="flex gap-2">
        <Button
          onClick={triggerFileSelect}
          variant="outline"
          className="gap-2"
        >
          <Upload className="h-4 w-4" />
          Pilih Gambar
        </Button>
        
        {preview && (
          <Button
            onClick={handleUpload}
            disabled={uploading}
            className="gap-2"
          >
            {uploading ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Upload className="h-4 w-4" />
            )}
            {uploading ? 'Mengupload...' : 'Upload'}
          </Button>
        )}
      </div>
    </div>
  )
}
