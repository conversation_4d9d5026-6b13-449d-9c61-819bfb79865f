"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>ger,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { AlertCircle, CheckCircle, Loader2, Phone } from "lucide-react"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'

type StudentData = {
  id: string
  name: string
  class: string
  photo: string | null
  achievements: {
    alQuran: {
      value: string
      grade: string
    }
    haditsArbain: {
      value: string
      grade: string
    }
  }
}

type StudentRecord = {
  id: string
  name: string
  photo_url: string | null
}

type Achievement = {
  subject_id: string
  value: string
  grade: string
  subjects?: { name: string }
}

type ClassStudentRecord = {
  student_id: string
  class_id: string
  classes: { name: string } | null
}

export function ParentAccessDialog() {
  const [open, setOpen] = useState(false)
  const [phoneNumber, setPhoneNumber] = useState("")
  const [isVerifying, setIsVerifying] = useState(false)
  const [verificationResult, setVerificationResult] = useState<"success" | "error" | null>(null)
  const [studentData, setStudentData] = useState<StudentData[]>([])
  const [errorMessage, setErrorMessage] = useState("")
  
  const supabase = createClientComponentClient()

  const handleVerify = async () => {
    setVerificationResult(null)
    setStudentData([])
    setErrorMessage("")

    if (!phoneNumber || phoneNumber.length < 10) {
      setVerificationResult("error")
      setErrorMessage("Nomor telepon tidak valid. Pastikan format benar (min. 10 digit)")
      return
    }

    setIsVerifying(true)

    try {
      // 1. Find parent by phone
      const { data: parent, error: parentError } = await supabase
        .from('parents')
        .select('id')
        .eq('phone', phoneNumber)
        .single();

      if (parentError || !parent) {
        throw new Error("Nomor WhatsApp tidak terdaftar dalam sistem");
      }
      // 2. Find student_id(s) for this parent
      const { data: studentParents, error: studentParentError } = await supabase
        .from('student_parent')
        .select('student_id')
        .eq('parent_id', parent.id)
        .eq('is_primary', true);

      if (studentParentError || !studentParents || studentParents.length === 0) {
        throw new Error("Data santri tidak ditemukan untuk orangtua ini");
      }

      const studentIds = studentParents.map(sp => String(sp.student_id));
      if (!studentIds || studentIds.length === 0) {
        throw new Error("Data santri tidak ditemukan untuk orangtua ini");
      }

      // Ambil data student
      const { data: students, error: studentError } = await supabase
        .from('students')
        .select(`
          id,
          name,
          photo_url
        `)
        .in('id', studentIds);

      if (studentError || !students || students.length === 0) {
        throw new Error("Data santri tidak ditemukan")
      }

      // Ambil data kelas untuk semua student
      const { data: classStudents, error: classStudentsError } = await supabase
        .from('class_students')
        .select(`
          student_id,
          class_id,
          classes(name)
        `)
        .in('student_id', studentIds) as { data: ClassStudentRecord[] | null, error: any };

      if (classStudentsError || !classStudents) {
        throw new Error("Gagal memuat data kelas santri")
      }

      // Ambil pencapaian untuk semua santri
      const studentDataArray: StudentData[] = [];
      for (const student of students) {
        // Ambil pencapaian untuk masing-masing santri
        const { data: achievements, error: achievementsError } = await supabase
          .from('achievements')
          .select(`
            subject_id,
            value,
            grade,
            subjects!inner(name)
          `)
          .eq('student_id', student.id);

        if (achievementsError) {
          throw new Error("Gagal memuat data pencapaian santri")
        }

        // Handle both real subjects and fallback subjects
        const alQuranAchievement = achievements?.find(a =>
          a.subjects?.name === "Al-Quran" ||
          a.subjects?.name === "Al-Qur'an" ||
          a.subject_id === "fallback-1"
        ) || { value: "-", grade: "-" }

        const haditsAchievement = achievements?.find(a =>
          a.subjects?.name === "Hadits" ||
          a.subjects?.name === "Hadits Arbain" ||
          a.subject_id === "fallback-2"
        ) || { value: "-", grade: "-" }

        // Ambil nama kelas dari classStudents
        const studentClasses = classStudents
          .filter(cs => cs.student_id === student.id)
          .map(cs => cs.classes?.name)
          .filter(Boolean);
        const className = studentClasses.length > 0 ? studentClasses.join(', ') : "-";

        studentDataArray.push({
          id: student.id,
          name: student.name,
          class: className,
          photo: student.photo_url,
          achievements: {
            alQuran: {
              value: alQuranAchievement.value,
              grade: alQuranAchievement.grade,
            },
            haditsArbain: {
              value: haditsAchievement.value,
              grade: haditsAchievement.grade,
            },
          }
        });
      }

      setStudentData(studentDataArray)
      setVerificationResult("success")
    } catch (error) {
      console.error("Verification error:", error)
      setVerificationResult("error")
      setErrorMessage(error instanceof Error ? error.message : "Terjadi kesalahan saat verifikasi")
    } finally {
      setIsVerifying(false)
    }
  }

  const handlePhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    // Only allow numbers
    const value = e.target.value.replace(/[^0-9]/g, "")
    setPhoneNumber(value)
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button
          variant="outline"
          className="gap-2 bg-white text-emerald-800 hover:bg-emerald-50 border-emerald-200"
          onClick={() => setOpen(true)}
        >
          <Phone className="h-4 w-4" />
          Akses Orangtua
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Akses Portal Orangtua</DialogTitle>
          <DialogDescription>Masukkan nomor WhatsApp yang terdaftar untuk melihat pencapaian santri.</DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <Label htmlFor="phone">Nomor WhatsApp</Label>
            <div className="flex items-center gap-2">
              <Input
                id="phone"
                placeholder="Contoh: 081234567890"
                value={phoneNumber}
                onChange={handlePhoneChange}
                disabled={isVerifying}
              />
              <Button onClick={handleVerify} disabled={isVerifying || phoneNumber.length < 10} className="shrink-0">
                {isVerifying ? <Loader2 className="h-4 w-4 animate-spin" /> : "Verifikasi"}
              </Button>
            </div>
            <p className="text-xs text-muted-foreground">Format: 08xxxxxxxxxx (tanpa spasi atau karakter khusus)</p>
          </div>

          {verificationResult === "error" && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Verifikasi Gagal</AlertTitle>
              <AlertDescription>
                {errorMessage || "Nomor WhatsApp tidak ditemukan atau tidak valid. Pastikan nomor yang dimasukkan benar."}
              </AlertDescription>
            </Alert>
          )}

          {verificationResult === "success" && studentData.length > 0 && (
            <div className="space-y-4">
              <Alert className="bg-emerald-50 border-emerald-200">
                <CheckCircle className="h-4 w-4 text-emerald-600" />
                <AlertTitle className="text-emerald-600">Verifikasi Berhasil</AlertTitle>
                <AlertDescription className="text-emerald-700">
                  Nomor WhatsApp terverifikasi. Berikut data santri yang terkait.
                </AlertDescription>
              </Alert>

              {studentData.map((sd) => (
                <div key={sd.id} className="bg-gray-50 p-4 rounded-lg">
                  <div className="flex items-center gap-4 mb-4">
                    <div className="relative h-16 w-16 rounded-full overflow-hidden border-2 border-emerald-200">
                      <img
                        src={sd.photo || "/placeholder.svg"}
                        alt={sd.name}
                        className="object-cover"
                      />
                    </div>
                    <div>
                      <h3 className="font-bold text-lg">{sd.name}</h3>
                      <p className="text-gray-500">{sd.class}</p>
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-3">
                    <div className="bg-white p-3 rounded border">
                      <p className="text-sm text-gray-500">Hafalan Al-Qur'an</p>
                      <p className="font-bold text-emerald-700">{sd.achievements.alQuran.value}</p>
                      <p className="text-xs text-emerald-600">{sd.achievements.alQuran.grade}</p>
                    </div>
                    <div className="bg-white p-3 rounded border">
                      <p className="text-sm text-gray-500">Hafalan Hadits</p>
                      <p className="font-bold text-emerald-700">{sd.achievements.haditsArbain.value}</p>
                      <p className="text-xs text-emerald-600">{sd.achievements.haditsArbain.grade}</p>
                    </div>
                  </div>
                  <Button asChild className="w-full sm:w-auto mt-4">
                    <a href={`/parent-dashboard?student_id=${sd.id}`}>Lihat Detail Lengkap</a>
                  </Button>
                </div>
              ))}
            </div>
          )}
        </div>

        <DialogFooter className="flex flex-col sm:flex-row gap-2">
          {verificationResult === "success" && studentData.length > 0 && (
            <Button 
              asChild 
              className="w-full sm:w-auto"
            >
              <a href={`/parent-dashboard?student_id=${studentData[0].id}`}>Lihat Detail Lengkap</a>
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
