"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import StudentList from "./student-list"
import type { Student } from "@/types"

interface StudentListClientProps {
  students: Student[]
  batches: string[]
}

export default function StudentListClient({ students, batches }: StudentListClientProps) {
  const router = useRouter()
  const [searchQuery, setSearchQuery] = useState("")
  const [filter, setFilter] = useState<string>("all")

  // Filter students based on search query and batch filter
  const filteredStudents = students.filter((student) => {
    const matchesSearch =
      student.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      student.batch.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesFilter = filter === "all" || student.batch === filter

    return matchesSearch && matchesFilter
  })

  const handleSelectStudent = (studentId: string) => {
    router.push(`/dashboard/${studentId}`)
  }

  return (
    <StudentList
      students={filteredStudents}
      batches={["all", ...batches]}
      searchQuery={searchQuery}
      setSearchQuery={setSearchQuery}
      filter={filter}
      setFilter={setFilter}
      onSelectStudent={handleSelectStudent}
    />
  )
} 