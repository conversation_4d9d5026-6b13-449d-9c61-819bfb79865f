"use client"
import { Search, User, Filter, List, Grid } from "lucide-react"
import type { Student } from "@/types"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import StudentCard from "./student-card"

interface StudentListProps {
  students: Student[]
  batches: string[]
  searchQuery: string
  setSearchQuery: (query: string) => void
  filter: string
  setFilter: (filter: string) => void
  onSelectStudent: (studentId: string) => void
}

export default function StudentList({
  students,
  batches,
  searchQuery,
  setSearchQuery,
  filter,
  setFilter,
  onSelectStudent,
}: StudentListProps) {
  return (
    <Tabs defaultValue="grid" className="w-full">
      <div className="flex flex-col md:flex-row justify-between items-center gap-4 mb-6">
        <TabsList className="grid w-full md:w-[200px] grid-cols-2">
          <TabsTrigger value="grid" className="flex items-center gap-2">
            <Grid className="h-4 w-4" />
            <span className="hidden sm:inline">Grid</span>
          </TabsTrigger>
          <TabsTrigger value="list" className="flex items-center gap-2">
            <List className="h-4 w-4" />
            <span className="hidden sm:inline">List</span>
          </TabsTrigger>
        </TabsList>

        <div className="flex flex-col sm:flex-row w-full md:w-auto gap-3 mt-4 md:mt-0">
          <div className="relative flex-grow">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <Input
              type="text"
              placeholder="Cari nama santri..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 py-6 bg-gray-50 border-gray-200"
            />
          </div>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" className="gap-2 whitespace-nowrap">
                <Filter className="h-4 w-4" />
                <span className="hidden sm:inline">Filter Angkatan</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              <DropdownMenuItem onClick={() => setFilter("all")}>Semua Angkatan</DropdownMenuItem>
              {batches.map((batch) => (
                <DropdownMenuItem key={batch} onClick={() => setFilter(batch)}>
                  {batch}
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      <TabsContent value="grid" className="mt-0">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {students.length > 0 ? (
            students.map((student) => (
              <StudentCard key={student.id} student={student} onClick={() => onSelectStudent(student.id)} />
            ))
          ) : (
            <div className="col-span-full text-center py-12 text-gray-500">
              <div className="flex flex-col items-center gap-2">
                <User className="h-12 w-12 text-gray-300" />
                <h3 className="text-lg font-medium">Tidak ada santri yang ditemukan</h3>
                <p>Coba kata kunci lain atau ubah filter angkatan</p>
              </div>
            </div>
          )}
        </div>
      </TabsContent>

      <TabsContent value="list" className="mt-0">
        {students.length > 0 ? (
          <Card>
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-left p-4">Nama Santri</th>
                  <th className="text-left p-4 hidden md:table-cell">Angkatan</th>
                  <th className="text-right p-4">Aksi</th>
                </tr>
              </thead>
              <tbody>
                {students.map((student) => (
                  <tr key={student.id} className="border-b hover:bg-gray-50">
                    <td className="p-4">
                      <div className="flex items-center gap-3">
                        <Avatar className="h-10 w-10">
                          <AvatarImage src={student.photo} alt={student.name} />
                          <AvatarFallback>{student.name.charAt(0)}</AvatarFallback>
                        </Avatar>
                        <span className="font-medium">{student.name}</span>
                      </div>
                    </td>
                    <td className="p-4 hidden md:table-cell text-gray-500">{student.batch}</td>
                    <td className="p-4 text-right">
                      <Button variant="outline" size="sm" onClick={() => onSelectStudent(student.id)}>
                        Lihat Detail
                      </Button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </Card>
        ) : (
          <div className="text-center py-12 text-gray-500">
            <div className="flex flex-col items-center gap-2">
              <User className="h-12 w-12 text-gray-300" />
              <h3 className="text-lg font-medium">Tidak ada santri yang ditemukan</h3>
              <p>Coba kata kunci lain atau ubah filter angkatan</p>
            </div>
          </div>
        )}
      </TabsContent>
    </Tabs>
  )
}

