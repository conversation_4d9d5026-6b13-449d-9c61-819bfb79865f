"use client"

import { useState, useEffect } from 'react'
import { useAchievementCrud, AchievementData } from '@/hooks/use-achievement-crud'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardHeader, CardTitle, CardContent, CardFooter } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2 } from 'lucide-react'

interface AchievementEditorProps {
  studentId: string
  teacherId: string
  achievement?: AchievementData
  onSuccess?: (achievement: any) => void
  onCancel?: () => void
}

export default function AchievementEditor({
  studentId,
  teacherId,
  achievement,
  onSuccess,
  onCancel
}: AchievementEditorProps) {
  const { createAchievement, updateAchievement, loading, error } = useAchievementCrud()
  
  const [formData, setFormData] = useState<Partial<AchievementData>>({
    student_id: studentId,
    subject: '',
    description: '',
    achievement_date: new Date().toISOString().split('T')[0],
    teacher_id: teacherId,
    grade: ''
  })
  
  // Load achievement data if editing
  useEffect(() => {
    if (achievement) {
      setFormData({
        ...achievement,
        achievement_date: achievement.achievement_date.split('T')[0] // Format date for input
      })
    }
  }, [achievement])
  
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
  }
  
  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => ({ ...prev, [name]: value }))
  }
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    try {
      let result
      
      if (achievement?.id) {
        // Update existing achievement
        result = await updateAchievement(achievement.id, formData)
      } else {
        // Create new achievement
        result = await createAchievement(formData as AchievementData)
      }
      
      if (result && onSuccess) {
        onSuccess(result)
      }
    } catch (err) {
      console.error('Failed to save achievement:', err)
    }
  }
  
  return (
    <Card className="w-full max-w-lg mx-auto">
      <CardHeader>
        <CardTitle>{achievement ? 'Edit Achievement' : 'Add New Achievement'}</CardTitle>
      </CardHeader>
      <form onSubmit={handleSubmit}>
        <CardContent className="space-y-4">
          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
          
          <div className="space-y-2">
            <Label htmlFor="subject">Subject</Label>
            <Select 
              value={formData.subject} 
              onValueChange={(value) => handleSelectChange('subject', value)}
              required
            >
              <SelectTrigger>
                <SelectValue placeholder="Select subject" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="alQuran">Al-Quran</SelectItem>
                <SelectItem value="haditsArbain">Hadits Arbain</SelectItem>
                <SelectItem value="ushulTsalatsah">Ushul Tsalatsah</SelectItem>
                <SelectItem value="ghoyahWaTaqrib">Ghoyah Wa Taqrib</SelectItem>
                <SelectItem value="alAjurumiyyah">Al-Ajurumiyyah</SelectItem>
                <SelectItem value="kitabGundul">Kitab Gundul</SelectItem>
                <SelectItem value="publicSpeaking">Public Speaking</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              name="description"
              value={formData.description || ''}
              onChange={handleChange}
              placeholder="Achievement details"
              required
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="grade">Grade</Label>
            <Select 
              value={formData.grade || ''} 
              onValueChange={(value) => handleSelectChange('grade', value)}
              required
            >
              <SelectTrigger>
                <SelectValue placeholder="Select grade" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Maqbul">Maqbul</SelectItem>
                <SelectItem value="Jayyid">Jayyid</SelectItem>
                <SelectItem value="Jayyid Jiddan">Jayyid Jiddan</SelectItem>
                <SelectItem value="Mumtaz">Mumtaz</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="achievement_date">Date</Label>
            <Input
              id="achievement_date"
              name="achievement_date"
              type="date"
              value={formData.achievement_date || ''}
              onChange={handleChange}
              required
            />
          </div>
        </CardContent>
        
        <CardFooter className="flex justify-end gap-2">
          {onCancel && (
            <Button type="button" variant="outline" onClick={onCancel}>
              Cancel
            </Button>
          )}
          <Button type="submit" disabled={loading}>
            {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {achievement ? 'Update' : 'Save'} Achievement
          </Button>
        </CardFooter>
      </form>
    </Card>
  )
} 