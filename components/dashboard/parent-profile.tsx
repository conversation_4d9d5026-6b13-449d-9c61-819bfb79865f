"use client"

import { useState, useEffect } from "react"
import { useParentCrud } from "@/hooks/use-parent-crud"
import { But<PERSON> } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { Loader2, User, Mail, Phone, Home, Briefcase } from "lucide-react"

const parentSchema = z.object({
  name: z.string().min(2, {
    message: "Name must be at least 2 characters.",
  }),
  email: z.string().email({
    message: "Please enter a valid email address.",
  }),
  phone: z.string().min(10, {
    message: "Phone number must be at least 10 digits.",
  }),
  address: z.string().min(5, {
    message: "Address must be at least 5 characters.",
  }),
  occupation: z.string().optional(),
})

type ParentFormValues = z.infer<typeof parentSchema>

interface ParentProfileProps {
  userId: string
}

export default function ParentProfile({ userId }: ParentProfileProps) {
  const { loading, error, updateParent, getParentByUserId } = useParentCrud()
  const [parent, setParent] = useState<any>(null)
  const [isOpen, setIsOpen] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [userRole, setUserRole] = useState<string | null>(null)

  const form = useForm<ParentFormValues>({
    resolver: zodResolver(parentSchema),
    defaultValues: {
      name: "",
      email: "",
      phone: "",
      address: "",
      occupation: "",
    },
  })

  useEffect(() => {
    const fetchParentData = async () => {
      setIsLoading(true)
      try {
        // Langsung menggunakan endpoint parents yang sudah ada
        const response = await fetch(`/api/parents?userId=${userId}`)
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }
        const { parent } = await response.json()
        if (parent) {
          setParent(parent)
          form.reset({
            name: parent.name,
            email: parent.email,
            phone: parent.phone,
            address: parent.address,
            occupation: parent.occupation || "",
          })
        }
      } catch (error) {
        console.error("Failed to fetch parent data:", error)
      } finally {
        setIsLoading(false)
      }
    }

    if (userId) {
      fetchParentData()
    }
  }, [userId, form])

  async function onSubmit(values: ParentFormValues) {
    if (!parent) return

    try {
      const updatedParent = await updateParent(parent.id, values)
      if (updatedParent) {
        setParent(updatedParent)
        setIsOpen(false)
      }
    } catch (error) {
      console.error("Failed to update parent:", error)
    }
  }

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-[200px]">
        <Loader2 className="h-8 w-8 animate-spin text-emerald-500" />
      </div>
    )
  }

  if (!parent) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Parent Profile</CardTitle>
          <CardDescription>Your parent profile information is not available.</CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground">
            Please contact an administrator to set up your parent profile.
          </p>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Parent Profile</CardTitle>
        <CardDescription>Your registered information as a parent</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-start space-x-4">
          <User className="h-5 w-5 text-gray-500 mt-0.5" />
          <div>
            <h4 className="text-sm font-medium">Name</h4>
            <p className="text-sm text-gray-700">{parent.name}</p>
          </div>
        </div>
        
        <div className="flex items-start space-x-4">
          <Mail className="h-5 w-5 text-gray-500 mt-0.5" />
          <div>
            <h4 className="text-sm font-medium">Email</h4>
            <p className="text-sm text-gray-700">{parent.email}</p>
          </div>
        </div>
        
        <div className="flex items-start space-x-4">
          <Phone className="h-5 w-5 text-gray-500 mt-0.5" />
          <div>
            <h4 className="text-sm font-medium">Phone</h4>
            <p className="text-sm text-gray-700">{parent.phone}</p>
          </div>
        </div>
        
        <div className="flex items-start space-x-4">
          <Home className="h-5 w-5 text-gray-500 mt-0.5" />
          <div>
            <h4 className="text-sm font-medium">Address</h4>
            <p className="text-sm text-gray-700">{parent.address}</p>
          </div>
        </div>
        
        {parent.occupation && (
          <div className="flex items-start space-x-4">
            <Briefcase className="h-5 w-5 text-gray-500 mt-0.5" />
            <div>
              <h4 className="text-sm font-medium">Occupation</h4>
              <p className="text-sm text-gray-700">{parent.occupation}</p>
            </div>
          </div>
        )}
        
        {parent.students && parent.students.length > 0 && (
          <div className="mt-6">
            <h4 className="text-sm font-medium mb-2">Children</h4>
            <ul className="space-y-1">
              {parent.students.map((student: any) => (
                <li key={student.id} className="text-sm text-gray-700">
                  {student.name}
                </li>
              ))}
            </ul>
          </div>
        )}
      </CardContent>
      <CardFooter>
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
          <DialogTrigger asChild>
            <Button variant="outline">Edit Profile</Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>Edit Parent Profile</DialogTitle>
              <DialogDescription>
                Update your information. Click save when you're done.
              </DialogDescription>
            </DialogHeader>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Name</FormLabel>
                      <FormControl>
                        <Input placeholder="Your full name" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email</FormLabel>
                      <FormControl>
                        <Input placeholder="Your email address" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="phone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Phone</FormLabel>
                      <FormControl>
                        <Input placeholder="Your phone number" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="address"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Address</FormLabel>
                      <FormControl>
                        <Textarea placeholder="Your home address" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="occupation"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Occupation</FormLabel>
                      <FormControl>
                        <Input placeholder="Your occupation (optional)" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <DialogFooter>
                  <Button type="submit" disabled={loading}>
                    {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                    Save Changes
                  </Button>
                </DialogFooter>
                {error && (
                  <p className="text-sm text-red-500 mt-2">{error}</p>
                )}
              </form>
            </Form>
          </DialogContent>
        </Dialog>
      </CardFooter>
    </Card>
  )
}