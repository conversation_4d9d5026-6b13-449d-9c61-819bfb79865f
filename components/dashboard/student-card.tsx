"use client"

import { Card, CardContent } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import type { Student } from "@/types"

interface StudentCardProps {
  student: Student
  onClick: () => void
}

export default function StudentCard({ student, onClick }: StudentCardProps) {
  // Get the latest achievement count for display
  const achievementCount = student.achievements?.length || 0
  const hasRecentAchievements = achievementCount > 0

  return (
    <Card
      className="cursor-pointer group overflow-hidden transition-all duration-300 hover:shadow-lg hover:shadow-emerald-100 border-gray-100 hover:border-emerald-200"
      onClick={onClick}
    >
      <div className="h-2 bg-gradient-to-r from-emerald-500 to-emerald-600 transform origin-left transition-all duration-300 group-hover:scale-x-110"></div>
      <CardContent className="p-6">
        <div className="flex items-center gap-4">
          <Avatar className="h-16 w-16 border-2 border-emerald-100 shadow-sm group-hover:border-emerald-200 transition-all duration-300">
            <AvatarImage src={student.photo || undefined} alt={student.name} />
            <AvatarFallback className="bg-emerald-100 text-emerald-700">{student.name.charAt(0)}</AvatarFallback>
          </Avatar>
          <div className="flex-1">
            <h3 className="font-semibold text-lg group-hover:text-emerald-700 transition-colors duration-300">
              {student.name}
            </h3>
            <div className="flex items-center gap-2 mt-1">
              <Badge variant="outline" className="text-xs font-normal">
                {student.batch}
              </Badge>
              {hasRecentAchievements && (
                <Badge variant="secondary" className="text-xs font-normal bg-emerald-50 text-emerald-700">
                  {achievementCount} pencapaian
                </Badge>
              )}
            </div>
            {student.class && (
              <p className="text-sm text-gray-500 mt-1">
                {student.class.name} - {student.class.level}
              </p>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

