"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { 
  RefreshCw,
  TrendingUp,
  Clock,
  BarChart3,
  Loader2
} from "lucide-react"
import { ActivityTimeline, PerformanceMetrics } from "@/components/dashboard/simple-chart"

interface DashboardData {
  statistics: {
    totalStudents: number
    totalClasses: number
    activeStudents: number
    graduatedStudents: number
  }
  classDistribution: Array<{
    name: string
    studentCount: number
    capacity: number
    percentage: number
  }>
  performanceMetrics: Array<{
    subject: string
    average: number
    target: number
    students: number
  }>
  recentActivities: Array<{
    type: string
    message: string
    time: string
    user: string
  }>
  monthlyProgress: {
    hafalanBaru: string
    pencapaianSelesai: string
    santriAktif: number
    tingkatKehadiran: string
  }
}

interface RealTimeDashboardProps {
  initialData: DashboardData
}

export function RealTimeDashboard({ initialData }: RealTimeDashboardProps) {
  const [data, setData] = useState<DashboardData>(initialData)
  const [loading, setLoading] = useState(false)
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date())

  // Auto-refresh every 5 minutes
  useEffect(() => {
    const interval = setInterval(() => {
      refreshData()
    }, 5 * 60 * 1000) // 5 minutes

    return () => clearInterval(interval)
  }, [])

  const refreshData = async () => {
    try {
      setLoading(true)
      
      const response = await fetch('/api/dashboard/stats', {
        cache: 'no-store'
      })

      if (response.ok) {
        const newData = await response.json()
        setData({
          statistics: newData.statistics,
          classDistribution: newData.classDistribution,
          performanceMetrics: newData.performanceMetrics,
          recentActivities: newData.recentActivities,
          monthlyProgress: newData.monthlyProgress
        })
        setLastUpdated(new Date())
      }
    } catch (error) {
      console.error('Error refreshing dashboard data:', error)
    } finally {
      setLoading(false)
    }
  }

  const formatLastUpdated = (date: Date) => {
    return date.toLocaleTimeString('id-ID', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
  }

  return (
    <div className="space-y-6">
      {/* Refresh Controls */}
      <div className="flex justify-between items-center">
        <div className="text-sm text-gray-500">
          Terakhir diperbarui: {formatLastUpdated(lastUpdated)}
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={refreshData}
          disabled={loading}
          className="gap-2"
        >
          {loading ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            <RefreshCw className="h-4 w-4" />
          )}
          {loading ? 'Memperbarui...' : 'Refresh'}
        </Button>
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Class Distribution */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              Distribusi Kelas
            </CardTitle>
            <CardDescription>
              Jumlah santri per kelas dan kapasitas (Real-time)
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {data.classDistribution.map((cls, index) => (
                <div key={index} className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="font-medium">{cls.name}</span>
                    <span className="text-sm text-gray-500">
                      {cls.studentCount}/{cls.capacity}
                    </span>
                  </div>
                  <Progress 
                    value={(cls.studentCount / cls.capacity) * 100} 
                    className="h-2"
                  />
                  <div className="text-xs text-gray-500 text-right">
                    {Math.round((cls.studentCount / cls.capacity) * 100)}% terisi
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Recent Activities */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Aktivitas Terbaru
            </CardTitle>
            <CardDescription>
              Aktivitas sistem real-time
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ActivityTimeline activities={data.recentActivities} />
          </CardContent>
        </Card>
      </div>

      {/* Performance Overview */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Performance Metrics */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              Performa Akademik
            </CardTitle>
            <CardDescription>
              Rata-rata pencapaian santri per mata pelajaran (Data Real)
            </CardDescription>
          </CardHeader>
          <CardContent>
            <PerformanceMetrics metrics={data.performanceMetrics} />
          </CardContent>
        </Card>

        {/* Monthly Progress */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              Progress Bulanan
            </CardTitle>
            <CardDescription>
              Perkembangan santri bulan ini (Data Real)
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">Hafalan Baru</span>
                <Badge variant="secondary">{data.monthlyProgress.hafalanBaru}</Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">Pencapaian Selesai</span>
                <Badge variant="secondary">{data.monthlyProgress.pencapaianSelesai}</Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">Santri Aktif</span>
                <Badge variant="secondary">{data.monthlyProgress.santriAktif} Santri</Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">Tingkat Kehadiran</span>
                <Badge variant="secondary">{data.monthlyProgress.tingkatKehadiran}</Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Performance Summary */}
      {data.performanceMetrics.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Ringkasan Performa</CardTitle>
            <CardDescription>
              Analisis performa akademik berdasarkan data real
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {data.performanceMetrics.map((metric, index) => (
                <div key={index} className="text-center p-4 border rounded-lg">
                  <h4 className="font-medium text-sm mb-2">{metric.subject}</h4>
                  <div className="text-2xl font-bold text-emerald-600 mb-1">
                    {metric.average}%
                  </div>
                  <div className="text-xs text-gray-500">
                    Target: {metric.target}%
                  </div>
                  <div className="text-xs text-gray-500">
                    {metric.students} santri
                  </div>
                  {metric.average >= metric.target ? (
                    <Badge variant="default" className="mt-2 text-xs">
                      Target Tercapai
                    </Badge>
                  ) : (
                    <Badge variant="secondary" className="mt-2 text-xs">
                      Perlu Peningkatan
                    </Badge>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
