"use client"

import { useState } from "react"
import { format } from "date-fns"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { MoreHorizontal, Plus } from "lucide-react"
import { createQuranProgress, deleteQuranProgress } from "@/lib/supabase-crud"
import AddQuranProgressDialog from "./dialogs/add-quran-progress-dialog"

// Type for Quran progress
export type QuranProgress = {
  id: string
  student_id: string
  surah: string
  start_verse: number
  end_verse: number
  status: "memorized" | "in_progress" | "review_needed"
  notes?: string | null
  teacher_id?: string | null
  teacher_name?: string | null
  progress_date: string
}

interface QuranProgressSectionProps {
  studentId: string
  initialProgress: QuranProgress[]
}

export default function QuranProgressSection({
  studentId,
  initialProgress,
}: QuranProgressSectionProps) {
  const [progressList, setProgressList] = useState<QuranProgress[]>(initialProgress)
  const [addDialogOpen, setAddDialogOpen] = useState(false)
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [selectedProgressId, setSelectedProgressId] = useState<string | null>(null)

  // Format date for display
  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), "dd MMM yyyy")
    } catch (error) {
      return "Tanggal tidak valid"
    }
  }

  // Get badge color based on status
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "memorized":
        return "bg-green-500"
      case "in_progress":
        return "bg-yellow-500"
      case "review_needed":
        return "bg-red-500"
      default:
        return "bg-gray-500"
    }
  }

  // Get status display text
  const getStatusText = (status: string) => {
    switch (status) {
      case "memorized":
        return "Hafal"
      case "in_progress":
        return "Proses"
      case "review_needed":
        return "Perlu Diulang"
      default:
        return "Tidak Diketahui"
    }
  }

  // Handle adding new progress
  const handleAddProgress = async (data: any) => {
    try {
      // Create new progress in database
      const newProgress = await createQuranProgress({
        student_id: studentId,
        surah: data.surah,
        start_verse: data.start_verse,
        end_verse: data.end_verse,
        status: data.status,
        notes: data.notes || null,
        teacher_id: data.teacher_id || "1", // Default teacher ID
        progress_date: data.progress_date.toISOString(),
      })

      if (newProgress) {
        // Add to state
        setProgressList([...progressList, newProgress])
        setAddDialogOpen(false)
        return true
      }
      return false
    } catch (error) {
      console.error("Error adding progress:", error)
      return false
    }
  }

  // Handle deleting progress
  const handleDeleteProgress = async () => {
    if (!selectedProgressId) return

    try {
      // Delete from database
      const success = await deleteQuranProgress(selectedProgressId)
      
      if (success) {
        // Remove from state
        setProgressList(progressList.filter(item => item.id !== selectedProgressId))
      }
      
      // Close dialog
      setDeleteDialogOpen(false)
      setSelectedProgressId(null)
    } catch (error) {
      console.error("Error deleting progress:", error)
    }
  }

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>Progress Hafalan Al-Quran</CardTitle>
          <Button
            size="sm"
            onClick={() => setAddDialogOpen(true)}
          >
            <Plus className="mr-2 h-4 w-4" />
            Tambah Progress
          </Button>
        </CardHeader>
        <CardContent>
          {progressList.length === 0 ? (
            <div className="text-center py-6 text-muted-foreground">
              Belum ada data progress hafalan
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Tanggal</TableHead>
                  <TableHead>Surah</TableHead>
                  <TableHead>Ayat</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Guru</TableHead>
                  <TableHead className="w-[50px]"></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {progressList.map((progress) => (
                  <TableRow key={progress.id}>
                    <TableCell>{formatDate(progress.progress_date)}</TableCell>
                    <TableCell>{progress.surah}</TableCell>
                    <TableCell>
                      {progress.start_verse} - {progress.end_verse}
                    </TableCell>
                    <TableCell>
                      <Badge className={getStatusBadge(progress.status)}>
                        {getStatusText(progress.status)}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {progress.teacher_name || "Tidak diketahui"}
                    </TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon">
                            <MoreHorizontal className="h-4 w-4" />
                            <span className="sr-only">Menu</span>
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem
                            className="text-destructive"
                            onClick={() => {
                              setSelectedProgressId(progress.id)
                              setDeleteDialogOpen(true)
                            }}
                          >
                            Hapus
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Add Progress Dialog */}
      <AddQuranProgressDialog
        open={addDialogOpen}
        onOpenChange={setAddDialogOpen}
        onSubmit={handleAddProgress}
        studentId={studentId}
      />

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Hapus Progress Hafalan</AlertDialogTitle>
            <AlertDialogDescription>
              Apakah Anda yakin ingin menghapus catatan progress hafalan ini?
              Tindakan ini tidak dapat dibatalkan.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Batal</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteProgress}
              className="bg-destructive hover:bg-destructive/90"
            >
              Hapus
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
} 