"use client"

import { useState, useEffect } from 'react'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Label } from '@/components/ui/label'
import { Button } from '@/components/ui/button'
import { Loader2, Plus } from 'lucide-react'
import { Input } from '../ui/input'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'

export type ParentOption = {
  id: string
  name: string
  email?: string
  phone?: string
}

interface ParentSelectorProps {
  value?: string
  onChange: (value: string, relationship: string) => void
  required?: boolean
  label?: string
  error?: string
  disabled?: boolean
  parents: ParentOption[]
  onCreateParent?: (parent: { name: string, phone: string, email: string }) => Promise<any>
}

export default function ParentSelector({
  value,
  onChange,
  required,
  label = "Orang Tua",
  error,
  disabled,
  parents,
  onCreateParent,
}: ParentSelectorProps) {
  const [selectedParentId, setSelectedParentId] = useState(value || '')
  const [relationship, setRelationship] = useState('parent')
  const [isAddParentOpen, setIsAddParentOpen] = useState(false)
  const [isCreating, setIsCreating] = useState(false)

  // New parent form
  const [newParent, setNewParent] = useState({
    name: '',
    phone: '',
    email: '',
  })

  useEffect(() => {
    setSelectedParentId(value || '')
  }, [value])

  const handleChange = (id: string) => {
    setSelectedParentId(id)
    onChange(id, relationship)
  }

  const handleRelationshipChange = (rel: string) => {
    setRelationship(rel)
    onChange(selectedParentId, rel)
  }

  const handleCreateParent = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!onCreateParent) return

    try {
      setIsCreating(true)
      const result = await onCreateParent(newParent)
      if (result?.id) {
        // Clear form and close dialog
        setNewParent({
          name: '',
          phone: '',
          email: '',
        })
        setIsAddParentOpen(false)
        
        // Select the newly created parent
        handleChange(result.id)
      }
    } catch (error) {
      console.error("Error creating parent:", error)
    } finally {
      setIsCreating(false)
    }
  }
  
  return (
    <div className="space-y-3">
      <div className="flex justify-between items-center">
        <Label htmlFor="parent-selector">
          {label} {required && <span className="text-red-500">*</span>}
        </Label>
        
        {onCreateParent && (
          <Dialog open={isAddParentOpen} onOpenChange={setIsAddParentOpen}>
            <DialogTrigger asChild>
              <Button 
                type="button" 
                variant="outline" 
                size="sm" 
                className="flex items-center text-xs"
              >
                <Plus className="h-3 w-3 mr-1" />
                Tambah Orang Tua
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Tambah Orang Tua Baru</DialogTitle>
              </DialogHeader>
              <form onSubmit={handleCreateParent} className="space-y-4 mt-2">
                <div className="space-y-2">
                  <Label htmlFor="parent-name">Nama Orang Tua</Label>
                  <Input
                    id="parent-name"
                    value={newParent.name}
                    onChange={(e) => setNewParent(prev => ({ ...prev, name: e.target.value }))}
                    placeholder="Nama lengkap"
                    required
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="parent-phone">Nomor Telepon</Label>
                  <Input
                    id="parent-phone"
                    value={newParent.phone}
                    onChange={(e) => setNewParent(prev => ({ ...prev, phone: e.target.value }))}
                    placeholder="Contoh: 6281234567890"
                    required
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="parent-email">Email (Opsional)</Label>
                  <Input
                    id="parent-email"
                    type="email"
                    value={newParent.email}
                    onChange={(e) => setNewParent(prev => ({ ...prev, email: e.target.value }))}
                    placeholder="<EMAIL>"
                  />
                </div>
                
                <div className="flex justify-end space-x-2 pt-2">
                  <Button 
                    type="button" 
                    variant="outline" 
                    onClick={() => setIsAddParentOpen(false)}
                  >
                    Batal
                  </Button>
                  <Button 
                    type="submit"
                    disabled={isCreating || !newParent.name || !newParent.phone}
                  >
                    {isCreating && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                    Simpan
                  </Button>
                </div>
              </form>
            </DialogContent>
          </Dialog>
        )}
      </div>
      
      <Select
        value={selectedParentId}
        onValueChange={handleChange}
        disabled={disabled}
      >
        <SelectTrigger className={error ? "border-red-500" : ""}>
          <SelectValue placeholder="Pilih orang tua" />
        </SelectTrigger>
        <SelectContent>
          {parents.length === 0 ? (
            <div className="px-2 py-1 text-sm text-gray-500">
              Tidak ada data orang tua
            </div>
          ) : (
            parents.map((parent) => (
              <SelectItem key={parent.id} value={parent.id}>
                {parent.name} {parent.phone && `(${parent.phone})`}
              </SelectItem>
            ))
          )}
        </SelectContent>
      </Select>
      
      <div className="space-y-2">
        <Label htmlFor="relationship">Hubungan</Label>
        <Select
          value={relationship}
          onValueChange={handleRelationshipChange}
          disabled={disabled || !selectedParentId}
        >
          <SelectTrigger>
            <SelectValue placeholder="Pilih hubungan" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="father">Ayah</SelectItem>
            <SelectItem value="mother">Ibu</SelectItem>
            <SelectItem value="guardian">Wali</SelectItem>
            <SelectItem value="parent">Orang Tua</SelectItem>
          </SelectContent>
        </Select>
      </div>
      
      {error && <p className="text-sm text-red-500">{error}</p>}
    </div>
  )
} 