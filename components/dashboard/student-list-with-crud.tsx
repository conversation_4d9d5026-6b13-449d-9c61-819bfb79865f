"use client"

import { useState } from "react"
import { useStudentCrud, StudentData } from "@/hooks/use-student-crud"
import { useParentCrud } from "@/hooks/use-parent-crud"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { Plus, Edit, Trash } from "lucide-react"
import StudentEditor from "./student-editor"
import { ParentOption } from "./parent-selector"

interface StudentListWithCrudProps {
  initialStudents: any[]
  classOptions: { id: string, name: string }[]
  parentOptions: ParentOption[]
  parentId?: string // Optional now
}

export default function StudentListWithCrud({ 
  initialStudents, 
  classOptions,
  parentOptions: initialParentOptions,
  parentId
}: StudentListWithCrudProps) {
  const [students, setStudents] = useState(initialStudents)
  const [editingStudent, setEditingStudent] = useState<StudentData | null>(null)
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [parentOptions, setParentOptions] = useState<ParentOption[]>(initialParentOptions)
  
  const { deleteStudent, loading: studentLoading, error: studentError } = useStudentCrud()
  const { createParent, loading: parentLoading, error: parentError } = useParentCrud()
  
  // Handle student creation success
  const handleCreateSuccess = (newStudent: any) => {
    setStudents(prev => [...prev, newStudent])
    setIsDialogOpen(false)
  }
  
  // Handle student update success
  const handleUpdateSuccess = (updatedStudent: any) => {
    setStudents(prev => 
      prev.map(student => 
        student.id === updatedStudent.id ? updatedStudent : student
      )
    )
    setEditingStudent(null)
    setIsDialogOpen(false)
  }
  
  // Handle student deletion
  const handleDelete = async (id: string) => {
    if (confirm('Apakah Anda yakin ingin menghapus santri ini?')) {
      const result = await deleteStudent(id)
      if (result) {
        setStudents(prev => prev.filter(student => student.id !== id))
      }
    }
  }

  // Handle parent creation
  const handleCreateParent = async (parentData: { name: string, phone: string, email: string }) => {
    try {
      const result = await createParent({
        name: parentData.name,
        phone: parentData.phone,
        email: parentData.email,
        address: ''
      })

      if (result?.data) {
        // Add the new parent to the options
        const newParent: ParentOption = {
          id: result.data.id,
          name: result.data.name,
          phone: result.data.phone,
          email: result.data.email
        }
        
        setParentOptions(prev => [...prev, newParent])
        return newParent
      }
      
      return null
    } catch (error) {
      console.error('Failed to create parent:', error)
      throw error
    }
  }
  
  const error = studentError || parentError
  const loading = studentLoading || parentLoading
  
  return (
    <div>
      {/* Header with Add Button */}
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold">Daftar Santri</h2>
        
        {/* Add Student Button */}
        <Button 
          onClick={() => {
            setEditingStudent(null)
            setIsDialogOpen(true)
          }}
          className="bg-emerald-600 hover:bg-emerald-700"
        >
          <Plus className="mr-2 h-4 w-4" /> Tambah Santri
        </Button>
      </div>
      
      {/* Error Message */}
      {error && (
        <div className="p-4 mb-4 text-red-700 bg-red-100 rounded-md">
          {error}
        </div>
      )}
      
      {/* Dialog for Add/Edit */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>
              {editingStudent ? 'Edit Santri' : 'Tambah Santri Baru'}
            </DialogTitle>
          </DialogHeader>
          <StudentEditor 
            student={editingStudent || undefined}
            parentId={editingStudent?.parent_id || parentId}
            relationship={editingStudent?.relationship}
            classOptions={classOptions}
            parentOptions={parentOptions}
            onSuccess={editingStudent ? handleUpdateSuccess : handleCreateSuccess}
            onCancel={() => {
              setEditingStudent(null)
              setIsDialogOpen(false)
            }}
            onCreateParent={handleCreateParent}
          />
        </DialogContent>
      </Dialog>
      
      {/* Student List */}
      {students.length === 0 ? (
        <div className="text-center py-12 bg-gray-50 rounded-lg">
          <p className="text-gray-500">Belum ada data santri</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {students.map((student) => (
            <Card key={student.id} className="overflow-hidden">
              <CardContent className="p-4">
                <div className="flex items-center space-x-4 mb-4">
                  <Avatar className="h-12 w-12 border-2 border-emerald-100">
                    <AvatarFallback className="bg-emerald-100 text-emerald-700">
                      {student.name.charAt(0)}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <h3 className="font-medium">{student.name}</h3>
                    <p className="text-sm text-gray-500">{student.class?.name || "Belum ada kelas"}</p>
                  </div>
                </div>
                <div className="flex justify-end space-x-2">
                  {/* Edit Button */}
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => {
                      setEditingStudent(student as StudentData)
                      setIsDialogOpen(true)
                    }}
                    className="flex items-center"
                  >
                    <Edit className="h-4 w-4 mr-1" />
                    Edit
                  </Button>
                  
                  {/* Delete Button */}
                  <Button 
                    variant="destructive" 
                    size="sm"
                    onClick={() => handleDelete(student.id)}
                    disabled={loading}
                    className="flex items-center"
                  >
                    <Trash className="h-4 w-4 mr-1" />
                    Hapus
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  )
} 