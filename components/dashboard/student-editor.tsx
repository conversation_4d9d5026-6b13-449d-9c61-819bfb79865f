"use client"

import { useState, useEffect } from 'react'
import { useStudentCrud, StudentData } from '@/hooks/use-student-crud'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2 } from 'lucide-react'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import ParentSelector, { ParentOption } from './parent-selector'

interface StudentEditorProps {
  student?: StudentData
  parentId?: string  // Now optional since we use ParentSelector
  relationship?: string 
  classOptions: { id: string, name: string }[]
  parentOptions: ParentOption[]
  onSuccess?: (student: any) => void
  onCancel?: () => void
  onCreateParent?: (parent: { name: string, phone: string, email: string }) => Promise<any>
}

export default function StudentEditor({
  student,
  parentId,
  relationship,
  classOptions,
  parentOptions = [],
  onSuccess,
  onCancel,
  onCreateParent
}: StudentEditorProps) {
  const { createStudent, updateStudent, loading, error } = useStudentCrud()
  
  const [formData, setFormData] = useState<Partial<StudentData>>({
    name: '',
    gender: 'male',
    birth_date: '',
    address: '',
    batch: new Date().getFullYear().toString(),
    status: 'active'
  })

  // Separate state for parent relationship
  const [parentRelation, setParentRelation] = useState({
    parent_id: parentId || '',
    relationship: relationship || 'parent'
  })
  
  // Separate state for class assignment
  const [classId, setClassId] = useState('')
  
  // Load student data if editing
  useEffect(() => {
    if (student) {
      setFormData({
        ...student,
        // Format date if needed
        birth_date: student.birth_date?.split('T')[0],
      })
      
      // If student has class info, set the class ID
      if (student.class_id) {
        setClassId(student.class_id)
      }
      
      // If student has parent info, set the parent relation
      if (student.parent_id) {
        setParentRelation({
          parent_id: student.parent_id,
          relationship: student.relationship || 'parent'
        })
      }
    }
  }, [student])
  
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
  }
  
  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => ({ ...prev, [name]: value }))
  }
  
  const handleParentChange = (parentId: string, relationship: string) => {
    setParentRelation({
      parent_id: parentId,
      relationship
    })
  }
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    try {
      let result
      
      // Prepare data with parent and class relationship
      const studentData = {
        ...formData
      }
      
      if (student?.id) {
        // Update existing student
        result = await updateStudent(student.id, {
          studentData, 
          classId, 
          parentId: parentRelation.parent_id,
          relationship: parentRelation.relationship
        })
      } else {
        // Create new student
        result = await createStudent({
          studentData: studentData as StudentData,
          classId,
          parentId: parentRelation.parent_id,
          relationship: parentRelation.relationship
        })
      }
      
      if (result && onSuccess) {
        onSuccess(result)
      }
    } catch (err) {
      console.error('Failed to save student:', err)
    }
  }
  
  return (
    <div className="w-full">
      <form onSubmit={handleSubmit} className="space-y-4">
        {error && (
          <Alert variant="destructive">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}
        
        <div className="space-y-2">
          <Label htmlFor="name">Nama Santri</Label>
          <Input
            id="name"
            name="name"
            value={formData.name || ''}
            onChange={handleChange}
            placeholder="Nama lengkap"
            required
          />
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="gender">Jenis Kelamin</Label>
          <RadioGroup 
            value={formData.gender || 'male'} 
            onValueChange={(value) => handleSelectChange('gender', value)}
            className="flex gap-4"
          >
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="male" id="male" />
              <Label htmlFor="male">Laki-laki</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="female" id="female" />
              <Label htmlFor="female">Perempuan</Label>
            </div>
          </RadioGroup>
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="birth_date">Tanggal Lahir</Label>
          <Input
            id="birth_date"
            name="birth_date"
            type="date"
            value={formData.birth_date || ''}
            onChange={handleChange}
            required
          />
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="address">Alamat</Label>
          <Input
            id="address"
            name="address"
            value={formData.address || ''}
            onChange={handleChange}
            placeholder="Alamat lengkap"
            required
          />
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="class_id">Kelas</Label>
          <Select 
            value={classId} 
            onValueChange={(value) => setClassId(value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Pilih kelas" />
            </SelectTrigger>
            <SelectContent>
              {classOptions.map(option => (
                <SelectItem key={option.id} value={option.id}>
                  {option.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        
        {/* Parent Selector Component */}
        <ParentSelector
          value={parentRelation.parent_id}
          onChange={handleParentChange}
          parents={parentOptions}
          onCreateParent={onCreateParent}
          required
        />
        
        <div className="space-y-2">
          <Label htmlFor="batch">Angkatan</Label>
          <Input
            id="batch"
            name="batch"
            value={formData.batch || new Date().getFullYear().toString()}
            onChange={handleChange}
            placeholder="Tahun angkatan"
          />
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="status">Status</Label>
          <Select 
            value={formData.status || 'active'} 
            onValueChange={(value) => handleSelectChange('status', value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Status santri" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="active">Aktif</SelectItem>
              <SelectItem value="inactive">Tidak Aktif</SelectItem>
              <SelectItem value="graduated">Lulus</SelectItem>
              <SelectItem value="suspended">Diskors</SelectItem>
            </SelectContent>
          </Select>
        </div>
        
        <div className="flex justify-end gap-2 pt-4">
          {onCancel && (
            <Button type="button" variant="outline" onClick={onCancel}>
              Batal
            </Button>
          )}
          <Button type="submit" disabled={loading || !parentRelation.parent_id}>
            {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {student ? 'Perbarui' : 'Simpan'} Santri
          </Button>
        </div>
      </form>
    </div>
  )
} 