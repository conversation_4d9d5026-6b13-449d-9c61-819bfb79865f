"use client"

import { useState } from "react"
import { useAchievementCrud, AchievementData } from "@/hooks/use-achievement-crud"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Plus, Edit, Trash, Award } from "lucide-react"
import AchievementEditor from "./achievement-editor"
import { Badge } from "@/components/ui/badge"

interface AchievementListProps {
  student: any
  initialAchievements: any[]
  teacherId: string
}

// Map the internal keys to display names
const subjectDisplayNames: Record<string, string> = {
  alQuran: "Al-Quran",
  haditsArbain: "Hadits Arbain",
  ushulTsalatsah: "Ushul Tsalatsah",
  ghoyahWaTaqrib: "Ghoyah Wa Taqrib",
  alAjurumiyyah: "Al-Ajurumiyyah",
  kitabGundul: "Kitab Gundul",
  publicSpeaking: "Public Speaking"
}

export default function AchievementList({ 
  student, 
  initialAchievements, 
  teacherId 
}: AchievementListProps) {
  const [achievements, setAchievements] = useState(initialAchievements)
  const [editingAchievement, setEditingAchievement] = useState<AchievementData | null>(null)
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  
  const { deleteAchievement, loading, error } = useAchievementCrud()
  
  // Only teachers can add/edit achievements
  const canEditAchievements = teacherId !== ''
  
  // Handle achievement creation success
  const handleCreateSuccess = (newAchievement: any) => {
    setAchievements(prev => [...prev, newAchievement])
    setIsDialogOpen(false)
  }
  
  // Handle achievement update success
  const handleUpdateSuccess = (updatedAchievement: any) => {
    setAchievements(prev => 
      prev.map(achievement => 
        achievement.id === updatedAchievement.id ? updatedAchievement : achievement
      )
    )
    setEditingAchievement(null)
    setIsDialogOpen(false)
  }
  
  // Handle achievement deletion
  const handleDelete = async (id: string) => {
    if (confirm('Are you sure you want to delete this achievement?')) {
      const result = await deleteAchievement(id)
      if (result) {
        setAchievements(prev => prev.filter(achievement => achievement.id !== id))
      }
    }
  }
  
  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold">Achievements</h2>
        
        {canEditAchievements && (
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="mr-2 h-4 w-4" /> Add Achievement
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[500px]">
              <DialogHeader>
                <DialogTitle>
                  {editingAchievement ? 'Edit Achievement' : 'Add New Achievement'}
                </DialogTitle>
              </DialogHeader>
              <AchievementEditor 
                studentId={student.id}
                teacherId={teacherId}
                achievement={editingAchievement || undefined}
                onSuccess={editingAchievement ? handleUpdateSuccess : handleCreateSuccess}
                onCancel={() => {
                  setEditingAchievement(null)
                  setIsDialogOpen(false)
                }}
              />
            </DialogContent>
          </Dialog>
        )}
      </div>
      
      {error && (
        <div className="p-4 mb-4 text-red-700 bg-red-100 rounded-md">
          {error}
        </div>
      )}
      
      {/* Achievement List */}
      {achievements.length === 0 ? (
        <div className="text-center py-12 bg-gray-50 rounded-lg border border-gray-200">
          <Award className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-4 text-lg font-medium text-gray-900">No achievements yet</h3>
          <p className="mt-2 text-sm text-gray-500">
            {canEditAchievements ? 
              'Click the "Add Achievement" button to add one.' : 
              'Check back later for student achievements.'
            }
          </p>
        </div>
      ) : (
        <div className="space-y-4">
          {achievements.map((achievement) => (
            <Card key={achievement.id} className="overflow-hidden">
              <CardContent className="p-4 flex justify-between items-start">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    <h3 className="font-medium">
                      {subjectDisplayNames[achievement.subject] || achievement.subject}
                    </h3>
                    <Badge variant="outline">
                      {achievement.grade}
                    </Badge>
                  </div>
                  <p className="text-gray-700 mb-2">{achievement.description}</p>
                  <p className="text-sm text-gray-500">
                    Date: {new Date(achievement.achievement_date).toLocaleDateString()}
                  </p>
                </div>
                
                {canEditAchievements && (
                  <div className="flex space-x-2">
                    <Button 
                      variant="outline" 
                      size="icon"
                      onClick={() => {
                        setEditingAchievement(achievement as AchievementData)
                        setIsDialogOpen(true)
                      }}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button 
                      variant="outline" 
                      size="icon"
                      onClick={() => handleDelete(achievement.id)}
                      disabled={loading}
                    >
                      <Trash className="h-4 w-4" />
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  )
} 