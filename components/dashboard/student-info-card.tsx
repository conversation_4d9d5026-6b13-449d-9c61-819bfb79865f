"use client"

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { User, CalendarDays, MapPin, School, BookOpen, Users } from "lucide-react"
import type { Student } from "@/types"

interface StudentInfoCardProps {
  student: Student & {
    birth_date?: string;
    address?: string;
    gender?: string;
    enrollment_date?: string;
    parentName?: string;
    className?: string;
  }
}

export default function StudentInfoCard({ student }: StudentInfoCardProps) {
  // Format dates for display
  const formatDate = (dateString?: string) => {
    if (!dateString) return "Tidak diketahui"
    
    try {
      const date = new Date(dateString)
      return new Intl.DateTimeFormat('id-ID', {
        day: 'numeric',
        month: 'long',
        year: 'numeric'
      }).format(date)
    } catch (error) {
      return dateString
    }
  }
  
  // Format gender for display
  const formatGender = (gender?: string) => {
    if (!gender) return "Tidak diketahui"
    
    return gender === "male" ? "Laki-laki" : "Perempuan"
  }
  
  return (
    <Card className="bg-white shadow-md border border-gray-100">
      <CardHeader className="pb-2">
        <CardTitle>Informasi Santri</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex flex-col items-center text-center mb-2">
          <Avatar className="w-24 h-24 mb-3">
            <AvatarImage src={student.photo} alt={student.name} />
            <AvatarFallback className="text-lg">{student.name.charAt(0)}</AvatarFallback>
          </Avatar>
          <h3 className="text-xl font-semibold">{student.name}</h3>
          <p className="text-sm text-gray-500">{student.batch}</p>
        </div>
        
        <Separator />
        
        <div className="space-y-3">
          <div className="flex items-center gap-2">
            <User className="h-4 w-4 text-gray-400" />
            <span className="text-gray-500 text-sm w-24">Jenis Kelamin</span>
            <span className="font-medium">{formatGender(student.gender)}</span>
          </div>
          
          <div className="flex items-center gap-2">
            <CalendarDays className="h-4 w-4 text-gray-400" />
            <span className="text-gray-500 text-sm w-24">Tanggal Lahir</span>
            <span className="font-medium">{formatDate(student.birth_date)}</span>
          </div>
          
          <div className="flex items-center gap-2">
            <MapPin className="h-4 w-4 text-gray-400" />
            <span className="text-gray-500 text-sm w-24">Alamat</span>
            <span className="font-medium">{student.address || "Tidak diketahui"}</span>
          </div>
          
          <div className="flex items-center gap-2">
            <School className="h-4 w-4 text-gray-400" />
            <span className="text-gray-500 text-sm w-24">Kelas</span>
            <span className="font-medium">{student.className || student.batch}</span>
          </div>
          
          <div className="flex items-center gap-2">
            <BookOpen className="h-4 w-4 text-gray-400" />
            <span className="text-gray-500 text-sm w-24">Terdaftar</span>
            <span className="font-medium">{formatDate(student.enrollment_date)}</span>
          </div>
          
          <div className="flex items-center gap-2">
            <Users className="h-4 w-4 text-gray-400" />
            <span className="text-gray-500 text-sm w-24">Orangtua</span>
            <span className="font-medium">{student.parentName || "Tidak diketahui"}</span>
          </div>
        </div>
      </CardContent>
    </Card>
  )
} 