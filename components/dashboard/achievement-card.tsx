"use client"

import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"
import { motion } from "framer-motion"

interface AchievementCardProps {
  title: string
  icon: string
  value: string
  grade: string
  className?: string
  delay?: number
}

// Helper function to get grade color
const getGradeColor = (grade: string) => {
  switch (grade) {
    case "Jayyid Jiddan":
      return "text-emerald-100 bg-emerald-800/50"
    case "Jayyid":
      return "text-emerald-100 bg-emerald-700/50"
    case "Maqbul":
      return "text-emerald-100 bg-emerald-600/50"
    default:
      return "text-emerald-100 bg-emerald-500/50"
  }
}

export default function AchievementCard({ title, icon, value, grade, className, delay = 0 }: AchievementCardProps) {
  return (
    <motion.div
      className={cn("group", className)}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: delay * 0.1 }}
    >
      <p className="text-center text-white/90 mb-2 font-medium">{title}</p>
      <div className="relative bg-white/10 backdrop-blur-sm rounded-xl p-5 shadow-lg border border-white/5 transition-all duration-300 group-hover:bg-white/15 group-hover:translate-y-[-3px]">
        <div className="absolute -top-3 left-1/2 transform -translate-x-1/2 bg-gradient-to-br from-emerald-400 to-emerald-600 rounded-full p-1 w-10 h-10 flex items-center justify-center shadow-lg border-2 border-white/20 group-hover:scale-110 transition-transform duration-300">
          <span className="text-lg">{icon}</span>
        </div>
        <div className="text-center pt-4 mt-2">
          <h3 className="text-2xl md:text-3xl font-bold text-white mb-2">{value}</h3>
          <Badge className={cn("font-medium px-3 py-1", getGradeColor(grade))}>{grade}</Badge>
        </div>
      </div>
    </motion.div>
  )
}

