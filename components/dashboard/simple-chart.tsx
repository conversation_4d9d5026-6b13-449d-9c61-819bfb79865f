"use client"

import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"

interface ChartData {
  name: string
  value: number
  total: number
  color?: string
}

interface SimpleBarChartProps {
  title: string
  description?: string
  data: ChartData[]
}

export function SimpleBarChart({ title, description, data }: SimpleBarChartProps) {
  const maxValue = Math.max(...data.map(d => d.total))

  return (
    <Card>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        {description && <CardDescription>{description}</CardDescription>}
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {data.map((item, index) => (
            <div key={index} className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="font-medium text-sm">{item.name}</span>
                <span className="text-sm text-gray-500">
                  {item.value}/{item.total}
                </span>
              </div>
              <div className="relative">
                <Progress 
                  value={(item.value / item.total) * 100} 
                  className="h-3"
                />
                <div className="absolute inset-0 flex items-center justify-center">
                  <span className="text-xs font-medium text-white">
                    {Math.round((item.value / item.total) * 100)}%
                  </span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}

interface SimpleStatsGridProps {
  stats: Array<{
    label: string
    value: string | number
    change?: string
    changeType?: 'positive' | 'negative' | 'neutral'
  }>
}

export function SimpleStatsGrid({ stats }: SimpleStatsGridProps) {
  return (
    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
      {stats.map((stat, index) => (
        <Card key={index}>
          <CardContent className="p-4">
            <div className="text-center">
              <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
              <p className="text-sm text-gray-600">{stat.label}</p>
              {stat.change && (
                <p className={`text-xs mt-1 ${
                  stat.changeType === 'positive' ? 'text-green-600' : 
                  stat.changeType === 'negative' ? 'text-red-600' : 
                  'text-gray-500'
                }`}>
                  {stat.change}
                </p>
              )}
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}

interface ActivityTimelineProps {
  activities: Array<{
    type: string
    message: string
    time: string
    user?: string
  }>
}

export function ActivityTimeline({ activities }: ActivityTimelineProps) {
  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'student_added':
        return '👤'
      case 'achievement':
        return '🏆'
      case 'timeline_updated':
        return '📝'
      case 'attendance':
        return '📅'
      default:
        return '📋'
    }
  }

  const getActivityColor = (type: string) => {
    switch (type) {
      case 'student_added':
        return 'bg-blue-500'
      case 'achievement':
        return 'bg-yellow-500'
      case 'timeline_updated':
        return 'bg-green-500'
      case 'attendance':
        return 'bg-purple-500'
      default:
        return 'bg-gray-500'
    }
  }

  return (
    <div className="space-y-4">
      {activities.map((activity, index) => (
        <div key={index} className="flex items-start gap-3">
          <div className={`w-8 h-8 rounded-full flex items-center justify-center text-white text-sm ${getActivityColor(activity.type)}`}>
            {getActivityIcon(activity.type)}
          </div>
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium text-gray-900">
              {activity.message}
            </p>
            {activity.user && (
              <p className="text-xs text-gray-600">oleh {activity.user}</p>
            )}
            <p className="text-xs text-gray-500">{activity.time}</p>
          </div>
        </div>
      ))}
    </div>
  )
}

interface PerformanceMetricsProps {
  metrics: Array<{
    subject: string
    average: number
    target: number
    students: number
  }>
}

export function PerformanceMetrics({ metrics }: PerformanceMetricsProps) {
  return (
    <div className="space-y-4">
      {metrics.map((metric, index) => (
        <div key={index} className="p-4 border rounded-lg">
          <div className="flex justify-between items-center mb-2">
            <h4 className="font-medium">{metric.subject}</h4>
            <span className="text-sm text-gray-500">{metric.students} santri</span>
          </div>
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Rata-rata: {metric.average}%</span>
              <span>Target: {metric.target}%</span>
            </div>
            <Progress 
              value={metric.average} 
              className="h-2"
            />
            <div className="flex justify-between text-xs text-gray-500">
              <span>0%</span>
              <span>100%</span>
            </div>
          </div>
        </div>
      ))}
    </div>
  )
}
