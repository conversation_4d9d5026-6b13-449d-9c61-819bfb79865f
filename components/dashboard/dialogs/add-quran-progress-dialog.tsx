"use client"

import { useState } from "react"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { z } from "zod"
import { format } from "date-fns"
import { CalendarIcon, Loader2 } from "lucide-react"
import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Calendar } from "@/components/ui/calendar"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"

// Hard-coded list of surah names for the dropdown
const surahList = [
  "Al-<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>-<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", 
  "<PERSON>-<PERSON>'am", "<PERSON>-<PERSON>'r<PERSON>", "<PERSON>-An<PERSON>l", "At-<PERSON>w<PERSON>", "<PERSON><PERSON>", 
  "<PERSON>d", "<PERSON>", "<PERSON>r-<PERSON>'d", "<PERSON>", "<PERSON>-<PERSON>jr", 
  "<PERSON>-<PERSON>l", "<PERSON>-<PERSON><PERSON>'", "<PERSON>-<PERSON>hf", "<PERSON><PERSON>", "<PERSON>-<PERSON>", 
  "<PERSON>-<PERSON><PERSON><PERSON>", "<PERSON>-<PERSON>jj", "<PERSON>-<PERSON>'min<PERSON>", "An-Nur", "Al-Furqan", 
  "Ash-Shu'ara", "An-Naml", "Al-Qasas", "Al-'Ankabut", "Ar-Rum", 
  "Luqman", "As-Sajdah", "Al-Ahzab", "Saba'", "Fatir", 
  "Ya-Sin", "As-Saffat", "Sad", "Az-Zumar", "Ghafir", 
  "Fussilat", "Ash-Shura", "Az-Zukhruf", "Ad-Dukhan", "Al-Jathiyah", 
  "Al-Ahqaf", "Muhammad", "Al-Fath", "Al-Hujurat", "Qaf", 
  "Adh-Dhariyat", "At-Tur", "An-Najm", "Al-Qamar", "Ar-Rahman", 
  "Al-Waqi'ah", "Al-Hadid", "Al-Mujadilah", "Al-Hashr", "Al-Mumtahinah", 
  "As-Saff", "Al-Jumu'ah", "Al-Munafiqun", "At-Taghabun", "At-Talaq", 
  "At-Tahrim", "Al-Mulk", "Al-Qalam", "Al-Haqqah", "Al-Ma'arij", 
  "Nuh", "Al-Jinn", "Al-Muzzammil", "Al-Muddaththir", "Al-Qiyamah", 
  "Al-Insan", "Al-Mursalat", "An-Naba'", "An-Nazi'at", "Abasa", 
  "At-Takwir", "Al-Infitar", "Al-Mutaffifin", "Al-Inshiqaq", "Al-Buruj", 
  "At-Tariq", "Al-A'la", "Al-Ghashiyah", "Al-Fajr", "Al-Balad", 
  "Ash-Shams", "Al-Layl", "Ad-Duha", "Ash-Sharh", "At-Tin", 
  "Al-'Alaq", "Al-Qadr", "Al-Bayyinah", "Az-Zalzalah", "Al-'Adiyat", 
  "Al-Qari'ah", "At-Takathur", "Al-'Asr", "Al-Humazah", "Al-Fil", 
  "Quraysh", "Al-Ma'un", "Al-Kawthar", "Al-Kafirun", "An-Nasr", 
  "Al-Masad", "Al-Ikhlas", "Al-Falaq", "An-Nas"
]

// Form schema
const formSchema = z.object({
  surah: z.string().min(1, { message: "Pilih surah" }),
  start_verse: z.coerce.number().min(1, { message: "Masukkan ayat awal" }),
  end_verse: z.coerce.number().min(1, { message: "Masukkan ayat akhir" }),
  status: z.enum(["memorized", "in_progress", "review_needed"], {
    required_error: "Pilih status",
  }),
  notes: z.string().optional(),
  progress_date: z.date({
    required_error: "Pilih tanggal",
  }),
  teacher_id: z.string().default("1"), // Default teacher ID until we have proper teacher selection
})

interface AddQuranProgressDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSubmit: (data: FormData) => Promise<boolean>
  studentId: string
}

// Define the form data type
type FormData = z.infer<typeof formSchema> & { student_id: string }

export default function AddQuranProgressDialog({
  open,
  onOpenChange,
  onSubmit,
  studentId,
}: AddQuranProgressDialogProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  
  // Initialize form with default values
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      surah: "",
      start_verse: 1,
      end_verse: 1,
      status: "in_progress",
      notes: "",
      progress_date: new Date(),
    },
  })
  
  // Handle form submission
  const handleSubmit = async (data: z.infer<typeof formSchema>) => {
    setIsSubmitting(true)
    
    try {
      // Add student ID to form data
      const formData: FormData = {
        ...data,
        student_id: studentId,
      }
      
      const success = await onSubmit(formData)
      
      if (success) {
        // Reset form on success
        form.reset()
      }
    } catch (error) {
      console.error("Error submitting form:", error)
    } finally {
      setIsSubmitting(false)
    }
  }
  
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Tambah Progress Hafalan</DialogTitle>
          <DialogDescription>
            Masukkan detail progress hafalan santri
          </DialogDescription>
        </DialogHeader>
        
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="surah"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Surah</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Pilih surah" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent className="max-h-[200px]">
                      {surahList.map((surah) => (
                        <SelectItem key={surah} value={surah}>
                          {surah}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="start_verse"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Ayat Awal</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        min={1}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="end_verse"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Ayat Akhir</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        min={1}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            
            <FormField
              control={form.control}
              name="status"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Status</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Pilih status" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="memorized">Hafal</SelectItem>
                      <SelectItem value="in_progress">Proses</SelectItem>
                      <SelectItem value="review_needed">Perlu Diulang</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="progress_date"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>Tanggal</FormLabel>
                  <Popover>
                    <PopoverTrigger asChild>
                      <FormControl>
                        <Button
                          variant={"outline"}
                          className={cn(
                            "pl-3 text-left font-normal",
                            !field.value && "text-muted-foreground"
                          )}
                        >
                          {field.value ? (
                            format(field.value, "PP")
                          ) : (
                            <span>Pilih tanggal</span>
                          )}
                          <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                        </Button>
                      </FormControl>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="single"
                        selected={field.value}
                        onSelect={field.onChange}
                        disabled={(date) =>
                          date > new Date() || date < new Date("2020-01-01")
                        }
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Catatan</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Catatan mengenai progress hafalan"
                      className="resize-none"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
              >
                Batal
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Menyimpan...
                  </>
                ) : (
                  "Simpan"
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
} 