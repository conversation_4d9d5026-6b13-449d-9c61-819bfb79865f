"use client"

import Image from "next/image"
import { motion } from "framer-motion"
import type { Student } from "@/types"
import { Badge } from "@/components/ui/badge"
import AchievementCard from "./achievement-card"

interface StudentAchievementDashboardProps {
  student: Student
}

export default function StudentAchievementDashboard({ student }: StudentAchievementDashboardProps) {
  return (
    <motion.div
      className="relative w-full max-w-5xl mx-auto bg-gradient-to-br from-emerald-600 to-emerald-700 rounded-xl overflow-hidden shadow-2xl"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      {/* Background pattern */}
      <div
        className="absolute inset-0 w-full h-full opacity-10"
        style={{
          backgroundImage:
            "url(\"data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fillRule='evenodd'%3E%3Cg fill='%23ffffff' fillOpacity='1'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\")",
          backgroundSize: "60px 60px",
        }}
      />

      {/* Content container */}
      <div className="relative z-10 p-6 md:p-8">
        {/* Header with logos and title */}
        <motion.div
          className="flex flex-col md:flex-row justify-between items-center mb-8"
          initial={{ y: -20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.5 }}
        >
          <div className="flex items-center space-x-4 mb-4 md:mb-0">
            <motion.div
              className="w-16 h-16 bg-white/10 backdrop-blur-sm rounded-full p-2 flex items-center justify-center"
              whileHover={{ scale: 1.05 }}
              transition={{ type: "spring", stiffness: 300 }}
            >
              <Image
                src="/placeholder.svg?height=64&width=64"
                alt="Logo PTQ"
                width={64}
                height={64}
                className="object-contain"
              />
            </motion.div>
            <motion.div
              className="w-16 h-16 bg-white/10 backdrop-blur-sm rounded-full p-2 flex items-center justify-center"
              whileHover={{ scale: 1.05 }}
              transition={{ type: "spring", stiffness: 300 }}
            >
              <Image
                src="/placeholder.svg?height=64&width=64"
                alt="Logo Al Ihsan"
                width={64}
                height={64}
                className="object-contain"
              />
            </motion.div>
          </div>

          {/* Title */}
          <div className="text-center md:text-right">
            <h1 className="text-3xl md:text-4xl font-bold text-white tracking-tight">Total Pencapaian Santri</h1>
            <p className="text-lg md:text-xl text-white/80 font-light">selama belajar di PTQ Al Ihsan</p>
          </div>
        </motion.div>

        {/* Main content */}
        <div className="flex flex-col lg:flex-row gap-8">
          {/* Student photo and info */}
          <motion.div
            className="w-full lg:w-1/3 relative"
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <div className="bg-emerald-800/50 backdrop-blur-sm rounded-2xl p-6 shadow-lg transform transition-all duration-300 hover:translate-y-[-5px]">
              {/* Student photo with decorative elements */}
              <div className="relative mb-6">
                {/* Arch background */}
                <div className="absolute top-0 left-0 right-0 h-48 bg-gradient-to-b from-emerald-900 to-transparent rounded-t-full opacity-50" />

                {/* Sparkle decorations */}
                <div className="absolute top-4 left-4 text-yellow-300 text-3xl animate-pulse">✦</div>
                <div
                  className="absolute top-12 right-8 text-yellow-300 text-2xl animate-pulse"
                  style={{ animationDelay: "0.5s" }}
                >
                  ✧
                </div>
                <div
                  className="absolute top-20 left-12 text-yellow-300 text-4xl animate-pulse"
                  style={{ animationDelay: "1s" }}
                >
                  ✦
                </div>

                {/* Student photo */}
                <div className="relative z-10 flex justify-center">
                  <motion.div
                    className="relative w-48 h-64 rounded-xl overflow-hidden border-4 border-white/20 shadow-lg"
                    whileHover={{ scale: 1.03 }}
                    transition={{ type: "spring", stiffness: 300 }}
                  >
                    <Image
                      src={student.photo || "/placeholder.svg"}
                      alt={`Foto ${student.name}`}
                      fill
                      className="object-cover"
                    />
                  </motion.div>
                </div>
              </div>

              {/* Student name and batch */}
              <div className="text-center">
                <h2 className="text-2xl font-bold text-white mb-3">{student.name}</h2>
                <Badge className="bg-yellow-400 hover:bg-yellow-500 text-emerald-900 font-medium px-4 py-1.5 text-sm rounded-full">
                  {student.batch}
                </Badge>
              </div>
            </div>
          </motion.div>

          {/* Achievement cards */}
          <motion.div
            className="w-full lg:w-2/3 grid grid-cols-1 sm:grid-cols-2 gap-4 md:gap-6"
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
          >
            <AchievementCard
              title="Hafalan Al Quran"
              icon="📖"
              value={student.achievements.alQuran.value}
              grade={student.achievements.alQuran.grade}
              delay={1}
            />

            <AchievementCard
              title="Hafalan Hadits Arbain"
              icon="📖"
              value={student.achievements.haditsArbain.value}
              grade={student.achievements.haditsArbain.grade}
              delay={2}
            />

            <AchievementCard
              title="Hafalan Ushul Tsalatsah"
              icon="📖"
              value={student.achievements.ushulTsalatsah.value}
              grade={student.achievements.ushulTsalatsah.grade}
              delay={3}
            />

            <AchievementCard
              title="Hafalan Ghoyah Wa Taqrib"
              icon="📖"
              value={student.achievements.ghoyahWaTaqrib.value}
              grade={student.achievements.ghoyahWaTaqrib.grade}
              delay={4}
            />

            <AchievementCard
              title="Hafalan Al Ajurumiyyah"
              icon="📖"
              value={student.achievements.alAjurumiyyah.value}
              grade={student.achievements.alAjurumiyyah.grade}
              delay={5}
            />

            <AchievementCard
              title="Kemampuan Baca Kitab Gundul"
              icon="📜"
              value={student.achievements.kitabGundul.value}
              grade={student.achievements.kitabGundul.grade}
              delay={6}
            />

            <AchievementCard
              title="Kemampuan Public Speaking"
              icon="📚"
              value={student.achievements.publicSpeaking.value}
              grade={student.achievements.publicSpeaking.grade}
              className="sm:col-span-2 md:col-span-1"
              delay={7}
            />
          </motion.div>
        </div>
      </div>
    </motion.div>
  )
}

