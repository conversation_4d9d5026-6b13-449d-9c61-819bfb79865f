"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useSearchParams } from "next/navigation"
import { Search, User, ArrowLeft, GraduationCap, Filter, Loader2 } from "lucide-react"
import StudentAchievementDashboard from "./student-achievement-dashboard"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'

type Student = {
  id: string
  student_id: string
  name: string
  gender: "male" | "female"
  birth_date: string | null
  address: string | null
  photo_url: string | null
  batch: string | null
  status: "active" | "inactive" | "graduated" | "suspended"
}

export default function ParentDashboard() {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedStudent, setSelectedStudent] = useState<Student | null>(null)
  const [filter, setFilter] = useState<string>("all")
  const [mounted, setMounted] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [students, setStudents] = useState<Student[]>([])
  const [batches, setBatches] = useState<string[]>([])
  const [parentName, setParentName] = useState("")

  const supabase = createClientComponentClient()
  const router = useRouter()
  const searchParams = useSearchParams()
  const studentIdParam = searchParams.get('student_id')

  // Fetch students data for the logged in parent
  useEffect(() => {
    const fetchStudents = async () => {
      try {
        setIsLoading(true)
        
        // Get current user
        const { data: { user }, error: userError } = await supabase.auth.getUser()
        
        if (userError || !user) {
          throw new Error("Anda harus login untuk mengakses halaman ini")
        }
        
        // Get parent profile
        const { data: parentProfile, error: parentError } = await supabase
          .from('profiles')
          .select('id, name')
          .eq('id', user.id)
          .single()
          
        if (parentError || !parentProfile) {
          throw new Error("Profil tidak ditemukan")
        }
        
        setParentName(parentProfile.name)
        
        // Get parent record
        const { data: parent, error: parentRecordError } = await supabase
          .from('parents')
          .select('id')
          .eq('profile_id', user.id)
          .single()
          
        if (parentRecordError || !parent) {
          throw new Error("Data orangtua tidak ditemukan")
        }
        
        // Get student-parent relationships
        const { data: studentParents, error: relationshipError } = await supabase
          .from('student_parent')
          .select('student_id')
          .eq('parent_id', parent.id)
          
        if (relationshipError || !studentParents || studentParents.length === 0) {
          throw new Error("Tidak ada data santri yang terkait dengan akun ini")
        }
        
        const studentIds = studentParents.map(sp => sp.student_id)
        
        // Get students data
        const { data: studentsData, error: studentsError } = await supabase
          .from('students')
          .select('*')
          .in('id', studentIds)
          
        if (studentsError) {
          throw new Error("Gagal memuat data santri")
        }
        
        setStudents(studentsData || [])
        
        // Extract unique batches for filtering
        const uniqueBatches = Array.from(new Set(studentsData?.map(s => s.batch).filter(Boolean) || []))
        setBatches(uniqueBatches as string[])
        
        // If student_id is provided in URL, select that student
        if (studentIdParam) {
          const student = studentsData?.find(s => s.id === studentIdParam) || null
          setSelectedStudent(student)
        }
        
      } catch (err) {
        setError(err instanceof Error ? err.message : "Terjadi kesalahan saat memuat data")
        console.error("Error fetching data:", err)
      } finally {
        setIsLoading(false)
      }
    }
    
    fetchStudents()
    setMounted(true)
  }, [supabase, studentIdParam])

  // Filter students based on search query and batch filter
  const filteredStudents = students.filter((student) => {
    const matchesSearch =
      student.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (student.batch && student.batch.toLowerCase().includes(searchQuery.toLowerCase()))
    const matchesFilter = filter === "all" || student.batch === filter

    return matchesSearch && matchesFilter
  })

  if (!mounted) return null

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-12 w-12 animate-spin mx-auto text-emerald-600" />
          <h2 className="mt-4 text-xl font-semibold">Memuat data...</h2>
          <p className="text-gray-500 mt-2">Mohon tunggu sebentar</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 p-6 flex items-center justify-center">
        <div className="w-full max-w-md">
          <Alert variant="destructive" className="mb-4">
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
          <Button onClick={() => router.push('/')} className="w-full">
            Kembali ke Beranda
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <header className="bg-gradient-to-r from-emerald-700 to-emerald-800 text-white p-4 shadow-lg">
        <div className="container mx-auto flex flex-col md:flex-row justify-between items-center">
          <div className="flex items-center gap-3">
            <GraduationCap className="h-8 w-8" />
            <h1 className="text-2xl font-bold mb-4 md:mb-0">PTQ Al Ihsan - Portal Orang Tua</h1>
          </div>
          <div className="flex items-center gap-3">
            <Avatar className="border-2 border-white/20">
              <AvatarImage src="/placeholder.svg?height=40&width=40" alt="Avatar" />
              <AvatarFallback>{parentName.substring(0, 2).toUpperCase()}</AvatarFallback>
            </Avatar>
            <div>
              <span className="font-medium">{parentName}</span>
              <p className="text-xs text-white/70">Akses Dashboard Santri</p>
            </div>
          </div>
        </div>
      </header>

      <main className="container mx-auto p-4 md:p-6">
        {!selectedStudent ? (
          <Card className="mb-8 border-none shadow-xl">
            <CardContent className="p-6 md:p-8">
              <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4">
                <div>
                  <h2 className="text-2xl font-bold mb-1">Daftar Santri</h2>
                  <p className="text-gray-500">Pilih santri untuk melihat pencapaian</p>
                </div>

                <Tabs defaultValue="grid" className="w-full md:w-auto">
                  <TabsList className="grid w-full grid-cols-2 md:w-[200px]">
                    <TabsTrigger value="grid">Grid</TabsTrigger>
                    <TabsTrigger value="list">List</TabsTrigger>
                  </TabsList>

                  <TabsContent value="grid" className="mt-0">
                    <div className="flex flex-col md:flex-row gap-3 mt-6">
                      <div className="relative flex-grow">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                        <Input
                          type="text"
                          placeholder="Cari nama santri..."
                          value={searchQuery}
                          onChange={(e) => setSearchQuery(e.target.value)}
                          className="pl-10 py-6 bg-gray-50 border-gray-200"
                        />
                      </div>

                      {batches.length > 0 && (
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="outline" className="gap-2">
                              <Filter className="h-4 w-4" />
                              Filter Angkatan
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end" className="w-56">
                            <DropdownMenuItem onClick={() => setFilter("all")}>Semua Angkatan</DropdownMenuItem>
                            {batches.map((batch) => (
                              <DropdownMenuItem key={batch} onClick={() => setFilter(batch)}>
                                {batch}
                              </DropdownMenuItem>
                            ))}
                          </DropdownMenuContent>
                        </DropdownMenu>
                      )}
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-6">
                      {filteredStudents.length > 0 ? (
                        filteredStudents.map((student) => (
                          <Card
                            key={student.id}
                            className="cursor-pointer hover:shadow-lg transition-all duration-300 hover:border-emerald-200 hover:translate-y-[-2px] overflow-hidden group"
                            onClick={() => setSelectedStudent(student)}
                          >
                            <div className="h-2 bg-emerald-600 group-hover:bg-emerald-500 transition-colors" />
                            <CardContent className="p-6 flex items-center gap-4">
                              <Avatar className="h-16 w-16 border-2 border-emerald-100">
                                <AvatarImage src={student.photo_url || ''} alt={student.name} />
                                <AvatarFallback>
                                  <User />
                                </AvatarFallback>
                              </Avatar>
                              <div>
                                <h3 className="font-semibold text-lg">{student.name}</h3>
                                {student.batch && (
                                  <Badge variant="outline" className="mt-1 text-xs font-normal">
                                    {student.batch}
                                  </Badge>
                                )}
                              </div>
                            </CardContent>
                          </Card>
                        ))
                      ) : (
                        <div className="col-span-full text-center py-12 text-gray-500">
                          <div className="flex flex-col items-center gap-2">
                            <User className="h-12 w-12 text-gray-300" />
                            <h3 className="text-lg font-medium">Tidak ada santri yang ditemukan</h3>
                            <p>Coba kata kunci lain atau ubah filter angkatan</p>
                          </div>
                        </div>
                      )}
                    </div>
                  </TabsContent>

                  <TabsContent value="list" className="mt-0">
                    <div className="flex flex-col md:flex-row gap-3 mt-6">
                      <div className="relative flex-grow">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                        <Input
                          type="text"
                          placeholder="Cari nama santri..."
                          value={searchQuery}
                          onChange={(e) => setSearchQuery(e.target.value)}
                          className="pl-10 py-6 bg-gray-50 border-gray-200"
                        />
                      </div>

                      {batches.length > 0 && (
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="outline" className="gap-2">
                              <Filter className="h-4 w-4" />
                              Filter Angkatan
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end" className="w-56">
                            <DropdownMenuItem onClick={() => setFilter("all")}>Semua Angkatan</DropdownMenuItem>
                            {batches.map((batch) => (
                              <DropdownMenuItem key={batch} onClick={() => setFilter(batch)}>
                                {batch}
                              </DropdownMenuItem>
                            ))}
                          </DropdownMenuContent>
                        </DropdownMenu>
                      )}
                    </div>

                    <div className="mt-6">
                      {filteredStudents.length > 0 ? (
                        <Card>
                          <table className="w-full">
                            <thead>
                              <tr className="border-b">
                                <th className="text-left p-4">Nama Santri</th>
                                <th className="text-left p-4 hidden md:table-cell">Angkatan</th>
                                <th className="text-right p-4">Aksi</th>
                              </tr>
                            </thead>
                            <tbody>
                              {filteredStudents.map((student) => (
                                <tr key={student.id} className="border-b hover:bg-gray-50">
                                  <td className="p-4">
                                    <div className="flex items-center gap-3">
                                      <Avatar className="h-10 w-10">
                                        <AvatarImage src={student.photo_url || ''} alt={student.name} />
                                        <AvatarFallback>
                                          <User />
                                        </AvatarFallback>
                                      </Avatar>
                                      <span className="font-medium">{student.name}</span>
                                    </div>
                                  </td>
                                  <td className="p-4 hidden md:table-cell text-gray-500">{student.batch || '-'}</td>
                                  <td className="p-4 text-right">
                                    <Button variant="outline" size="sm" onClick={() => setSelectedStudent(student)}>
                                      Lihat Detail
                                    </Button>
                                  </td>
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        </Card>
                      ) : (
                        <div className="text-center py-12 text-gray-500">
                          <div className="flex flex-col items-center gap-2">
                            <User className="h-12 w-12 text-gray-300" />
                            <h3 className="text-lg font-medium">Tidak ada santri yang ditemukan</h3>
                            <p>Coba kata kunci lain atau ubah filter angkatan</p>
                          </div>
                        </div>
                      )}
                    </div>
                  </TabsContent>
                </Tabs>
              </div>
            </CardContent>
          </Card>
        ) : (
          <div className="mb-6 space-y-6">
            <Button variant="outline" onClick={() => setSelectedStudent(null)} className="gap-2 shadow-sm">
              <ArrowLeft className="h-4 w-4" />
              Kembali ke Daftar Santri
            </Button>
            <StudentAchievementDashboard student={selectedStudent} />
          </div>
        )}
      </main>

      <footer className="bg-gray-100 border-t py-6 mt-12">
        <div className="container mx-auto text-center text-gray-500 text-sm">
          &copy; {new Date().getFullYear()} PTQ Al Ihsan. Hak Cipta Dilindungi.
        </div>
      </footer>
    </div>
  )
}

