# Subjects Feature Documentation

## Overview
The Subjects feature provides management capabilities for educational subjects in the Al Ihsan school management system. It allows administrators to create, update, and manage subjects, including their learning objectives and teacher relationships.

## Database Schema
The subjects feature uses the following tables:

### `subjects` Table
- `id` - UUID primary key
- `name` - Subject name (VARCHAR 255)
- `category` - Subject category (VARCHAR 100) 
- `description` - Subject description (TEXT)
- `is_active` - Active status (BOOLEAN)
- `created_at` - Creation timestamp
- `updated_at` - Update timestamp

### `subject_objectives` Table
- `id` - UUID primary key
- `subject_id` - Foreign key to subjects table
- `objective` - Learning objective text
- `created_at` - Creation timestamp
- `updated_at` - Update timestamp

### `subject_teachers` Table
- `id` - UUID primary key
- `subject_id` - Foreign key to subjects table
- `teacher_id` - Foreign key to teachers table
- `created_at` - Creation timestamp

## API Endpoints

### GET `/api/subjects`
Retrieves a list of subjects with optional filtering.

**Query Parameters:**
- `search` - Filter subjects by name (optional)
- `category` - Filter subjects by category (optional)

**Response:**
```json
{
  "subjects": [
    {
      "id": "uuid",
      "name": "Al-Quran",
      "category": "Keagamaan",
      "description": "...",
      "is_active": true,
      "created_at": "timestamp",
      "updated_at": "timestamp",
      "teacherCount": 3
    },
    // more subjects...
  ]
}
```

### GET `/api/subjects/:id`
Retrieves a specific subject by ID, including its learning objectives and assigned teachers.

**Response:**
```json
{
  "subject": {
    "id": "uuid",
    "name": "Al-Quran",
    "category": "Keagamaan",
    "description": "...",
    "is_active": true,
    "created_at": "timestamp",
    "updated_at": "timestamp"
  },
  "objectives": [
    {
      "id": "uuid",
      "subject_id": "uuid",
      "objective": "Learning objective text",
      "created_at": "timestamp"
    },
    // more objectives...
  ],
  "teachers": [
    {
      "id": "uuid",
      "name": "Teacher Name",
      "teacher_id": "TCH-001",
      "specialization": "Specialization"
    },
    // more teachers...
  ]
}
```

### POST `/api/subjects`
Creates a new subject.

**Request Body:**
```json
{
  "name": "Subject Name",
  "category": "Category",
  "description": "Description",
  "isActive": true,
  "learningObjectives": ["Objective 1", "Objective 2"]
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "uuid",
    "name": "Subject Name",
    "category": "Category",
    "description": "Description",
    "is_active": true,
    "created_at": "timestamp",
    "updated_at": "timestamp"
  }
}
```

### PUT `/api/subjects`
Updates an existing subject.

**Request Body:**
```json
{
  "id": "uuid",
  "name": "Updated Name",
  "category": "Updated Category",
  "description": "Updated Description",
  "isActive": true,
  "learningObjectives": ["Updated Objective 1", "Updated Objective 2"]
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "uuid",
    "name": "Updated Name",
    "category": "Updated Category",
    "description": "Updated Description",
    "is_active": true,
    "created_at": "timestamp",
    "updated_at": "timestamp"
  }
}
```

### DELETE `/api/subjects?id=:id`
Deletes a subject by ID.

**Query Parameters:**
- `id` - The ID of the subject to delete

**Response:**
```json
{
  "success": true
}
```

## Components

### SubjectList
Displays a list of subjects with filtering, search and pagination.

**Props:**
- `initialSubjects` - Initial subjects data (optional, for server-side rendering)

### SubjectForm
Form for creating or editing a subject.

**Props:**
- `subjectId` - ID of the subject to edit (optional, if not provided form will be in create mode)

## Access Control
- Administrators have full CRUD access to all subjects and related data
- Teachers have read-only access to subjects and related data
- All authenticated users can read subjects data

## Server-Side Rendering
The main subjects list page uses server-side rendering via the `getAllSubjects` function to provide the initial subjects data, improving performance and SEO. 