# Panduan Memperbaiki Error Achievements

## 🚨 Ma<PERSON>ah yang <PERSON>tem<PERSON>

### Error Console:
```
Failed to load achievements: "Failed to fetch achievements"
Source: app/dashboard/achievements/page.tsx (139:17)
```

### Penyebab Utama:
1. **Tabel `achievements` belum dibuat** di database Supabase
2. API `/api/achievements` mencoba mengakses tabel yang tidak ada
3. RLS (Row Level Security) policies belum dikonfigurasi

## 🔧 Langkah Perbaikan

### 1. Buat Tabel Achievements
Jalankan script berikut di **Supabase SQL Editor**:

```sql
-- Jalankan script: scripts/create-achievements-table.sql
\i scripts/create-achievements-table.sql
```

Atau copy-paste script dari file `scripts/create-achievements-table.sql`

### 2. Verifikasi Tabel Telah Dibuat
```sql
-- Periksa struktur tabel
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'achievements' 
ORDER BY ordinal_position;

-- <PERSON><PERSON><PERSON> jum<PERSON> records
SELECT COUNT(*) as total_achievements FROM achievements;
```

### 3. Test API Achievements
Setelah tabel dibuat, test API dengan:

```bash
# Test GET achievements
curl -X GET "http://localhost:3001/api/achievements" \
  -H "Authorization: Bearer YOUR_TOKEN"

# Test POST achievement (contoh)
curl -X POST "http://localhost:3001/api/achievements" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "student_id": "student_uuid",
    "subject_id": "subject_uuid", 
    "value": "Hafal Juz 30",
    "grade": "A",
    "notes": "Sangat baik",
    "verified_by": "teacher_uuid",
    "achievement_date": "2024-01-15"
  }'
```

## 📋 Struktur Tabel Achievements

```sql
CREATE TABLE achievements (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    student_id UUID NOT NULL,
    subject_id UUID NOT NULL,
    value TEXT NOT NULL,
    grade VARCHAR(10),
    notes TEXT,
    verified_by UUID,
    achievement_date DATE NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

### Foreign Key Relations:
- `student_id` → `students.id`
- `subject_id` → `subjects.id`
- `verified_by` → `teachers.id`

## 🔐 RLS Policies

### Admin Policies:
```sql
-- Admin dapat melakukan semua operasi
CREATE POLICY achievements_admin_all ON achievements
    FOR ALL TO authenticated
    USING (auth.uid() IN (SELECT id FROM profiles WHERE role = 'admin'));
```

### Teacher Policies:
```sql
-- Teacher dapat membaca semua, insert/update yang mereka verifikasi
CREATE POLICY achievements_teacher_read ON achievements
    FOR SELECT TO authenticated
    USING (auth.uid() IN (SELECT profile_id FROM teachers));

CREATE POLICY achievements_teacher_insert ON achievements
    FOR INSERT TO authenticated
    WITH CHECK (auth.uid() IN (SELECT profile_id FROM teachers WHERE id = verified_by));
```

### Parent Policies:
```sql
-- Parent hanya bisa melihat pencapaian anak mereka
CREATE POLICY achievements_parent_read ON achievements
    FOR SELECT TO authenticated
    USING (auth.uid() IN (
        SELECT p.profile_id 
        FROM parents p
        JOIN student_parent sp ON p.id = sp.parent_id
        WHERE sp.student_id = achievements.student_id
    ));
```

## 🧪 Testing Checklist

### ✅ Database Level:
- [ ] Tabel `achievements` sudah dibuat
- [ ] Indexes sudah dibuat
- [ ] RLS policies sudah aktif
- [ ] Sample data bisa diinsert

### ✅ API Level:
- [ ] GET `/api/achievements` return 200
- [ ] POST `/api/achievements` bisa create data
- [ ] PUT `/api/achievements` bisa update data
- [ ] DELETE `/api/achievements` bisa hapus data

### ✅ Frontend Level:
- [ ] Halaman achievements terbuka tanpa error
- [ ] Data achievements tampil di list
- [ ] Form create achievement berfungsi
- [ ] Form edit achievement berfungsi
- [ ] Delete achievement berfungsi

## 🚀 Menu Timeline-Input Sudah Ditambahkan

### Lokasi Menu:
- **Path:** `/dashboard/teacher/timeline-input`
- **Icon:** PenTool
- **Label:** "Input Timeline"

### Fitur Timeline-Input:
- ✅ Input data timeline per kelas
- ✅ Input per mata pelajaran
- ✅ Input nilai dan catatan
- ✅ Input perilaku siswa
- ✅ Input aktivitas dan prestasi
- ✅ Bulk input untuk semua siswa
- ✅ Import dari Excel
- ✅ History timeline

## 🔍 Troubleshooting

### Jika masih error setelah membuat tabel:

1. **Check Authentication:**
   ```javascript
   // Di browser console
   console.log('User session:', await supabase.auth.getSession())
   ```

2. **Check RLS Policies:**
   ```sql
   -- Lihat policies yang aktif
   SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual
   FROM pg_policies 
   WHERE tablename = 'achievements';
   ```

3. **Check API Response:**
   ```javascript
   // Di browser Network tab, lihat response dari /api/achievements
   // Pastikan tidak ada error 500 atau 403
   ```

4. **Temporary Disable RLS (untuk testing):**
   ```sql
   -- HANYA UNTUK TESTING - JANGAN DI PRODUCTION
   ALTER TABLE achievements DISABLE ROW LEVEL SECURITY;
   ```

## 📞 Support

Jika masih ada masalah:
1. Check browser console untuk error details
2. Check Supabase logs di dashboard
3. Check server logs dengan `npm run dev`
4. Refer ke dokumentasi di `docs/CRUD_TROUBLESHOOTING.md`
