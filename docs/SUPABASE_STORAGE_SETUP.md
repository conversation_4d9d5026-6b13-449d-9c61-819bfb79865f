# Supabase Storage Setup untuk Photo Upload

## 🎯 Overview

Untuk mengatasi masalah foto tidak muncul di frontend, kita akan menggunakan Supabase Storage sebagai solusi penyimpanan foto yang reliable.

## 🚨 Masalah Saat Ini

Foto tidak muncul karena:
1. **Path lokal** (`/photos/teachers/ahmad.jpg`) tidak ada di server
2. **Path upload** (`/uploads/teachers/xxx.png`) tidak ter-serve dengan benar
3. **File storage** tidak konsisten antara development dan production

## ✅ Solusi: Supabase Storage

Supabase Storage memberikan:
- **Public URLs** yang bisa diakses dari mana saja
- **CDN** untuk performa yang baik
- **Automatic scaling** dan backup
- **Integration** yang mudah dengan Supabase

## 🛠️ Setup Steps

### 1. **Buat Storage Bucket di Supabase Dashboard**

1. **Login ke Supabase Dashboard**
   ```
   https://app.supabase.com/
   ```

2. **Pilih project Anda**

3. **Go to Storage**
   - Klik "Storage" di sidebar kiri

4. **Create New Bucket**
   - Klik "New bucket"
   - Name: `photos`
   - Public bucket: ✅ **CHECKED** (penting!)
   - Klik "Create bucket"

### 2. **Setup Storage Policies**

Jalankan script SQL di Supabase SQL Editor:

```sql
-- Copy-paste isi file scripts/setup-supabase-storage.sql
```

### 3. **Update Database Schema**

```sql
-- Add storage_path column to website_images
ALTER TABLE website_images 
ADD COLUMN IF NOT EXISTS storage_path TEXT;

CREATE INDEX IF NOT EXISTS idx_website_images_storage_path 
ON website_images(storage_path);
```

### 4. **Deploy New Upload API**

API baru sudah dibuat: `/api/upload-supabase`
- Menggunakan Supabase Storage
- Mengembalikan public URL
- Lebih reliable dan scalable

### 5. **Migrate Existing Photos**

```bash
# Migrate foto yang sudah ada
node scripts/migrate-photos-to-supabase.js
```

## 🧪 Testing

### 1. **Test Storage Bucket**
```javascript
// Di browser console
const { createClient } = supabase
const supabase = createClient('YOUR_URL', 'YOUR_ANON_KEY')

// Test list buckets
const { data, error } = await supabase.storage.listBuckets()
console.log('Buckets:', data)
```

### 2. **Test Upload**
1. Buka form teacher/student
2. Upload foto baru
3. Check console logs
4. Verify foto muncul di preview
5. Submit form dan check database

### 3. **Test Photo Display**
```sql
-- Check new photo URLs
SELECT id, name, photo_url 
FROM teachers 
WHERE photo_url LIKE '%supabase%'
ORDER BY updated_at DESC;
```

## 📁 File Structure

### Supabase Storage Structure:
```
photos/ (bucket)
├── teachers/
│   ├── teacher_uuid_timestamp_random.jpg
│   └── teacher_uuid_timestamp_random.png
├── students/
│   ├── student_uuid_timestamp_random.jpg
│   └── student_uuid_timestamp_random.png
└── general/
    └── general_files...
```

### Database Records:
```sql
-- website_images table
{
  "id": "uuid",
  "key": "teacher_uuid",
  "filename": "teacher_uuid_timestamp.jpg",
  "file_path": "https://xxx.supabase.co/storage/v1/object/public/photos/teachers/teacher_uuid_timestamp.jpg",
  "storage_path": "teachers/teacher_uuid_timestamp.jpg",
  "category": "teachers"
}
```

## 🔄 Migration Process

### Automatic Migration:
```bash
node scripts/migrate-photos-to-supabase.js
```

### Manual Migration:
1. **Download existing photos** dari server lama
2. **Upload ke Supabase Storage** via dashboard atau API
3. **Update database** dengan URL baru
4. **Test display** di frontend

## 🎨 Frontend Changes

### ImageUpload Component:
- ✅ Updated to use `/api/upload-supabase`
- ✅ Handles Supabase Storage URLs
- ✅ Better error handling

### Photo Display:
```typescript
// Photo URLs akan berformat:
// https://xxx.supabase.co/storage/v1/object/public/photos/teachers/filename.jpg

// Tidak perlu perubahan di komponen display
<Image src={teacher.photo_url} alt={teacher.name} />
```

## 🔧 Environment Variables

Pastikan environment variables sudah set:

```env
NEXT_PUBLIC_SUPABASE_URL=https://xxx.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

## 🚨 Troubleshooting

### Bucket tidak ditemukan:
```
Error: The resource was not found
```
**Solution:** Pastikan bucket "photos" sudah dibuat dan public

### Upload gagal (403):
```
Error: new row violates row-level security policy
```
**Solution:** Check storage policies, pastikan user authenticated

### Foto tidak muncul:
```
Error: Failed to load image
```
**Solution:** 
1. Check bucket is public
2. Verify URL format
3. Check CORS settings

### Migration gagal:
```
Error: File not found
```
**Solution:** 
1. Check file paths
2. Verify local files exist
3. Check permissions

## 📊 Benefits

### Before (Local Storage):
- ❌ Path tidak konsisten
- ❌ File tidak accessible
- ❌ Tidak scalable
- ❌ Backup manual

### After (Supabase Storage):
- ✅ Public URLs yang reliable
- ✅ CDN untuk performa
- ✅ Automatic backup
- ✅ Scalable dan secure

## 🎉 Expected Results

Setelah setup selesai:

1. **Upload foto baru** akan tersimpan di Supabase Storage
2. **Foto lama** akan dimigrasikan ke Supabase Storage
3. **Semua foto** akan muncul dengan benar di frontend
4. **URLs** akan berformat: `https://xxx.supabase.co/storage/v1/object/public/photos/...`

## 📞 Quick Start

Untuk setup cepat:

1. **Create bucket "photos"** di Supabase Dashboard (set as public)
2. **Run SQL script:** `scripts/setup-supabase-storage.sql`
3. **Test upload:** Upload foto baru di form
4. **Migrate existing:** `node scripts/migrate-photos-to-supabase.js`
5. **Verify:** Check foto muncul di frontend

Setelah setup ini, semua foto akan tersimpan dan ditampilkan dengan benar! 🎉
