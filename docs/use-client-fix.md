# Perbaikan Error "use client" Directive

## Overview
Dokumentasi ini menjelaskan perbaikan error build yang terjadi karena posisi directive `"use client"` yang salah dalam file React component.

## Error yang Ter<PERSON>di
```
Error: × The "use client" directive must be placed before other expressions. Move it to the top of the file to resolve this issue.
```

## Penyebab Error
Error ini terjadi ketika directive `"use client"` ditempatkan setelah:
- Import statements
- Metadata exports
- Komentar atau kode lainnya

## File yang Diperbaiki

### 1. `/app/program/page.tsx`
**Sebelum perbaikan:**
```typescript
import type { Metadata } from 'next'
"use client"
export const metadata: Metadata = {
  // metadata config
}

import type React from "react"
// ... other imports
```

**Setelah perbaikan:**
```typescript
"use client"

import type React from "react"
import Head from "next/head"
// ... other imports

export default function ProgramPage() {
  return (
    <>
      <Head>
        <title>Program Pendidikan - PTQ <PERSON></title>
        <meta name="description" content="..." />
        {/* other meta tags */}
      </Head>
      <div className="min-h-screen bg-gray-50">
        {/* component content */}
      </div>
    </>
  )
}
```

### 2. `/app/tentang-kami/page.tsx`
**Sebelum perbaikan:**
```typescript
import type { Metadata } from 'next'

export const metadata: Metadata = {
  // metadata config
}

"use client"
import type React from "react"
```

**Setelah perbaikan:**
```typescript
"use client"

import type React from "react"
import { useState, useEffect } from "react"
import Head from "next/head"
// ... other imports

export default function AboutPage() {
  return (
    <>
      <Head>
        <title>Tentang Kami - PTQ Al Ihsan</title>
        <meta name="description" content="..." />
        {/* other meta tags */}
      </Head>
      <div className="min-h-screen bg-gray-50">
        {/* component content */}
      </div>
    </>
  )
}
```

## Aturan Perbaikan

### 1. Posisi "use client"
- **HARUS** ditempatkan di baris pertama file
- **SEBELUM** semua import statements
- **SEBELUM** export statements
- **SEBELUM** komentar atau kode lainnya

### 2. Metadata Handling
Karena client components tidak bisa menggunakan `export const metadata`, ada dua solusi:

#### Solusi A: Menggunakan Head Component (Yang Dipilih)
```typescript
"use client"

import Head from "next/head"

export default function MyPage() {
  return (
    <>
      <Head>
        <title>Page Title</title>
        <meta name="description" content="Page description" />
      </Head>
      {/* page content */}
    </>
  )
}
```

#### Solusi B: Memisahkan Server dan Client Components
```typescript
// page.tsx (Server Component)
import type { Metadata } from 'next'
import ClientComponent from './client-component'

export const metadata: Metadata = {
  title: 'Page Title',
  description: 'Page description'
}

export default function Page() {
  return <ClientComponent />
}

// client-component.tsx
"use client"

export default function ClientComponent() {
  // client-side logic
  return <div>Content</div>
}
```

## Hasil Perbaikan

### Build Status
✅ **Berhasil**: `npm run build` completed successfully  
✅ **No Errors**: Tidak ada error compile terkait "use client"  
✅ **Development**: `npm run dev` berjalan normal  
✅ **Production**: Build production berhasil  

### File yang Terpengaruh
- ✅ `/app/program/page.tsx` - Fixed
- ✅ `/app/tentang-kami/page.tsx` - Fixed
- ✅ Semua file lain sudah benar

## Best Practices

### 1. Struktur File yang Benar
```typescript
"use client"

// 1. Imports
import React from "react"
import { useState } from "react"

// 2. Types/Interfaces
interface Props {
  // ...
}

// 3. Component
export default function Component() {
  // component logic
}
```

### 2. Kapan Menggunakan "use client"
- Ketika menggunakan React hooks (useState, useEffect, etc.)
- Ketika menggunakan event handlers
- Ketika menggunakan browser APIs
- Ketika menggunakan context providers

### 3. Kapan TIDAK Menggunakan "use client"
- Server components yang hanya render static content
- Components yang menggunakan server-side data fetching
- Components yang menggunakan metadata exports

## Troubleshooting

### Error: "use client" directive must be placed before other expressions
**Solusi**: Pindahkan `"use client"` ke baris pertama file

### Error: Cannot use metadata export in client component
**Solusi**: Gunakan Head component atau pisahkan menjadi server component

### Error: Hooks can only be used in client components
**Solusi**: Tambahkan `"use client"` di awal file

## Validasi
Untuk memastikan tidak ada error serupa di masa depan:

1. **Build Test**: Jalankan `npm run build` secara berkala
2. **Linting**: Gunakan ESLint rules untuk Next.js
3. **Code Review**: Periksa posisi "use client" dalam PR
4. **Development**: Monitor console untuk warnings

## Kesimpulan
Perbaikan ini memastikan bahwa semua client components mengikuti aturan Next.js 13+ App Router dengan benar, sehingga aplikasi dapat di-build dan di-deploy tanpa error.
