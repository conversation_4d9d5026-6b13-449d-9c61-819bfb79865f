# Photo Upload Troubleshooting Guide

## 🚨 Problem
Upload foto untuk student dan teacher tidak berfungsi - foto tidak tersimpan atau tidak muncul.

## 🔍 Common Issues & Solutions

### 1. **Database Table Missing**
**Symptoms:** Console error "table website_images does not exist"

**Solution:**
```sql
-- Run in Supabase SQL Editor:
\i scripts/create-website-images-table.sql
```

### 2. **Upload Directories Missing**
**Symptoms:** Error "ENOENT: no such file or directory"

**Solution:**
```bash
# Run in terminal:
chmod +x scripts/fix-photo-upload.sh
./scripts/fix-photo-upload.sh
```

Or manually:
```bash
mkdir -p public/uploads/students
mkdir -p public/uploads/teachers
mkdir -p public/uploads/general
```

### 3. **Authentication Issues**
**Symptoms:** 401 Unauthorized error

**Solution:**
- Make sure user is logged in
- Check if user has proper role (admin/teacher)
- Verify session is valid

### 4. **RLS Policies Blocking Upload**
**Symptoms:** 403 Forbidden or database permission error

**Solution:**
```sql
-- Check policies in Supabase:
SELECT * FROM pg_policies WHERE tablename = 'website_images';

-- If needed, temporarily disable RLS for testing:
ALTER TABLE website_images DISABLE ROW LEVEL SECURITY;
-- Remember to re-enable after testing!
```

### 5. **Form Not Saving Photo URL**
**Symptoms:** Upload succeeds but photo_url not saved to student/teacher record

**Solution:** Check API payload includes photo_url field

## 🛠️ Fixes Applied

### 1. **Enhanced Image Upload Handler**
```typescript
const handleImageUpload = (data: any) => {
  console.log('Image upload success:', data)
  if (data.file_path) {
    form.setValue("photo_url", data.file_path)
  } else if (data.url) {
    form.setValue("photo_url", data.url)
  } else if (data.image?.file_path) {
    form.setValue("photo_url", data.image.file_path)
  }
}
```

### 2. **Improved Upload Response Handling**
```typescript
// In ImageUpload component
const imageUrl = data.url || data.image?.file_path || data.file_path
if (imageUrl) {
  setPreview(imageUrl)
}

onUploadSuccess?.({
  file_path: data.image?.file_path || data.url || data.file_path,
  url: data.url || data.image?.file_path || data.file_path,
  image: data.image,
  ...data
})
```

### 3. **Consistent API Response Format**
```typescript
// API returns:
{
  message: 'File uploaded successfully',
  image: imageRecord,
  url: `/api/uploads/${category}/${filename}`
}
```

## 🧪 Testing Steps

### 1. **Run Automated Tests**
```bash
# Test upload functionality
node scripts/test-photo-upload.js

# Fix common issues
chmod +x scripts/fix-photo-upload.sh
./scripts/fix-photo-upload.sh
```

### 2. **Manual Testing**
1. **Open student/teacher form**
   ```
   /dashboard/students/new
   /dashboard/teachers/new
   ```

2. **Upload image**
   - Click upload area
   - Select image file (JPG, PNG, GIF, WebP)
   - Wait for upload to complete

3. **Check console logs**
   - Open browser DevTools
   - Look for upload success/error messages
   - Verify photo_url is set

4. **Submit form**
   - Fill required fields
   - Submit form
   - Check if photo_url is saved in database

### 3. **Database Verification**
```sql
-- Check if image was saved
SELECT * FROM website_images ORDER BY created_at DESC LIMIT 5;

-- Check if photo_url was saved to student/teacher
SELECT id, name, photo_url FROM students WHERE photo_url IS NOT NULL;
SELECT id, name, photo_url FROM teachers WHERE photo_url IS NOT NULL;
```

## 📁 File Structure

### Upload Directory Structure:
```
public/
└── uploads/
    ├── students/
    │   └── student_123_1642345678.jpg
    ├── teachers/
    │   └── teacher_456_1642345679.png
    └── general/
        └── general_789_1642345680.gif
```

### Database Schema:
```sql
CREATE TABLE website_images (
    id UUID PRIMARY KEY,
    key VARCHAR(255) UNIQUE NOT NULL,
    filename VARCHAR(255) NOT NULL,
    original_name VARCHAR(255) NOT NULL,
    file_path TEXT NOT NULL,
    file_size INTEGER NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    alt_text TEXT,
    caption TEXT,
    category VARCHAR(100) DEFAULT 'general',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

## 🔄 Upload Flow

```
1. User selects image file
   ↓
2. ImageUpload component validates file
   ↓
3. FormData sent to /api/upload
   ↓
4. API saves file to public/uploads/
   ↓
5. API saves metadata to website_images table
   ↓
6. API returns image URL
   ↓
7. handleImageUpload sets form photo_url
   ↓
8. Form submission includes photo_url
   ↓
9. Student/Teacher record saved with photo_url
```

## 🚨 Debug Checklist

### When upload fails:
- [ ] Check browser console for errors
- [ ] Verify upload directories exist
- [ ] Check website_images table exists
- [ ] Verify user authentication
- [ ] Check RLS policies
- [ ] Test API endpoint directly
- [ ] Check server logs

### When form doesn't save photo:
- [ ] Verify handleImageUpload is called
- [ ] Check form.setValue is working
- [ ] Verify photo_url in form data
- [ ] Check API payload includes photo_url
- [ ] Verify database column exists

## 📞 Quick Fixes

### Immediate fixes to try:

1. **Create missing directories:**
   ```bash
   mkdir -p public/uploads/{students,teachers,general}
   ```

2. **Create database table:**
   ```sql
   -- Run in Supabase SQL Editor
   \i scripts/create-website-images-table.sql
   ```

3. **Check authentication:**
   ```javascript
   // In browser console
   console.log('User session:', await supabase.auth.getSession())
   ```

4. **Test upload API:**
   ```bash
   node scripts/test-photo-upload.js
   ```

5. **Check file permissions:**
   ```bash
   ls -la public/uploads/
   ```

## 🔮 Prevention

### To prevent future issues:

1. **Add upload validation:**
   ```typescript
   // Validate file before upload
   if (!file || file.size > 5MB) {
     throw new Error('Invalid file')
   }
   ```

2. **Add error boundaries:**
   ```typescript
   // Wrap upload components in error boundaries
   <ErrorBoundary fallback={<UploadError />}>
     <ImageUpload />
   </ErrorBoundary>
   ```

3. **Monitor upload directory:**
   ```bash
   # Add to deployment script
   mkdir -p public/uploads/{students,teachers,general}
   ```

4. **Database migrations:**
   ```sql
   -- Include in migration scripts
   CREATE TABLE IF NOT EXISTS website_images (...)
   ```

The upload functionality should now work correctly with these fixes applied!
