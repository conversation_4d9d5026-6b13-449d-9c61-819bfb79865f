# Student Timeline Feature

## 📋 Overview

Fitur Timeline telah ditambahkan ke halaman detail student untuk menampilkan perkembangan bulanan siswa. Fitur ini menampilkan data timeline yang sama seperti yang ada di parent dashboard, tetapi dalam konteks admin/teacher dashboard.

## 🎯 Features

### 1. **Tab Timeline di Student Detail**
- Tab baru "Timeline" ditambahkan di halaman detail student
- Menampilkan perkembangan bulanan siswa secara kronologis
- Interface yang konsisten dengan parent dashboard

### 2. **Timeline Entry Display**
- **Mata Pelajaran**: Pencapaian per subject dengan nilai dan catatan
- **Perilaku**: <PERSON>lai perilaku bulanan dengan catatan
- **Aktivitas**: Daftar aktivitas dan prestasi siswa
- **Timeline Visual**: Tampilan timeline dengan dots dan garis penghubung

### 3. **Data Integration**
- Menggunakan data dari tabel `timeline_entries`
- Terintegrasi dengan `timeline_details`, `timeline_activities`, dan `behavior_records`
- API endpoint khusus untuk mengambil data timeline per student

## 📁 File Structure

### New Files:
```
app/api/students/[id]/timeline/route.ts    # API endpoint untuk timeline data
docs/STUDENT_TIMELINE_FEATURE.md          # Dokumentasi fitur (file ini)
```

### Modified Files:
```
components/students/student-detail.tsx           # Menambahkan tab timeline
components/students/student-detail-skeleton.tsx # Skeleton untuk timeline tab
```

## 🔧 Technical Implementation

### 1. **API Endpoint**
```typescript
GET /api/students/[id]/timeline

Response:
{
  "success": true,
  "student": {
    "id": "student_uuid",
    "name": "Nama Siswa",
    "student_id": "ID_SISWA"
  },
  "timeline": [
    {
      "id": "timeline_uuid",
      "student_id": "student_uuid",
      "month": "1",
      "year": 2024,
      "created_at": "2024-01-15T10:00:00Z",
      "timeline_details": [...],
      "timeline_activities": [...],
      "behavior_records": [...]
    }
  ],
  "total": 5
}
```

### 2. **Component Structure**
```typescript
// Main component
StudentDetail
├── Tab: Parents
├── Tab: Achievements  
├── Tab: Timeline (NEW)
│   ├── TimelineEntryCard
│   │   ├── Subject Cards
│   │   ├── Behavior Records
│   │   └── Activities List
│   └── Refresh Button
└── Tab: Attendance
```

### 3. **Data Flow**
```
Student Detail Page
    ↓
fetchTimeline() function
    ↓
API: /api/students/[id]/timeline
    ↓
Database: timeline_entries + behavior_records
    ↓
TimelineEntryCard components
    ↓
Visual Timeline Display
```

## 🎨 UI Components

### 1. **Timeline Entry Card**
- **Header**: Bulan/Tahun dengan tanggal input
- **Subject Cards**: Grid layout dengan border kiri berwarna
- **Behavior Section**: Background biru dengan badge nilai
- **Activities Section**: Background abu-abu dengan bullet points
- **Empty State**: Pesan jika tidak ada data

### 2. **Visual Elements**
- **Timeline Dots**: Bulatan hijau dengan shadow
- **Timeline Line**: Garis vertikal hijau di kiri
- **Grade Badges**: Warna berbeda berdasarkan nilai (A=hijau, B=biru, dll)
- **Category Colors**: Emerald untuk subjects, blue untuk behavior

### 3. **Responsive Design**
- Grid 2 kolom untuk subject cards di desktop
- Stack vertikal di mobile
- Responsive spacing dan typography

## 🔄 Data Sources

### 1. **Timeline Entries**
```sql
SELECT * FROM timeline_entries 
WHERE student_id = ? 
ORDER BY year DESC, month DESC
```

### 2. **Timeline Details (Subjects)**
```sql
SELECT td.*, s.name, s.category 
FROM timeline_details td
JOIN subjects s ON td.subject_id = s.id
WHERE td.timeline_id = ?
```

### 3. **Timeline Activities**
```sql
SELECT * FROM timeline_activities 
WHERE timeline_id = ?
```

### 4. **Behavior Records**
```sql
SELECT * FROM behavior_records 
WHERE student_id = ? AND month = ? AND year = ?
```

## 🧪 Testing

### Manual Testing:
1. **Navigate to student detail page**
   ```
   /dashboard/students/[student_id]
   ```

2. **Click Timeline tab**
   - Should load timeline data automatically
   - Should show loading state while fetching

3. **Test Refresh button**
   - Should reload timeline data
   - Should show loading indicator

4. **Test different data states**
   - Student with timeline data
   - Student without timeline data
   - Student with partial data (only subjects, only activities, etc.)

### API Testing:
```bash
# Test timeline API
curl -X GET "http://localhost:3001/api/students/STUDENT_ID/timeline" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 📊 Grade Color Mapping

```typescript
const gradeColors = {
  'A': 'bg-emerald-100 text-emerald-800 border-emerald-200',
  'B': 'bg-blue-100 text-blue-800 border-blue-200', 
  'C': 'bg-yellow-100 text-yellow-800 border-yellow-200',
  'D': 'bg-orange-100 text-orange-800 border-orange-200',
  'E': 'bg-red-100 text-red-800 border-red-200',
  'default': 'bg-gray-100 text-gray-800 border-gray-200'
}
```

## 🔮 Future Enhancements

### Potential Improvements:
1. **Filter by Date Range**: Tambah filter untuk periode tertentu
2. **Export Timeline**: Export ke PDF atau Excel
3. **Timeline Comparison**: Bandingkan dengan siswa lain
4. **Progress Charts**: Grafik perkembangan nilai
5. **Timeline Comments**: Komentar dari guru/admin
6. **Photo Attachments**: Lampiran foto untuk aktivitas

### Performance Optimizations:
1. **Pagination**: Untuk siswa dengan banyak data timeline
2. **Lazy Loading**: Load data saat tab diklik
3. **Caching**: Cache timeline data di client
4. **Virtual Scrolling**: Untuk timeline yang sangat panjang

## 🚀 Usage

### For Administrators:
1. Go to Students menu
2. Click on any student name
3. Navigate to "Timeline" tab
4. View monthly progress reports
5. Use refresh button to get latest data

### For Teachers:
1. Access student detail from class management
2. Review student's monthly development
3. Compare with input timeline data
4. Track student progress over time

## 🔗 Related Features

- **Timeline Input**: `/dashboard/teacher/timeline-input`
- **Parent Dashboard**: Timeline view for parents
- **Student Management**: Main student CRUD operations
- **Achievements**: Student achievements tracking

## 📝 Notes

- Timeline data is read-only in student detail view
- Data input is done through Timeline Input feature
- Consistent with parent dashboard timeline display
- Supports all existing timeline data structure
- Responsive design for mobile and desktop
