# Manajemen Konten Tentang Kami

## Overview
Fitur ini memungkinkan admin untuk mengedit konten halaman "Tentang Kami" melalui dashboard admin tanpa perlu mengubah kode secara langsung.

## Fitur yang Tersedia

### 1. Edit Konten Melalui Dashboard
- **Lokasi**: Dashboard → Settings → Tab "Tentang Kami"
- **Konten yang dapat diedit**:
  - **Visi**: Visi PTQ Al Ihsan
  - **Misi**: Misi PTQ Al Ihsan (mendukung multiple lines)
  - **Sejarah**: Sejarah singkat PTQ Al Ihsan
  - **Fasilitas**: Daftar fasilitas dengan gambar dan deskripsi

### 2. Data Pengajar Otomatis
- **Sumber Data**: Langsung dari database tabel `teachers`
- **Update Otomatis**: Data pengajar akan otomatis update ketika ada perubahan di dashboard guru
- **Informasi Ditampilkan**: <PERSON><PERSON>, s<PERSON><PERSON><PERSON><PERSON>, dan foto pengajar

### 3. Editor Fasilitas
- **Tambah/Edit/Hapus**: F<PERSON><PERSON><PERSON> dapat dikelola secara dinamis
- **Upload Gambar**: <PERSON>iap fasilitas dapat memiliki gambar
- **Urutan**: Fasilitas dapat diurutkan sesuai kebutuhan

### 4. Penyimpanan Real-time
- Perubahan disimpan langsung ke database
- Notifikasi sukses/error setelah penyimpanan
- Data langsung terlihat di halaman publik

### 5. Format Konten
- **Visi**: Text area single paragraph
- **Misi**: Text area dengan dukungan multiple lines (pisahkan dengan Enter)
- **Sejarah**: Text area panjang untuk narasi sejarah
- **Fasilitas**: Nama, deskripsi, dan gambar untuk setiap fasilitas

## Cara Menggunakan

### Mengakses Editor
1. Login sebagai admin
2. Buka Dashboard → Settings
3. Klik tab "Tentang Kami"

### Mengedit Konten
1. Edit konten di textarea yang tersedia
2. Untuk misi: pisahkan setiap poin dengan baris baru (Enter)
3. Klik tombol "Simpan Konten Tentang Kami"
4. Tunggu notifikasi sukses

### Mengelola Fasilitas
1. **Mengedit Fasilitas Existing**:
   - Edit nama fasilitas di field "Nama Fasilitas"
   - Edit deskripsi di field "Deskripsi"
   - Upload gambar baru jika diperlukan

2. **Menambah Fasilitas Baru**:
   - Klik tombol "Tambah Fasilitas Baru"
   - Isi nama dan deskripsi fasilitas
   - Upload gambar fasilitas

3. **Menghapus Fasilitas**:
   - Klik tombol trash (🗑️) di pojok kanan atas card fasilitas

4. **Menyimpan Perubahan**:
   - Klik tombol "Simpan Data Fasilitas"
   - Tunggu notifikasi sukses

### Melihat Hasil
1. Buka halaman `/tentang-kami` di website
2. Konten yang diubah akan langsung terlihat
3. Data pengajar akan otomatis update dari database guru
4. Fasilitas akan tampil sesuai urutan yang ditentukan

## Struktur Database

### Tabel: website_content
```sql
- section: 'about' | 'facilities'
- key: 'vision' | 'mission' | 'history' | 'facility_key'
- title: Judul konten
- content: Isi konten
- order_index: Urutan tampilan
- is_active: Status aktif/nonaktif
```

### Tabel: teachers
```sql
- id: UUID primary key
- name: Nama pengajar
- specialization: Spesialisasi/bidang keahlian
- photo_url: URL foto pengajar
- status: Status aktif/nonaktif
```

## API Endpoints

### GET /api/website-content
- Mengambil semua konten website
- Filter berdasarkan section: `?section=about`

### PUT /api/website-content
- Update multiple content items
- Body: `{ content: [array of content objects] }`

### GET /api/teachers
- Mengambil data semua pengajar aktif
- Response: `{ teachers: [array of teacher objects] }`

## File yang Terlibat

### Frontend
- `app/dashboard/settings/page.tsx` - Halaman settings dengan tab Tentang Kami dan editor fasilitas
- `app/tentang-kami/page.tsx` - Halaman publik yang menampilkan konten, pengajar, dan fasilitas
- `hooks/use-website-settings.ts` - Hook untuk mengambil data konten, pengajar, dan fasilitas

### Backend
- `app/api/website-content/route.ts` - API untuk CRUD content dan fasilitas
- `app/api/teachers/route.ts` - API untuk mengambil data pengajar
- `db/website_settings.sql` - Schema database dengan data fasilitas
- `db/schema.sql` - Schema tabel teachers

## Keamanan
- Hanya admin yang dapat mengakses editor
- RLS (Row Level Security) diterapkan di database
- Validasi input di API level

## Tips Penggunaan
1. **Misi**: Gunakan format poin-poin dengan memisahkan setiap misi dengan baris baru
2. **Sejarah**: Tulis dalam bentuk paragraf yang mengalir
3. **Visi**: Buat singkat dan jelas, maksimal 2-3 kalimat
4. **Fasilitas**:
   - Gunakan nama yang jelas dan mudah dipahami
   - Deskripsi singkat tapi informatif
   - Upload gambar berkualitas baik (recommended: 300x200px)
5. **Pengajar**: Data akan otomatis update dari dashboard guru, pastikan data guru selalu terbaru
6. **Preview**: Selalu cek hasil di halaman publik setelah menyimpan

## Troubleshooting

### Konten tidak tersimpan
- Pastikan koneksi internet stabil
- Cek console browser untuk error
- Pastikan login sebagai admin

### Konten tidak muncul di halaman publik
- Refresh halaman publik
- Cek apakah `is_active` = true di database
- Pastikan tidak ada error di console

### Format misi tidak sesuai
- Pastikan setiap poin misi dipisahkan dengan Enter
- Jangan gunakan bullet points manual (•, -, *)
- Sistem akan otomatis menambahkan icon checklist

### Data pengajar tidak muncul
- Pastikan ada data guru di dashboard dengan status aktif
- Cek koneksi API `/api/teachers`
- Pastikan foto pengajar sudah diupload

### Fasilitas tidak tersimpan
- Pastikan semua field (nama dan deskripsi) sudah diisi
- Cek console browser untuk error
- Pastikan gambar sudah diupload dengan benar
