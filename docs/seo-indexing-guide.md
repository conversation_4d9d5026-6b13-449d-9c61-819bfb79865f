# Panduan SEO dan Indexing Website PTQ Al Ihsan

## Overview

Dokumen ini menjelaskan langkah-langkah untuk melakukan indexing ulang website PTQ Al Ihsan agar mudah ditemukan di search engine seperti Google dan Bing.

## Komponen SEO yang Sudah Diterapkan

### 1. Sitemap Dinamis (`app/sitemap.ts`)
- Sitemap otomatis yang mencakup semua halaman penting
- Update otomatis dengan timestamp terbaru
- Prioritas dan frekuensi update yang dioptimalkan

### 2. Robots.txt (`app/robots.ts`)
- Mengizinkan crawler mengakses halaman publik
- Memblokir area sensitif (dashboard, API, admin)
- Referensi ke sitemap untuk crawler

### 3. Metadata Lengkap
- **Layout utama**: Metadata global dengan OpenGraph dan Twitter Cards
- **Halaman spesifik**: Metadata khusus untuk setiap halaman
- **Structured Data**: JSON-LD untuk organisasi dan website

### 4. Structured Data (Schema.org)
- **OrganizationStructuredData**: Informasi lengkap tentang PTQ Al Ihsan
- **WebsiteStructuredData**: Data website untuk search engine
- **BreadcrumbStructuredData**: Navigasi breadcrumb

## Cara Menjalankan Indexing

### Method 1: Menggunakan Script Otomatis

```bash
# Jalankan indexing langsung
npm run seo:index

# Atau build dulu lalu indexing
npm run seo:build
```

### Method 2: Manual Steps

1. **Build website**
   ```bash
   npm run build
   ```

2. **Deploy ke production**
   ```bash
   npm run start
   ```

3. **Verifikasi file SEO**
   - Buka: `https://www.ptqalihsan.ac.id/sitemap.xml`
   - Buka: `https://www.ptqalihsan.ac.id/robots.txt`

## Langkah Manual di Search Console

### Google Search Console

1. **Login ke Google Search Console**
   - URL: https://search.google.com/search-console
   - Pilih property: `https://www.ptqalihsan.ac.id`

2. **Submit Sitemap**
   - Masuk ke menu "Sitemaps"
   - Tambahkan URL: `sitemap.xml`
   - Klik "Submit"

3. **Request Indexing untuk Halaman Penting**
   - Masuk ke menu "URL Inspection"
   - Masukkan URL halaman penting:
     - `https://www.ptqalihsan.ac.id/`
     - `https://www.ptqalihsan.ac.id/ppdb`
     - `https://www.ptqalihsan.ac.id/tentang-kami`
     - `https://www.ptqalihsan.ac.id/program`
   - Klik "Request Indexing"

### Bing Webmaster Tools

1. **Login ke Bing Webmaster Tools**
   - URL: https://www.bing.com/webmasters
   - Pilih property: `https://www.ptqalihsan.ac.id`

2. **Submit Sitemap**
   - Masuk ke "Sitemaps"
   - Submit URL: `https://www.ptqalihsan.ac.id/sitemap.xml`

3. **Submit URL untuk Indexing**
   - Gunakan fitur "Submit URLs"
   - Submit halaman penting satu per satu

## Validasi dan Testing

### 1. Rich Results Test
- URL: https://search.google.com/test/rich-results
- Test semua halaman untuk structured data

### 2. Mobile-Friendly Test
- URL: https://search.google.com/test/mobile-friendly
- Pastikan semua halaman mobile-friendly

### 3. PageSpeed Insights
- URL: https://pagespeed.web.dev/
- Optimasi performa untuk SEO

### 4. Lighthouse SEO Audit
```bash
# Install lighthouse globally
npm install -g lighthouse

# Run audit
lighthouse https://www.ptqalihsan.ac.id --only-categories=seo --output=html
```

## Monitoring dan Maintenance

### Weekly Tasks
- [ ] Cek Google Search Console untuk errors
- [ ] Monitor indexing status halaman baru
- [ ] Review search performance data

### Monthly Tasks
- [ ] Update sitemap jika ada halaman baru
- [ ] Review dan update metadata
- [ ] Analisa keyword performance
- [ ] Update structured data jika ada perubahan info

### Best Practices

1. **Content Quality**
   - Pastikan konten unik dan berkualitas
   - Update konten secara regular
   - Gunakan keyword yang relevan

2. **Technical SEO**
   - Pastikan website loading cepat
   - Implementasi HTTPS
   - Responsive design

3. **Local SEO**
   - Optimasi untuk pencarian lokal "pondok tahfidz ungaran"
   - Gunakan Google My Business
   - Konsistensi NAP (Name, Address, Phone)

## Troubleshooting

### Sitemap Tidak Terbaca
- Cek format XML di `https://www.ptqalihsan.ac.id/sitemap.xml`
- Pastikan semua URL accessible
- Verifikasi robots.txt tidak memblokir sitemap

### Halaman Tidak Terindex
- Cek robots.txt untuk disallow rules
- Pastikan halaman tidak memiliki noindex meta tag
- Submit manual melalui Search Console

### Structured Data Error
- Gunakan Rich Results Test untuk debugging
- Cek format JSON-LD
- Pastikan required properties tersedia

## Contact dan Support

Untuk pertanyaan teknis terkait SEO dan indexing:
- Email: <EMAIL>
- Dokumentasi: `/docs/seo-indexing-guide.md` 