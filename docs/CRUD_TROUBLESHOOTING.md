# CRUD Operations Troubleshooting Guide

## Masalah yang Telah Diperbaiki

### 1. Teachers CRUD Issues

**Masalah:**
- API POST mencoba insert ke tabel `users` padahal sehar<PERSON>nya ke tabel `teachers`
- Tidak ada `teacher_id` yang di-generate untuk teacher baru
- Inkonsistensi field mapping

**Perbaikan:**
- ✅ Mengubah API POST untuk insert ke tabel `teachers`
- ✅ Menambahkan auto-generation `teacher_id` dengan format `TCH-{timestamp}-{random}`
- ✅ Menggunakan `profile_id` dari session user
- ✅ Menambahkan field `join_date`, `status`, dan `specialization`

### 2. Parents CRUD Issues

**Masalah:**
- Form mengirim ID via query parameter tapi API mengharapkan di body
- Inkonsistensi penggunaan `profile_id` vs `user_id`

**Perbaikan:**
- ✅ Mengubah form untuk mengirim ID dalam body payload
- ✅ Memastikan konsistensi penggunaan `profile_id`
- ✅ Helper function `createParent` sudah handle foreign key constraints

### 3. Subjects CRUD Issues

**Masalah:**
- Form UPDATE tidak mengirim `id` dalam payload
- URL untuk UPDATE sama dengan CREATE

**Perbaikan:**
- ✅ Menambahkan `id` dalam payload untuk operasi UPDATE
- ✅ API sudah benar menerima ID dari body

## Struktur Database yang Benar

### Teachers Table
```sql
CREATE TABLE teachers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    teacher_id VARCHAR UNIQUE NOT NULL,
    name VARCHAR NOT NULL,
    specialization VARCHAR,
    photo_url TEXT,
    join_date DATE,
    status VARCHAR DEFAULT 'active',
    profile_id UUID REFERENCES profiles(id),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

### Parents Table
```sql
CREATE TABLE parents (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR NOT NULL,
    phone VARCHAR NOT NULL,
    email VARCHAR,
    address TEXT,
    occupation VARCHAR,
    profile_id UUID REFERENCES profiles(id),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

### Subjects Table
```sql
CREATE TABLE subjects (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    category VARCHAR(100) NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

## API Endpoints

### Teachers
- **POST** `/api/teachers` - Create new teacher
- **PUT** `/api/teachers` - Update teacher (ID in body)
- **DELETE** `/api/teachers?id={id}` - Soft delete teacher
- **GET** `/api/teachers` - List all teachers

### Parents
- **POST** `/api/parents` - Create new parent
- **PUT** `/api/parents` - Update parent (ID in body)
- **DELETE** `/api/parents?id={id}` - Delete parent
- **GET** `/api/parents` - List all parents

### Subjects
- **POST** `/api/subjects` - Create new subject
- **PUT** `/api/subjects` - Update subject (ID in body)
- **DELETE** `/api/subjects?id={id}` - Delete subject
- **GET** `/api/subjects` - List all subjects

## Testing

### Manual Testing
1. Jalankan development server: `npm run dev`
2. Login sebagai admin user
3. Test melalui UI forms di dashboard

### Automated Testing
```bash
# Jalankan script test CRUD
node scripts/test-crud-operations.js
```

### Database Verification
```sql
-- Jalankan di Supabase SQL Editor
\i scripts/verify-database-schema.sql
```

## Common Issues & Solutions

### 1. "Unauthorized" Error
**Penyebab:** User tidak login atau tidak memiliki permission
**Solusi:** 
- Pastikan user sudah login
- Pastikan user memiliki role 'admin'
- Check RLS policies di Supabase

### 2. "Foreign Key Constraint" Error
**Penyebab:** `profile_id` tidak valid atau tidak ada di tabel `profiles`
**Solusi:**
- Pastikan user memiliki record di tabel `profiles`
- Jalankan script `scripts/safe-migration.sql` untuk membuat profiles yang hilang

### 3. "Column does not exist" Error
**Penyebab:** Struktur database tidak sesuai dengan kode
**Solusi:**
- Jalankan `scripts/verify-database-schema.sql`
- Uncomment bagian perbaikan jika diperlukan

### 4. Form Tidak Menyimpan Data
**Penyebab:** 
- Payload tidak sesuai dengan yang diharapkan API
- Validation error di form
- Network error

**Solusi:**
- Check browser console untuk error messages
- Verify payload di Network tab
- Check server logs

## Monitoring & Debugging

### Browser Console
```javascript
// Check form data sebelum submit
console.log('Form data:', formData);

// Check API response
fetch('/api/teachers', {...})
  .then(res => res.json())
  .then(data => console.log('API Response:', data))
  .catch(err => console.error('API Error:', err));
```

### Server Logs
```bash
# Check Next.js logs
npm run dev

# Check Supabase logs di dashboard
```

### Database Queries
```sql
-- Check recent records
SELECT * FROM teachers ORDER BY created_at DESC LIMIT 5;
SELECT * FROM parents ORDER BY created_at DESC LIMIT 5;
SELECT * FROM subjects ORDER BY created_at DESC LIMIT 5;

-- Check for errors
SELECT * FROM pg_stat_activity WHERE state = 'active';
```

## Best Practices

1. **Always validate input** di frontend dan backend
2. **Use transactions** untuk operasi yang melibatkan multiple tables
3. **Handle errors gracefully** dengan user-friendly messages
4. **Log errors** untuk debugging
5. **Test CRUD operations** setelah setiap perubahan
6. **Backup database** sebelum menjalankan migration scripts
