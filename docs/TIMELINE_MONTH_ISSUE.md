# Timeline Month Display Issue

## 🚨 Problem
Timeline menampilkan "undefined 2025" instead of "Juli 2025" untuk bulan.

## 🔍 Root Cause Analysis

### Possible Causes:
1. **Database month column has NULL values**
2. **Month stored as string name but expected as number**
3. **Month stored as number but expected as string**
4. **Data type mismatch in database schema**
5. **API not returning month data correctly**

## 🛠️ Debugging Steps

### 1. Run Debug Script
```bash
node scripts/debug-timeline-month.js
```

### 2. Check Database Schema
```sql
-- Check column types
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'timeline_entries' 
AND column_name IN ('month', 'year');
```

### 3. Check Sample Data
```sql
-- Check actual data
SELECT id, student_id, month, year, created_at 
FROM timeline_entries 
ORDER BY created_at DESC 
LIMIT 10;
```

### 4. Check for NULL Values
```sql
-- Find NULL months
SELECT COUNT(*) as null_months 
FROM timeline_entries 
WHERE month IS NULL;

-- Find empty string months
SELECT COUNT(*) as empty_months 
FROM timeline_entries 
WHERE month = '';
```

## 🔧 Solutions

### Solution 1: Fix NULL Month Values
```sql
-- Update NULL months to current month
UPDATE timeline_entries 
SET month = EXTRACT(MONTH FROM created_at)::text 
WHERE month IS NULL;
```

### Solution 2: Fix Month Data Type
```sql
-- If months are stored as numbers but should be strings
UPDATE timeline_entries 
SET month = CASE 
  WHEN month::integer = 1 THEN 'Januari'
  WHEN month::integer = 2 THEN 'Februari'
  WHEN month::integer = 3 THEN 'Maret'
  WHEN month::integer = 4 THEN 'April'
  WHEN month::integer = 5 THEN 'Mei'
  WHEN month::integer = 6 THEN 'Juni'
  WHEN month::integer = 7 THEN 'Juli'
  WHEN month::integer = 8 THEN 'Agustus'
  WHEN month::integer = 9 THEN 'September'
  WHEN month::integer = 10 THEN 'Oktober'
  WHEN month::integer = 11 THEN 'November'
  WHEN month::integer = 12 THEN 'Desember'
  ELSE month
END
WHERE month ~ '^[0-9]+$'; -- Only update numeric months
```

### Solution 3: Standardize to Numbers
```sql
-- Convert month names to numbers
UPDATE timeline_entries 
SET month = CASE 
  WHEN LOWER(month) = 'januari' THEN '1'
  WHEN LOWER(month) = 'februari' THEN '2'
  WHEN LOWER(month) = 'maret' THEN '3'
  WHEN LOWER(month) = 'april' THEN '4'
  WHEN LOWER(month) = 'mei' THEN '5'
  WHEN LOWER(month) = 'juni' THEN '6'
  WHEN LOWER(month) = 'juli' THEN '7'
  WHEN LOWER(month) = 'agustus' THEN '8'
  WHEN LOWER(month) = 'september' THEN '9'
  WHEN LOWER(month) = 'oktober' THEN '10'
  WHEN LOWER(month) = 'november' THEN '11'
  WHEN LOWER(month) = 'desember' THEN '12'
  ELSE month
END
WHERE month !~ '^[0-9]+$'; -- Only update non-numeric months
```

## 📝 Code Fix Applied

### Enhanced Month Parsing in Component
```typescript
// Handle both string and number month values
let monthIndex = 0 // Default to January

if (entry.month) {
  if (typeof entry.month === 'string') {
    // If month is a string like "Juli", find its index
    const foundIndex = monthNames.findIndex(name => 
      name.toLowerCase() === entry.month.toString().toLowerCase()
    )
    
    if (foundIndex !== -1) {
      monthIndex = foundIndex
    } else {
      // If not found, try parsing as number
      const parsed = parseInt(entry.month)
      if (!isNaN(parsed) && parsed >= 1 && parsed <= 12) {
        monthIndex = parsed - 1
      }
    }
  } else {
    // If month is already a number
    const monthNum = Number(entry.month)
    if (!isNaN(monthNum) && monthNum >= 1 && monthNum <= 12) {
      monthIndex = monthNum - 1
    }
  }
}

const monthName = monthNames[monthIndex] || 'Bulan Tidak Diketahui'
const year = entry.year || new Date().getFullYear()
const monthYear = `${monthName} ${year}`
```

## 🧪 Testing

### Manual Test:
1. Open browser console
2. Navigate to student detail page
3. Click Timeline tab
4. Check console logs for debug info
5. Verify month displays correctly

### API Test:
```bash
# Test timeline API directly
curl -X GET "http://localhost:3001/api/students/STUDENT_ID/timeline"
```

## 📊 Expected Data Format

### Database:
```sql
-- timeline_entries table
id          | UUID
student_id  | UUID  
month       | VARCHAR (should be '1'-'12' or 'Januari'-'Desember')
year        | INTEGER (should be 2024, 2025, etc.)
created_at  | TIMESTAMPTZ
```

### API Response:
```json
{
  "timeline": [
    {
      "id": "uuid",
      "student_id": "uuid",
      "month": "7",        // or "Juli"
      "year": 2025,
      "created_at": "2025-07-16T10:00:00Z"
    }
  ]
}
```

### Component Display:
```
"Juli 2025"  ✅ Correct
"undefined 2025"  ❌ Wrong
```

## 🔄 Prevention

### 1. Data Validation on Insert
```typescript
// In timeline input form
const monthValidation = (month: string | number) => {
  if (typeof month === 'string') {
    const monthNames = ['Januari', 'Februari', ...];
    return monthNames.includes(month) || /^[1-9]|1[0-2]$/.test(month);
  }
  return month >= 1 && month <= 12;
};
```

### 2. Database Constraints
```sql
-- Add check constraint
ALTER TABLE timeline_entries 
ADD CONSTRAINT check_month_valid 
CHECK (
  month ~ '^[1-9]|1[0-2]$' OR 
  month IN ('Januari', 'Februari', 'Maret', 'April', 'Mei', 'Juni',
            'Juli', 'Agustus', 'September', 'Oktober', 'November', 'Desember')
);
```

### 3. API Validation
```typescript
// In API endpoint
if (!month || (typeof month === 'string' && month.trim() === '')) {
  return NextResponse.json({ error: "Month is required" }, { status: 400 });
}
```

## 📞 Quick Fix

If you need immediate fix:

1. **Check console logs** in browser for debug info
2. **Run debug script**: `node scripts/debug-timeline-month.js`
3. **Check database data** with SQL queries above
4. **Apply appropriate SQL fix** based on data format found
5. **Refresh timeline tab** to see corrected display

The component now handles both string and numeric month formats, so the display should work once the data is correct.
