import { createServerClient } from '@supabase/ssr'
import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

export async function middleware(request: NextRequest) {
  let response = NextResponse.next({
    request: {
      headers: request.headers,
    },
  })

  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return request.cookies.get(name)?.value
        },
        set(name: string, value: string, options: any) {
          request.cookies.set({
            name,
            value,
            ...options,
          })
          response = NextResponse.next({
            request: {
              headers: request.headers,
            },
          })
          response.cookies.set({
            name,
            value,
            ...options,
          })
        },
        remove(name: string, options: any) {
          request.cookies.delete({
            name,
            ...options,
          })
          response = NextResponse.next({
            request: {
              headers: request.headers,
            },
          })
          response.cookies.delete({
            name,
            ...options,
          })
        },
      },
    }
  )

  const { data } = await supabase.auth.getSession()

  const url = new URL(request.url)
  const isAuthPage = url.pathname === '/login'
  const isPublicPage = url.pathname === '/' ||
                      url.pathname.startsWith('/parent-dashboard') ||
                      url.pathname.startsWith('/api/parent-dashboard') ||
                      url.pathname.startsWith('/api/upload') ||
                      url.pathname.startsWith('/api/uploads') ||
                      url.pathname.startsWith('/api/website-settings') ||
                      url.pathname.startsWith('/api/website-content') ||
                      url.pathname.startsWith('/blog') ||
                      url.pathname.startsWith('/program') ||
                      url.pathname.startsWith('/tentang-kami') ||
                      url.pathname.startsWith('/kontak') ||
                      url.pathname.startsWith('/ppdb')

  if (!data.session && !isAuthPage && !isPublicPage) {
    const redirectUrl = new URL('/login', request.url)
    return NextResponse.redirect(redirectUrl)
  }
  
  if (data.session && isAuthPage) {
    const redirectUrl = new URL('/dashboard', request.url)
    return NextResponse.redirect(redirectUrl)
  }

  return response
}

export const config = {
  matcher: ['/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)'],
}