// Database Student interface - matches the actual database structure
export interface DatabaseStudent {
  id: string
  student_id: string
  name: string
  gender: 'male' | 'female'
  birth_date: string
  address: string
  photo_url: string | null
  batch: string
  status: 'active' | 'inactive' | 'graduated' | 'suspended'
  profile_id: string | null
  created_at: string
  updated_at: string
}

// Achievement interface for database
export interface Achievement {
  id: string
  student_id: string
  subject_id: string
  value: string
  grade: string
  notes: string | null
  verified_by: string | null
  achievement_date: string
  created_at: string
  updated_at: string
  subject?: {
    id: string
    name: string
    category: string
  }
}

// Timeline detail interface
export interface TimelineDetail {
  id: string
  timeline_id: string
  subject_id: string
  value: string
  grade: string
  notes: string | null
  created_at: string
  updated_at: string
  subject?: {
    id: string
    name: string
    category: string
  }
}

// Enhanced Student interface for frontend use
export interface Student {
  id: string
  student_id: string
  name: string
  gender: 'male' | 'female'
  birth_date: string
  address: string
  photo: string | null // mapped from photo_url
  batch: string
  status: 'active' | 'inactive' | 'graduated' | 'suspended'
  profile_id: string | null
  created_at: string
  updated_at: string
  // Related data
  achievements?: Achievement[]
  timelineDetails?: TimelineDetail[]
  class?: {
    id: string
    name: string
    level: string
  }
  // Computed achievements for backward compatibility
  computedAchievements?: {
    alQuran: {
      value: string
      grade: string
    }
    haditsArbain: {
      value: string
      grade: string
    }
    ushulTsalatsah: {
      value: string
      grade: string
    }
    ghoyahWaTaqrib: {
      value: string
      grade: string
    }
    alAjurumiyyah: {
      value: string
      grade: string
    }
    kitabGundul: {
      value: string
      grade: string
    }
    publicSpeaking: {
      value: string
      grade: string
    }
  }
}

