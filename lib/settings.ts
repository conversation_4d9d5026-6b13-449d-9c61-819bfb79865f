"use server"

import { cache } from "react"
import { createServerClient } from "@/utils/supabase/server"
import { cookies } from "next/headers"

type SettingType = 'string' | 'boolean' | 'number' | 'json'

interface Setting {
  id: string
  key: string
  value: string | null
  type: SettingType
  group_name: string
  description: string | null
}

export const getSettings = cache(async (groupName?: string) => {
  const supabase = await createServerClient()
  
  let query = supabase.from('settings').select('*')
  
  if (groupName) {
    query = query.eq('group_name', groupName)
  }
  
  const { data, error } = await query
  
  if (error) {
    console.error('Error fetching settings:', error)
    return []
  }
  
  return data as Setting[]
})

export const getSetting = cache(async (key: string) => {
  const supabase = await createServerClient()
  
  const { data, error } = await supabase
    .from('settings')
    .select('*')
    .eq('key', key)
    .single()
  
  if (error) {
    console.error(`Error fetching setting with key "${key}":`, error)
    return null
  }
  
  return parseSettingValue(data as Setting)
})

export async function updateSetting(key: string, value: string | boolean | number | object) {
  const supabase = await createServerClient()
  
  // Get the current setting to determine its type
  const { data: currentSetting, error: fetchError } = await supabase
    .from('settings')
    .select('*')
    .eq('key', key)
    .single()
  
  if (fetchError) {
    console.error(`Error fetching setting with key "${key}":`, fetchError)
    return { success: false, error: fetchError.message }
  }
  
  // Convert value to string based on its type
  let stringValue: string
  
  if (typeof value === 'boolean') {
    stringValue = value.toString()
  } else if (typeof value === 'object') {
    stringValue = JSON.stringify(value)
  } else {
    stringValue = String(value)
  }
  
  // Update the setting
  const { error: updateError } = await supabase
    .from('settings')
    .update({ value: stringValue })
    .eq('key', key)
  
  if (updateError) {
    console.error(`Error updating setting with key "${key}":`, updateError)
    return { success: false, error: updateError.message }
  }
  
  return { success: true }
}

function parseSettingValue(setting: Setting) {
  if (!setting || setting.value === null) return null
  
  switch (setting.type) {
    case 'boolean':
      return setting.value.toLowerCase() === 'true'
    case 'number':
      return parseFloat(setting.value)
    case 'json':
      try {
        return JSON.parse(setting.value)
      } catch (e) {
        console.error(`Error parsing JSON setting "${setting.key}":`, e)
        return null
      }
    case 'string':
    default:
      return setting.value
  }
} 