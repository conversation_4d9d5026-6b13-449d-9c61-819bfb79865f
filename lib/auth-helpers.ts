"use server"

import { createServerClient } from "@/utils/supabase/server"
import { cookies } from "next/headers"
import { redirect } from "next/navigation"

export async function getSession() {
  try {
    const supabase = await createServerClient()
    const { data: { session } } = await supabase.auth.getSession()
    return { session }
  } catch (error) {
    console.error("Error getting session:", error)
    return { session: null }
  }
}

export async function getUserProfile() {
  const { session } = await getSession()
  
  if (!session) {
    return null
  }
  
  const supabase = await createServerClient()
  
  const { data: profile, error } = await supabase
    .from('profiles')
    .select('*')
    .eq('user_id', session.user.id)
    .single()
  
  if (error) {
    console.error('Error fetching user profile:', error)
    return null
  }
  
  return profile
}

export async function requireAuth() {
  const { session } = await getSession()
  
  if (!session) {
    redirect('/login')
  }
  
  return session
}

export async function requireAdmin() {
  const { session } = await getSession()
  
  if (!session) {
    redirect('/login')
  }
  
  const supabase = await createServerClient()
  
  const { data: profile, error } = await supabase
    .from('profiles')
    .select('role')
    .eq('user_id', session.user.id)
    .single()
  
  if (error || profile?.role !== 'admin') {
    redirect('/dashboard') // Redirect non-admins to regular dashboard
  }
  
  return session
}

// This function requires SUPABASE_SERVICE_ROLE_KEY to be set in .env.local
export async function createAdminUser(email: string, password: string, fullName: string) {
  // This function should only be called from a secure server context
  // or a one-time setup script, never from client-side code
  
  const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY
  
  if (!serviceRoleKey) {
    throw new Error('SUPABASE_SERVICE_ROLE_KEY is required to create admin users')
  }
  
  // Create a service role client
  const supabase = await createServerClient()
  
  // Create the user
  const { data: userData, error: userError } = await supabase.auth.admin.createUser({
    email,
    password,
    email_confirm: true,
    user_metadata: { full_name: fullName },
    app_metadata: { role: 'admin' }
  })
  
  if (userError) {
    console.error('Error creating admin user:', userError)
    throw userError
  }
  
  // Create the profile entry
  const { error: profileError } = await supabase
    .from('profiles')
    .insert({
      user_id: userData.user.id,
      full_name: fullName,
      role: 'admin'
    })
  
  if (profileError) {
    console.error('Error creating admin profile:', profileError)
    throw profileError
  }
  
  return userData.user
}