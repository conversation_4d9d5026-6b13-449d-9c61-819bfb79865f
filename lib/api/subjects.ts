"use server"

import { createServerClient } from "@/utils/supabase/server"

export type Subject = {
  id: string;
  name: string;
  category: string;
  description: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export type SubjectObjective = {
  id: string;
  subject_id: string;
  objective: string;
  created_at: string;
}

export type Teacher = {
  id: string;
  name: string;
  teacher_id: string;
  specialization: string | null;
}

export async function getSubject(id: string) {
  try {
    const supabase = await createServerClient()
    
    // Get subject details
    const { data: subject, error } = await supabase
      .from('subjects')
      .select('*')
      .eq('id', id)
      .single()
    
    if (error || !subject) {
      console.error('Error fetching subject:', error)
      return null
    }
    
    // Get learning objectives for this subject
    const { data: objectives, error: objectivesError } = await supabase
      .from('subject_objectives')
      .select('*')
      .eq('subject_id', id)
      .order('created_at', { ascending: true })
    
    if (objectivesError) {
      console.error("Error fetching objectives:", objectivesError)
      // Continue anyway
    }
    
    // Get teacher relationships for this subject
    const { data: teachers, error: teachersError } = await supabase
      .from('subject_teachers')
      .select(`
        teacher_id,
        teacher:teachers (
          id,
          name,
          teacher_id,
          specialization
        )
      `)
      .eq('subject_id', id)
    
    if (teachersError) {
      console.error("Error fetching subject teachers:", teachersError)
      // Continue anyway
    }
    
    return {
      subject,
      objectives: objectives || [],
      teachers: teachers?.map(t => t.teacher) || []
    }
  } catch (error) {
    console.error('Failed to get subject:', error)
    return null
  }
}

export async function getAllSubjects() {
  try {
    const supabase = await createServerClient()
    
    const { data: subjects, error } = await supabase
      .from('subjects')
      .select('*')
      .order('name', { ascending: true })
    
    if (error) {
      console.error('Error fetching subjects:', error)
      return []
    }
    
    return subjects
  } catch (error) {
    console.error('Failed to get subjects:', error)
    return []
  }
} 