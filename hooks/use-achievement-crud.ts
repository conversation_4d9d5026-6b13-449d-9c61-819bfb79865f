"use client"

import { useState } from "react"

export type AchievementData = {
  id?: string
  student_id: string
  subject: string
  description: string
  achievement_date: string
  teacher_id: string
  grade?: string
}

export function useAchievementCrud() {
  const [loading, setLoading] = useState<boolean>(false)
  const [error, setError] = useState<string | null>(null)

  // Create a new achievement
  const createAchievement = async (achievementData: AchievementData) => {
    setLoading(true)
    setError(null)
    
    try {
      const response = await fetch('/api/achievements', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(achievementData),
      })
      
      const data = await response.json()
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to create achievement')
      }
      
      return data
    } catch (err: any) {
      setError(err.message)
      return null
    } finally {
      setLoading(false)
    }
  }

  // Update an existing achievement
  const updateAchievement = async (id: string, achievementData: Partial<AchievementData>) => {
    setLoading(true)
    setError(null)
    
    try {
      const response = await fetch('/api/achievements', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ id, ...achievementData }),
      })
      
      const data = await response.json()
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to update achievement')
      }
      
      return data
    } catch (err: any) {
      setError(err.message)
      return null
    } finally {
      setLoading(false)
    }
  }

  // Delete an achievement
  const deleteAchievement = async (id: string) => {
    setLoading(true)
    setError(null)
    
    try {
      const response = await fetch(`/api/achievements?id=${id}`, {
        method: 'DELETE',
      })
      
      const data = await response.json()
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to delete achievement')
      }
      
      return data
    } catch (err: any) {
      setError(err.message)
      return null
    } finally {
      setLoading(false)
    }
  }

  // Get achievements for a student
  const getStudentAchievements = async (studentId: string) => {
    setLoading(true)
    setError(null)
    
    try {
      const response = await fetch(`/api/achievements?studentId=${studentId}`)
      const data = await response.json()
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to get achievements')
      }
      
      return data
    } catch (err: any) {
      setError(err.message)
      return null
    } finally {
      setLoading(false)
    }
  }

  return {
    createAchievement,
    updateAchievement,
    deleteAchievement,
    getStudentAchievements,
    loading,
    error,
  }
} 