"use client"

import { useState } from "react"

export type StudentData = {
  id?: string
  name: string
  gender: string
  birth_date: string
  address: string
  photo_url?: string
  batch?: string
  status?: string
  student_id?: string
  user_id?: string
  // Relations
  parent_id?: string
  relationship?: string
  class_id?: string
}

export function useStudentCrud() {
  const [loading, setLoading] = useState<boolean>(false)
  const [error, setError] = useState<string | null>(null)

  // Create a new student with multiple parents
  const createStudentWithParents = async ({
    studentData,
    classId,
    parents
  }: {
    studentData: StudentData,
    classId?: string,
    parents: Array<{
      id: string;
      relationship: string;
      is_primary: boolean;
    }>
  }) => {
    setLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/students', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...studentData,
          classId,
          parents
        }),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Gagal membuat data santri')
      }

      return data
    } catch (err: any) {
      setError(err.message)
      return null
    } finally {
      setLoading(false)
    }
  }

  // Legacy create function for backward compatibility
  const createStudent = async ({
    studentData,
    classId,
    parentId,
    relationship = 'parent'
  }: {
    studentData: StudentData,
    classId?: string,
    parentId?: string,
    relationship?: string
  }) => {
    // Convert to new format
    const parents = parentId ? [{
      id: parentId,
      relationship,
      is_primary: true
    }] : []

    return createStudentWithParents({
      studentData,
      classId,
      parents
    })
  }

  // Update an existing student with multiple parents
  const updateStudentWithParents = async (
    id: string,
    {
      studentData,
      classId,
      parents
    }: {
      studentData: Partial<StudentData>,
      classId?: string,
      parents?: Array<{
        id: string;
        relationship: string;
        is_primary: boolean;
      }>
    }
  ) => {
    setLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/students', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id,
          ...studentData,
          classId,
          parents
        }),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Gagal memperbarui data santri')
      }

      return data
    } catch (err: any) {
      setError(err.message)
      return null
    } finally {
      setLoading(false)
    }
  }

  // Legacy update function for backward compatibility
  const updateStudent = async (
    id: string,
    {
      studentData,
      classId,
      parentId,
      relationship
    }: {
      studentData: Partial<StudentData>,
      classId?: string,
      parentId?: string,
      relationship?: string
    }
  ) => {
    // Convert to new format
    const parents = parentId ? [{
      id: parentId,
      relationship: relationship || 'parent',
      is_primary: true
    }] : undefined

    return updateStudentWithParents(id, {
      studentData,
      classId,
      parents
    })
  }

  // Delete a student
  const deleteStudent = async (id: string) => {
    setLoading(true)
    setError(null)
    
    try {
      const response = await fetch(`/api/students?id=${id}`, {
        method: 'DELETE',
      })
      
      const data = await response.json()
      
      if (!response.ok) {
        throw new Error(data.error || 'Gagal menghapus data santri')
      }
      
      return data
    } catch (err: any) {
      setError(err.message)
      return null
    } finally {
      setLoading(false)
    }
  }

  return {
    createStudent,
    createStudentWithParents,
    updateStudent,
    updateStudentWithParents,
    deleteStudent,
    loading,
    error,
  }
} 