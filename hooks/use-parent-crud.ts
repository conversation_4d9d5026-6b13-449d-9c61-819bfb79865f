"use client"

import { useState } from "react"

export type ParentData = {
  id?: string
  name: string
  phone: string
  email?: string
  address?: string
  occupation?: string
  user_id?: string // Linked to auth.users
}

export function useParentCrud() {
  const [loading, setLoading] = useState<boolean>(false)
  const [error, setError] = useState<string | null>(null)

  // Create a new parent
  const createParent = async (parentData: ParentData) => {
    setLoading(true)
    setError(null)
    
    try {
      const response = await fetch('/api/parents', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(parentData),
      })
      
      const data = await response.json()
      
      if (!response.ok) {
        throw new Error(data.error || 'Gagal membuat data orang tua')
      }
      
      return data
    } catch (err: any) {
      setError(err.message)
      return null
    } finally {
      setLoading(false)
    }
  }

  // Update an existing parent
  const updateParent = async (id: string, parentData: Partial<ParentData>) => {
    setLoading(true)
    setError(null)
    
    try {
      const response = await fetch('/api/parents', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ id, ...parentData }),
      })
      
      const data = await response.json()
      
      if (!response.ok) {
        throw new Error(data.error || 'Gagal memperbarui data orang tua')
      }
      
      return data
    } catch (err: any) {
      setError(err.message)
      return null
    } finally {
      setLoading(false)
    }
  }

  // Delete a parent
  const deleteParent = async (id: string) => {
    setLoading(true)
    setError(null)
    
    try {
      const response = await fetch(`/api/parents?id=${id}`, {
        method: 'DELETE',
      })
      
      const data = await response.json()
      
      if (!response.ok) {
        throw new Error(data.error || 'Gagal menghapus data orang tua')
      }
      
      return data
    } catch (err: any) {
      setError(err.message)
      return null
    } finally {
      setLoading(false)
    }
  }

  return {
    createParent,
    updateParent,
    deleteParent,
    loading,
    error,
  }
} 