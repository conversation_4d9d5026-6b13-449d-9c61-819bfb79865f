"use client"

import { useState, useEffect } from 'react'

interface WebsiteSetting {
  key: string
  value: string
  type: string
  category: string
  description?: string
}

interface WebsiteImage {
  key: string
  file_path: string
  alt_text: string
  caption: string
}

interface WebsiteContent {
  section: string
  key: string
  title: string
  content: string
  order_index: number
  is_active: boolean
  website_images?: WebsiteImage
}

export function useWebsiteSettings() {
  const [settings, setSettings] = useState<{ [key: string]: string }>({})
  const [images, setImages] = useState<{ [key: string]: WebsiteImage }>({})
  const [content, setContent] = useState<{ [key: string]: WebsiteContent[] }>({})
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    loadData()
  }, [])

  const loadData = async () => {
    try {
      setLoading(true)
      setError(null)

      // Load settings
      try {
        const settingsResponse = await fetch('/api/website-settings')
        if (settingsResponse.ok) {
          const settingsData = await settingsResponse.json()
          if (settingsData.settings && Array.isArray(settingsData.settings)) {
            const settingsMap = settingsData.settings.reduce((acc: any, setting: WebsiteSetting) => {
              acc[setting.key] = setting.value
              return acc
            }, {})
            setSettings(settingsMap)
          }
        }
      } catch (error) {
        console.error('Error loading settings:', error)
      }

      // Load images
      try {
        const imagesResponse = await fetch('/api/upload')
        if (imagesResponse.ok) {
          const imagesData = await imagesResponse.json()
          if (imagesData.images && Array.isArray(imagesData.images)) {
            const imagesMap = imagesData.images.reduce((acc: any, image: WebsiteImage) => {
              acc[image.key] = image
              return acc
            }, {})
            setImages(imagesMap)
          }
        }
      } catch (error) {
        console.error('Error loading images:', error)
      }

      // Load content
      try {
        const contentResponse = await fetch('/api/website-content')
        if (contentResponse.ok) {
          const contentData = await contentResponse.json()
          setContent(contentData.grouped || {})
        }
      } catch (error) {
        console.error('Error loading content:', error)
      }

    } catch (error: any) {
      console.error('Error loading website data:', error)
      setError('Failed to load website data')
    } finally {
      setLoading(false)
    }
  }

  // Helper functions to get specific data
  const getSetting = (key: string, defaultValue: string = '') => {
    return settings[key] || defaultValue
  }

  const getImage = (key: string) => {
    return images[key]
  }

  const getImageUrl = (key: string, defaultUrl: string = '/placeholder.svg') => {
    try {
      console.log('getImageUrl called with key:', key, 'Available images:', Object.keys(images), 'Images data:', images)
      const image = images[key]
      console.log('Found image for key:', key, image)

      if (!image?.file_path) {
        console.log('No image found for key:', key, 'returning default:', defaultUrl)
        return defaultUrl
      }

      // Convert old file_path format to API route format
      if (image.file_path.startsWith('/uploads/')) {
        const newUrl = image.file_path.replace('/uploads/', '/api/uploads/')
        console.log('Converted URL from:', image.file_path, 'to:', newUrl)
        return newUrl
      }

      console.log('Using original file_path:', image.file_path)
      return image.file_path
    } catch (error) {
      console.error('Error getting image URL for key:', key, error)
      return defaultUrl
    }
  }

  const getContent = (section: string) => {
    return content[section] || []
  }

  const getContentByKey = (section: string, key: string) => {
    const sectionContent = content[section] || []
    return sectionContent.find(item => item.key === key)
  }

  // Feature flags
  const isFeatureEnabled = (feature: string) => {
    return getSetting(`feature_${feature}`) === 'true'
  }

  return {
    settings,
    images,
    content,
    loading,
    error,
    getSetting,
    getImage,
    getImageUrl,
    getContent,
    getContentByKey,
    isFeatureEnabled,
    reload: loadData
  }
}

// Specific hooks for different sections
export function useHeroSettings() {
  const { getSetting, getImageUrl, loading } = useWebsiteSettings()

  const imageUrl = getImageUrl('hero_image', '/placeholder.svg?height=400&width=600')
  console.log('useHeroSettings - imageUrl:', imageUrl)

  return {
    title: getSetting('hero_title', 'Pondok Tahfidzul Qur\'an Al Ihsan'),
    subtitle: getSetting('hero_subtitle', 'Setara jenjang SMP dan SMK'),
    description: getSetting('hero_description', 'Pendidikan terbaik berbasis tahfidz'),
    imageUrl,
    loading
  }
}

export function usePPDBSettings() {
  const { getSetting, loading } = useWebsiteSettings()

  return {
    status: getSetting('ppdb_status', 'open'),
    batch: getSetting('ppdb_batch', 'Batch 5'),
    date: getSetting('ppdb_date', '1 Maret - 30 Juni 2024'),
    announcement: getSetting('ppdb_announcement', 'Pendaftaran telah dibuka!'),
    loading
  }
}

export function useContactSettings() {
  const { getSetting, loading } = useWebsiteSettings()

  return {
    address: getSetting('contact_address', ''),
    phone1: getSetting('contact_phone_1', ''),
    phone2: getSetting('contact_phone_2', ''),
    email1: getSetting('contact_email_1', ''),
    email2: getSetting('contact_email_2', ''),
    website: getSetting('contact_website', ''),
    operatingHours: getSetting('operating_hours', ''),
    social: {
      facebook: getSetting('social_facebook', ''),
      instagram: getSetting('social_instagram', ''),
      youtube: getSetting('social_youtube', ''),
      whatsapp: getSetting('social_whatsapp', '')
    },
    loading
  }
}

export function useProgramSettings() {
  const { getImageUrl, getContent, loading } = useWebsiteSettings()

  const programs = getContent('programs').map(program => ({
    ...program,
    imageUrl: getImageUrl(`program_${program.key}`, '/placeholder.svg?height=200&width=300')
  }))

  return {
    programs,
    loading
  }
}

export function useValueSettings() {
  const { getContent, loading } = useWebsiteSettings()

  return {
    values: getContent('values'),
    loading
  }
}

export function useFacilitiesSettings() {
  const { getImageUrl, getContent, loading } = useWebsiteSettings()

  const facilities = getContent('facilities').map(facility => ({
    ...facility,
    imageUrl: getImageUrl(`facility_${facility.key}`, '/placeholder.svg?height=200&width=300')
  }))

  return {
    facilities,
    loading
  }
}

export function useFeatureFlags() {
  const { isFeatureEnabled, loading } = useWebsiteSettings()

  return {
    ppdbMenu: isFeatureEnabled('ppdb_menu'),
    blogMenu: isFeatureEnabled('blog_menu'),
    contactForm: isFeatureEnabled('contact_form'),
    parentDashboard: isFeatureEnabled('parent_dashboard'),
    achievementPage: isFeatureEnabled('achievement_page'),
    loading
  }
}
