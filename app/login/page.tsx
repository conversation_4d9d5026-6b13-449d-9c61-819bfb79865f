import LoginForm from "@/components/auth/login-form"
import { createServerClient } from "@/utils/supabase/server"
import { redirect } from "next/navigation"
import { cookies } from "next/headers"

export default async function LoginPage() {
  // Check if user is already logged in
  const supabase = await createServerClient()
  const { data: { session } } = await supabase.auth.getSession()
  
  // If already logged in, redirect to dashboard
  if (session) {
    redirect("/dashboard")
  }
  
  return (
    <div className="container mx-auto py-16 px-4">
      <div className="max-w-md mx-auto">
        <h1 className="text-3xl font-bold text-center mb-8 text-emerald-800">Akses Dashboard</h1>
        <LoginForm />
      </div>
      
      <div className="mt-12 text-center">
        <p className="text-gray-600 mb-4">
          Belum terdaftar sebagai orangtua atau guru?
        </p>
        <p className="text-gray-600">
          Silahkan hubungi admin PTQ Al Ihsan untuk mendapatkan akses.
        </p>
      </div>
    </div>
  )
}