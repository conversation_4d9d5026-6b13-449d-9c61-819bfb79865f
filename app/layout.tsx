import type React from "react"
import { ThemeProvider } from "@/components/theme-provider"
import type { Metada<PERSON> } from "next"
import { Inter } from "next/font/google"
import "./globals.css"
import Navigation from "@/components/layout/navigation"
import Footer from "@/components/layout/footer"
import { getSession } from "@/lib/auth-helpers"
import type { Session } from "@supabase/supabase-js"
import { OrganizationStructuredData, WebsiteStructuredData } from "@/components/structured-data"

const inter = Inter({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: {
    default: "PTQ Al Ihsan - Pondok Tahfidz Quran Al Ihsan",
    template: "%s | PTQ Al Ihsan"
  },
  description: "Pondok Tahfidz Quran Al <PERSON>an <PERSON> - Lembaga pendidikan Islam berbasis tahfidz Al-Quran dengan metode pembelajaran terpadu untuk membentuk generasi Qur'ani yang berakhlak mulia.",
  keywords: [
    "pondok tahfidz", 
    "tahfidz quran", 
    "al ihsan", 
    "pendidikan islam", 
    "ungaran", 
    "pesantren", 
    "sekolah islam",
    "hafal quran",
    "santri",
    "pendidikan karakter",
    "semarang",
    "jawa tengah"
  ],
  authors: [{ name: "PTQ Al Ihsan" }],
  creator: "PTQ Al Ihsan",
  publisher: "PTQ Al Ihsan",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL('https://www.ptqalihsan.ac.id'),
  alternates: {
    canonical: '/',
  },
  openGraph: {
    type: 'website',
    locale: 'id_ID',
    url: 'https://www.ptqalihsan.ac.id',
    siteName: 'PTQ Al Ihsan',
    title: 'PTQ Al Ihsan - Pondok Tahfidz Quran Al Ihsan',
    description: 'Pondok Tahfidz Quran Al Ihsan Ungaran - Lembaga pendidikan Islam berbasis tahfidz Al-Quran dengan metode pembelajaran terpadu untuk membentuk generasi Qur\'ani yang berakhlak mulia.',
    images: [
      {
        url: '/logo.svg',
        width: 1200,
        height: 630,
        alt: 'PTQ Al Ihsan Logo',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'PTQ Al Ihsan - Pondok Tahfidz Quran Al Ihsan',
    description: 'Pondok Tahfidz Quran Al Ihsan Ungaran - Lembaga pendidikan Islam berbasis tahfidz Al-Quran',
    images: ['/logo.svg'],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: process.env.GOOGLE_SITE_VERIFICATION,
  },
}

export default async function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  // Get the session on the server
  const { session } = await getSession()
  
  return (
    <html lang="id" suppressHydrationWarning>
      <head>
        <OrganizationStructuredData />
        <WebsiteStructuredData />
      </head>
      <body className={inter.className}>
        <ThemeProvider 
          attribute="class" 
          defaultTheme="light" 
          enableSystem 
          disableTransitionOnChange
        >
          <div className="flex flex-col min-h-screen">
            <Navigation initialSession={session ?? null} />
            <main className="flex-grow">{children}</main>
            <Footer />
          </div>
        </ThemeProvider>
      </body>
    </html>
  )
}