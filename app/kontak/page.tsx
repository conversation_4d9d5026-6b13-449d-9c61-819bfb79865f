"use client"
import type React from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { MapPin, Phone, Mail, Clock, Send, Loader2 } from "lucide-react"
import { useContactSettings, useFeatureFlags } from "@/hooks/use-website-settings"

export default function ContactPage() {
  // Load dynamic data from settings
  const contactSettings = useContactSettings()
  const featureFlags = useFeatureFlags()

  // Show loading state while data is being fetched
  if (contactSettings.loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-emerald-600 mx-auto" />
          <p className="mt-2 text-gray-600">Memuat halaman kontak...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-emerald-700 to-emerald-800 text-white py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto text-center">
            <h1 className="text-4xl font-bold mb-6">Hubungi Kami</h1>
            <p className="text-xl text-emerald-100">
              Kami siap membantu Anda dengan informasi lebih lanjut tentang PTQ Al Ihsan.
            </p>
          </div>
        </div>
      </section>

      {/* Contact Info & Form */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <div>
              <h2 className="text-3xl font-bold text-gray-800 mb-6">Informasi Kontak</h2>

              <div className="space-y-6">
                <ContactInfoCard
                  icon={<MapPin className="h-6 w-6 text-emerald-600" />}
                  title="Alamat"
                  content={
                    <div className="whitespace-pre-line">
                      {contactSettings.address || "Desa Keji, Ungaran Barat\nKabupaten Semarang\nJawa Tengah, Indonesia"}
                    </div>
                  }
                />

                <ContactInfoCard
                  icon={<Phone className="h-6 w-6 text-emerald-600" />}
                  title="Telepon"
                  content={
                    <>
                      {contactSettings.phone1 && <p>{contactSettings.phone1}</p>}
                      {contactSettings.phone2 && <p>{contactSettings.phone2}</p>}
                      {!contactSettings.phone1 && !contactSettings.phone2 && (
                        <>
                          <p>+62 822-2737-4455 (Ustadz Ahmad)</p>
                          <p>+62 813-2456-7890 (Administrasi)</p>
                        </>
                      )}
                    </>
                  }
                />

                <ContactInfoCard
                  icon={<Mail className="h-6 w-6 text-emerald-600" />}
                  title="Email"
                  content={
                    <>
                      {contactSettings.email1 && <p>{contactSettings.email1}</p>}
                      {contactSettings.email2 && <p>{contactSettings.email2} (Pendaftaran)</p>}
                      {!contactSettings.email1 && !contactSettings.email2 && (
                        <>
                          <p><EMAIL></p>
                          <p><EMAIL> (Pendaftaran)</p>
                        </>
                      )}
                    </>
                  }
                />

                <ContactInfoCard
                  icon={<Clock className="h-6 w-6 text-emerald-600" />}
                  title="Jam Operasional"
                  content={
                    <div className="whitespace-pre-line">
                      {contactSettings.operatingHours || "Senin - Jumat: 08.00 - 16.00 WIB\nSabtu: 08.00 - 12.00 WIB\nMinggu & Hari Libur: Tutup"}
                    </div>
                  }
                />
              </div>

              <div className="mt-8">
                <h3 className="text-xl font-bold text-gray-800 mb-4">Ikuti Kami</h3>
                <div className="flex gap-4">
                  {contactSettings.social.instagram && (
                    <a
                      href={contactSettings.social.instagram}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="bg-emerald-100 p-2 rounded-full hover:bg-emerald-200 transition-colors"
                    >
                      <span className="sr-only">Instagram</span>
                      <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                        <path
                          fillRule="evenodd"
                          d="M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </a>
                  )}

                  {contactSettings.social.facebook && (
                    <a
                      href={contactSettings.social.facebook}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="bg-emerald-100 p-2 rounded-full hover:bg-emerald-200 transition-colors"
                    >
                      <span className="sr-only">Facebook</span>
                      <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                        <path
                          fillRule="evenodd"
                          d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </a>
                  )}

                  {contactSettings.social.youtube && (
                    <a
                      href={contactSettings.social.youtube}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="bg-emerald-100 p-2 rounded-full hover:bg-emerald-200 transition-colors"
                    >
                      <span className="sr-only">YouTube</span>
                      <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                        <path
                          fillRule="evenodd"
                          d="M19.812 5.418c.861.23 1.538.907 1.768 1.768C21.998 8.746 22 12 22 12s0 3.255-.418 4.814a2.504 2.504 0 0 1-1.768 1.768c-1.56.419-7.814.419-7.814.419s-6.255 0-7.814-.419a2.505 2.505 0 0 1-1.768-1.768C2 15.255 2 12 2 12s0-3.255.417-4.814a2.507 2.507 0 0 1 1.768-1.768C5.744 5 11.998 5 11.998 5s6.255 0 7.814.418ZM15.194 12 10 15V9l5.194 3Z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </a>
                  )}

                  {contactSettings.social.whatsapp && (
                    <a
                      href={contactSettings.social.whatsapp}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="bg-emerald-100 p-2 rounded-full hover:bg-emerald-200 transition-colors"
                    >
                      <span className="sr-only">WhatsApp</span>
                      <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                        <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/>
                      </svg>
                    </a>
                  )}
                </div>
              </div>
            </div>

            <div>
              {featureFlags.contactForm ? (
                <Card>
                  <CardHeader>
                    <CardTitle>Kirim Pesan</CardTitle>
                    <CardDescription>Isi formulir di bawah ini untuk mengirim pesan kepada kami.</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <form className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="name">Nama Lengkap</Label>
                          <Input id="name" placeholder="Masukkan nama lengkap" />
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="email">Email</Label>
                          <Input id="email" type="email" placeholder="Masukkan email" />
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="phone">Nomor Telepon</Label>
                        <Input id="phone" placeholder="Masukkan nomor telepon" />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="subject">Subjek</Label>
                        <Input id="subject" placeholder="Masukkan subjek pesan" />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="message">Pesan</Label>
                        <Textarea id="message" placeholder="Masukkan pesan Anda" rows={5} />
                      </div>
                    </form>
                  </CardContent>
                  <CardFooter>
                    <Button className="w-full gap-2">
                      <Send className="h-4 w-4" />
                      Kirim Pesan
                    </Button>
                  </CardFooter>
                </Card>
              ) : (
                <Card>
                  <CardHeader>
                    <CardTitle>Hubungi Langsung</CardTitle>
                    <CardDescription>Silakan hubungi kami melalui kontak yang tersedia.</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <p className="text-gray-600">
                        Untuk informasi lebih lanjut, silakan hubungi kami melalui:
                      </p>
                      <div className="space-y-2">
                        {contactSettings.phone1 && (
                          <p className="flex items-center gap-2">
                            <Phone className="h-4 w-4 text-emerald-600" />
                            <a href={`tel:${contactSettings.phone1}`} className="text-emerald-600 hover:underline">
                              {contactSettings.phone1}
                            </a>
                          </p>
                        )}
                        {contactSettings.email1 && (
                          <p className="flex items-center gap-2">
                            <Mail className="h-4 w-4 text-emerald-600" />
                            <a href={`mailto:${contactSettings.email1}`} className="text-emerald-600 hover:underline">
                              {contactSettings.email1}
                            </a>
                          </p>
                        )}
                        {contactSettings.social.whatsapp && (
                          <p className="flex items-center gap-2">
                            <svg className="h-4 w-4 text-emerald-600" fill="currentColor" viewBox="0 0 24 24">
                              <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/>
                            </svg>
                            <a href={contactSettings.social.whatsapp} target="_blank" rel="noopener noreferrer" className="text-emerald-600 hover:underline">
                              WhatsApp
                            </a>
                          </p>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          </div>
        </div>
      </section>

      {/* Map Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-800 mb-4">Lokasi Kami</h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Kunjungi PTQ Al Ihsan di lokasi yang strategis dan mudah dijangkau.
            </p>
          </div>

          <div className="bg-gray-200 h-96 rounded-xl overflow-hidden">
            {/* This would be replaced with an actual map component */}
            <div className="w-full h-full flex items-center justify-center">
              <p className="text-gray-500">Peta Lokasi PTQ Al Ihsan</p>
            </div>
          </div>

          <div className="mt-6 text-center">
            <Button variant="outline" className="gap-2">
              <MapPin className="h-4 w-4" />
              Lihat di Google Maps
            </Button>
          </div>
        </div>
      </section>
    </div>
  )
}

function ContactInfoCard({ icon, title, content }: { icon: React.ReactNode; title: string; content: React.ReactNode }) {
  return (
    <div className="flex gap-4">
      <div className="bg-emerald-100 p-3 rounded-full h-12 w-12 flex items-center justify-center flex-shrink-0">
        {icon}
      </div>
      <div>
        <h3 className="text-lg font-bold text-gray-800 mb-2">{title}</h3>
        <div className="text-gray-600 space-y-1">{content}</div>
      </div>
    </div>
  )
}

