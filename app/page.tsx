"use client"
import type React from "react"
import Link from "next/link"
import Image from "next/image"
import { ArrowRight, BookOpen, Mic, BookText, Languages, Award, Users, Loader2 } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ParentAccessDialog } from "@/components/parent-access-dialog"
import { useHeroSettings, usePPDBSettings, useValueSettings, useProgramSettings, useFeatureFlags } from "@/hooks/use-website-settings"

// Helper function to get icon for value cards
function getValueIcon(key: string) {
  const iconMap: { [key: string]: React.ReactNode } = {
    tahfidz: <BookOpen className="h-10 w-10 text-emerald-600" />,
    aqidah: <BookText className="h-10 w-10 text-emerald-600" />,
    hadits: <Award className="h-10 w-10 text-emerald-600" />,
    fiqih: <BookText className="h-10 w-10 text-emerald-600" />,
    nahwu: <Languages className="h-10 w-10 text-emerald-600" />,
    kitab: <BookOpen className="h-10 w-10 text-emerald-600" />,
    speaking: <Mic className="h-10 w-10 text-emerald-600" />,
    akhlak: <Users className="h-10 w-10 text-emerald-600" />
  }
  return iconMap[key] || <BookOpen className="h-10 w-10 text-emerald-600" />
}

export default function Home() {
  // Load dynamic data from settings
  const heroSettings = useHeroSettings()
  const ppdbSettings = usePPDBSettings()
  const valueSettings = useValueSettings()
  const programSettings = useProgramSettings()
  const featureFlags = useFeatureFlags()

  // Show loading state while data is being fetched
  if (heroSettings.loading || ppdbSettings.loading || valueSettings.loading || programSettings.loading) {
    return (
      <div className="flex flex-col min-h-screen items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-emerald-600" />
        <p className="mt-2 text-gray-600">Memuat halaman...</p>
      </div>
    )
  }

  return (
    <div className="flex flex-col min-h-screen">
      {/* Hero Section */}
      <section className="bg-gradient-to-b from-emerald-800 to-emerald-700 text-white py-16 md:py-24">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row items-center gap-8">
            <div className="md:w-1/2 space-y-6">
              <h1 className="text-4xl md:text-5xl font-bold leading-tight">
                {heroSettings.title}
              </h1>
              <p className="text-xl text-emerald-100">{heroSettings.subtitle}</p>
              {heroSettings.description && (
                <p className="text-lg text-emerald-200">{heroSettings.description}</p>
              )}
              <div className="pt-4 flex flex-wrap gap-3">
                {featureFlags.ppdbMenu && (
                  <Button asChild size="lg" className="bg-white text-emerald-800 hover:bg-emerald-100">
                    <Link href="/ppdb" className="flex items-center gap-2">
                      Daftar Sekarang
                      <ArrowRight className="h-4 w-4" />
                    </Link>
                  </Button>
                )}
                {featureFlags.parentDashboard && <ParentAccessDialog />}
              </div>
            </div>
            <div className="md:w-1/2 relative">
              <div className="relative w-full h-[400px] rounded-lg overflow-hidden shadow-2xl">
                <Image
                  src={heroSettings.imageUrl}
                  alt="Santri PTQ Al Ihsan"
                  fill
                  className="object-cover"
                />
              </div>
              {ppdbSettings.status === 'open' && (
                <div className="absolute -bottom-6 -left-6 bg-white p-4 rounded-lg shadow-xl">
                  <p className="text-emerald-800 font-bold">Pendaftaran {ppdbSettings.batch}</p>
                  <p className="text-gray-600">Dibuka: {ppdbSettings.date}</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </section>

      {/* Value Highlights */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-800 mb-4">Nilai Utama Kami</h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              PTQ Al Ihsan fokus pada pengembangan santri yang unggul, berbekal hafalan Al-Qur'an, penguasaan dasar
              agama Islam dan pembentukan pribadi santri.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {valueSettings.values.length > 0 ? (
              valueSettings.values.map((value) => (
                <ValueCard
                  key={value.key}
                  icon={getValueIcon(value.key)}
                  title={value.title}
                  description={value.content}
                />
              ))
            ) : (
              // Fallback static values if no data from database
              <>
                <ValueCard
                  icon={<BookOpen className="h-10 w-10 text-emerald-600" />}
                  title="Hafal Al Qur'an"
                  description="Minimal 7 Juz dengan pemahaman yang baik"
                />
                <ValueCard
                  icon={<BookText className="h-10 w-10 text-emerald-600" />}
                  title="Penguasaan Aqidah"
                  description="Matan Aqidah Ushulus Tsalasah"
                />
                <ValueCard
                  icon={<Award className="h-10 w-10 text-emerald-600" />}
                  title="Hafal Hadits Arbain"
                  description="Hafal dan memahami 42 hadits pilihan"
                />
                <ValueCard
                  icon={<BookText className="h-10 w-10 text-emerald-600" />}
                  title="Fiqih Hidayatul Mutafaqqih"
                  description="Hafalan mutun bab pilihan dan Pemahaman fiqih"
                />
                <ValueCard
                  icon={<Languages className="h-10 w-10 text-emerald-600" />}
                  title="Hafal Al Ajurumiyah"
                  description="Dasar-dasar tata bahasa Arab"
                />
                <ValueCard
                  icon={<BookOpen className="h-10 w-10 text-emerald-600" />}
                  title="Kitab Gundul"
                  description="Kemampuan membaca kitab tanpa harakat"
                />
                <ValueCard
                  icon={<Mic className="h-10 w-10 text-emerald-600" />}
                  title="Public Speaking"
                  description="Kemampuan berpidato dan presentasi"
                />
                <ValueCard
                  icon={<Users className="h-10 w-10 text-emerald-600" />}
                  title="Akhlak Mulia"
                  description="Pembentukan karakter Islami"
                />
              </>
            )}
          </div>
        </div>
      </section>

      {/* Programs Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-800 mb-4">Program Unggulan</h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Kurikulum komprehensif yang dirancang untuk menguatkan santri dalam ilmu agama dan mempersiapkan
              keterampilan hidup, agar mampu menghadapi tantangan zaman.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {programSettings.programs.length > 0 ? (
              programSettings.programs.map((program) => (
                <ProgramCard
                  key={program.key}
                  image={program.imageUrl}
                  title={program.title}
                  description={program.content}
                />
              ))
            ) : (
              // Fallback static programs if no data from database
              <>
                <ProgramCard
                  image="/placeholder.svg?height=200&width=300"
                  title="Tahfidz Al-Qur'an"
                  description="Program hafalan Al-Qur'an dengan target minimal 7 juz selama masa pendidikan."
                />
                <ProgramCard
                  image="/placeholder.svg?height=200&width=300"
                  title="Kajian Kitab Kuning"
                  description="Pembelajaran kitab-kitab klasik dengan metode yang mudah dipahami."
                />
                <ProgramCard
                  image="/placeholder.svg?height=200&width=300"
                  title="Public Speaking"
                  description="Pelatihan kemampuan berbicara di depan umum, pidato, dan presentasi."
                />
              </>
            )}
          </div>

          <div className="text-center mt-10">
            <Button asChild variant="outline" className="gap-2">
              <Link href="/program">
                Lihat Semua Program
                <ArrowRight className="h-4 w-4" />
              </Link>
            </Button>
          </div>
        </div>
      </section>

      {/* Testimonials */}
      <section className="py-16 bg-emerald-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-800 mb-4">Testimoni Orang Tua</h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Apa kata orang tua santri tentang pendidikan di PTQ Al Ihsan.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <TestimonialCard
              quote="Sebagai orang tua, Alhamdulillah kami merasa senang dan tenang ketika menyekolahkan anak kami di PTQ Al Ihsan. Para ustadz sering mengirimkan dokumentasi kegiatan positif ketika di pesantren maupun ketika kegiatan ekstra diluar pesantren. Terbukti ketika anak pulang libur ketika sekolah semangat menceritakan kegiatan di pesantren dan tidak merasa tertekan dengan apa yang diajarkan di pesantren. Alhamdulillah Sekolah pun bersih dan rapi Masya Allah...."
              name="Panji Nugroho"
              role="Wali Santri Ananda Fauzil kelas 8"
            />
            <TestimonialCard
              quote="Alhamdulillah kami selaku orang tua bersyukur dapat memilih sekolah PTQ Al Ihsan ungaran. Program kegiatan keseharian menerapkan kedisiplinan dengan jadwal teratur santri dari mulai bangun tidur sampai malam menjelang tidur. Fasilitas asrama yang baru, bersih dan nyaman, ruang kelas yang kondusif untuk belajar dengan nyaman serta udara yang sejuk karena lokasi pondok di lereng gunung ungaran."
              name="Abu Omar Fendi, S.Kom"
              role="Wali Santri Ananda Omar kelas 7"
            />
            <TestimonialCard
              quote="Alhamdulillah, Anak saya Kiano sudah banyak perkembangannya ustadz... Rajin puasa... Sudah hafal hadist-hadist walaupun belum banyak... Tambah dewasa dan tambah banyak ilmunya... Terima kasih ustadz sudah membimbing dan membersamai anak-anak."
              name="Ummu Kiano"
              role="Wali Santri kelas 7"
            />
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-gradient-to-r from-emerald-700 to-emerald-800 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-6">Siap Bergabung dengan PTQ Al Ihsan?</h2>
          <p className="text-xl text-emerald-100 mb-8 max-w-2xl mx-auto">
            Daftarkan putra/putri Anda sekarang dan berikan mereka pendidikan terbaik untuk masa depan yang cerah.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            {featureFlags.ppdbMenu && (
              <Button asChild size="lg" className="bg-white text-emerald-800 hover:bg-emerald-100">
                <Link href="/ppdb">Daftar Sekarang</Link>
              </Button>
            )}
            <Button
              asChild
              size="lg"
              variant="outline"
              className="text-white border-white/20 hover:bg-white/10 hover:text-white"
            >
              <Link href="/kontak">Hubungi Kami</Link>
            </Button>
          </div>
        </div>
      </section>
    </div>
  )
}

function ValueCard({ icon, title, description }: { icon: React.ReactNode; title: string; description: string }) {
  return (
    <div className="bg-white p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow border border-gray-100">
      <div className="mb-4">{icon}</div>
      <h3 className="text-lg font-bold text-gray-800 mb-2">{title}</h3>
      <p className="text-gray-600">{description}</p>
    </div>
  )
}

function ProgramCard({ image, title, description }: { image: string; title: string; description: string }) {
  return (
    <div className="bg-white rounded-xl shadow-md overflow-hidden hover:shadow-lg transition-shadow border border-gray-100">
      <div className="relative h-48">
        <Image src={image || "/placeholder.svg"} alt={title} fill className="object-cover" />
      </div>
      <div className="p-6">
        <h3 className="text-xl font-bold text-gray-800 mb-2">{title}</h3>
        <p className="text-gray-600 mb-4">{description}</p>
        <Button asChild variant="link" className="p-0 h-auto text-emerald-600 hover:text-emerald-700">
          <Link href="/program" className="flex items-center gap-1">
            Pelajari Lebih Lanjut
            <ArrowRight className="h-3 w-3" />
          </Link>
        </Button>
      </div>
    </div>
  )
}

function TestimonialCard({ quote, name, role }: { quote: string; name: string; role: string }) {
  return (
    <div className="bg-white p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow border border-gray-100">
      <div className="text-emerald-500 mb-4">
        <svg className="h-8 w-8" fill="currentColor" viewBox="0 0 24 24">
          <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h3.983v10h-9.983z" />
        </svg>
      </div>
      <p className="text-gray-600 mb-6">{quote}</p>
      <div>
        <p className="font-bold text-gray-800">{name}</p>
        <p className="text-gray-500 text-sm">{role}</p>
      </div>
    </div>
  )
}

