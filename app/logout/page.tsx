"use client"

import { useEffect } from "react"
import { useRouter } from "next/navigation"
import { createBrowserClient } from "@/utils/supabase/client"
import { Loader2 } from "lucide-react"

export default function LogoutPage() {
  const router = useRouter()
  const supabase = createBrowserClient()

  useEffect(() => {
    async function signOut() {
      try {
        await supabase.auth.signOut()
        // Redirect to login page after signout
        router.push("/login")
      } catch (error) {
        console.error("Error during logout:", error)
        // Still redirect to login even if there's an error
        router.push("/login")
      }
    }
    
    signOut()
  }, [router, supabase.auth])

  return (
    <div className="h-screen flex items-center justify-center">
      <div className="text-center">
        <Loader2 className="mx-auto h-8 w-8 animate-spin text-emerald-500" />
        <h1 className="mt-4 text-xl font-medium">Logging out...</h1>
        <p className="mt-2 text-sm text-gray-500">You are being redirected to the login page.</p>
      </div>
    </div>
  )
} 