"use client"

import { useState, useEffect } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { User, GraduationCap, Loader2, AlertCircle } from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"

interface Student {
  id: string
  student_id: string
  name: string
  photo_url: string | null
  birth_date: string
  address: string
  class: {
    id: string
    name: string
    level: string
  } | null
}

interface Parent {
  id: string
  name: string
  phone: string
}

export default function ParentDashboard() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [mounted, setMounted] = useState(false)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [parent, setParent] = useState<Parent | null>(null)
  const [students, setStudents] = useState<Student[]>([])

  const studentId = searchParams.get('student_id')
  const phone = searchParams.get('phone')

  useEffect(() => {
    setMounted(true)
    loadParentData()
  }, [studentId, phone])

  const loadParentData = async () => {
    try {
      setLoading(true)
      setError(null)

      if (!studentId && !phone) {
        setError("Parameter student_id atau phone diperlukan")
        return
      }

      const params = new URLSearchParams()
      if (studentId) params.append('student_id', studentId)
      if (phone) params.append('phone', phone)

      const response = await fetch(`/api/parent-dashboard?${params}`)
      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to load data')
      }

      if (data.parent && data.students) {
        // Multiple students for parent
        setParent(data.parent)
        setStudents(data.students)
      } else if (data.student) {
        // Single student data
        setStudents([data.student])
      }

    } catch (error: any) {
      console.error('Error loading parent data:', error)
      setError(error.message || 'Terjadi kesalahan saat memuat data')
    } finally {
      setLoading(false)
    }
  }

  if (!mounted) return null

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p>Memuat data...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <Alert className="max-w-md">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <header className="bg-gradient-to-r from-emerald-700 to-emerald-800 text-white p-4 shadow-lg">
        <div className="container mx-auto flex flex-col md:flex-row justify-between items-center">
          <div className="flex items-center gap-3">
            <GraduationCap className="h-8 w-8" />
            <h1 className="text-2xl font-bold mb-4 md:mb-0">PTQ Al Ihsan - Portal Orang Tua</h1>
          </div>
          <div className="flex items-center gap-3">
            <Avatar className="border-2 border-white/20">
              <AvatarImage src="/placeholder.svg?height=40&width=40" alt="Avatar" />
              <AvatarFallback>OT</AvatarFallback>
            </Avatar>
            <div>
              <span className="font-medium">Ahmad Hidayat</span>
              <p className="text-xs text-white/70">Orang Tua Santri</p>
            </div>
          </div>
        </div>
      </header>

      <main className="container mx-auto p-4 md:p-6">
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-gray-800 mb-4">Santri Saya</h2>
          <p className="text-gray-600 mb-6">Pilih santri untuk melihat profil dan laporan perkembangan</p>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {students.map((student) => (
              <Card
                key={student.id}
                className="cursor-pointer hover:shadow-lg transition-all duration-300 hover:border-emerald-200 hover:translate-y-[-2px] overflow-hidden group"
                onClick={() => router.push(`/parent-dashboard/student/${student.id}`)}
              >
                <div className="h-2 bg-emerald-600 group-hover:bg-emerald-500 transition-colors" />
                <CardContent className="p-6">
                  <div className="flex items-center gap-4">
                    <Avatar className="h-16 w-16 border-2 border-emerald-100">
                      <AvatarImage src={student.photo_url || "/placeholder.svg"} alt={student.name} />
                      <AvatarFallback>
                        <User />
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <h3 className="font-semibold text-lg">{student.name}</h3>
                      <p className="text-sm text-gray-500">{student.class?.name || 'Belum ada kelas'}</p>
                      <p className="text-xs text-gray-400">{student.student_id}</p>
                      <Button
                        variant="outline"
                        size="sm"
                        className="mt-2 text-emerald-600 border-emerald-200 hover:bg-emerald-50 hover:text-emerald-700"
                      >
                        Lihat Detail
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-md p-6 mb-8">
          <h3 className="text-xl font-bold text-gray-800 mb-4">Pengumuman Terbaru</h3>
          <div className="space-y-4">
            <div className="border-l-4 border-emerald-500 pl-4 py-1">
              <p className="font-medium">Jadwal Ujian Tahfidz Semester Genap</p>
              <p className="text-sm text-gray-600">Ujian Tahfidz akan dilaksanakan pada tanggal 15-20 Juni 2024</p>
              <p className="text-xs text-gray-500 mt-1">10 Mei 2024</p>
            </div>
            <div className="border-l-4 border-emerald-500 pl-4 py-1">
              <p className="font-medium">Pertemuan Orang Tua dan Guru</p>
              <p className="text-sm text-gray-600">Akan diadakan pada hari Sabtu, 25 Mei 2024 pukul 09.00 WIB</p>
              <p className="text-xs text-gray-500 mt-1">5 Mei 2024</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-md p-6">
          <h3 className="text-xl font-bold text-gray-800 mb-4">Kalender Kegiatan</h3>
          <div className="space-y-3">
            <div className="flex items-center gap-3">
              <div className="bg-emerald-100 text-emerald-800 h-12 w-12 rounded-lg flex items-center justify-center font-bold">
                25 Mei
              </div>
              <div>
                <p className="font-medium">Pertemuan Orang Tua dan Guru</p>
                <p className="text-sm text-gray-600">09.00 - 12.00 WIB</p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <div className="bg-emerald-100 text-emerald-800 h-12 w-12 rounded-lg flex items-center justify-center font-bold">
                15 Jun
              </div>
              <div>
                <p className="font-medium">Ujian Tahfidz Dimulai</p>
                <p className="text-sm text-gray-600">08.00 - 15.00 WIB</p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <div className="bg-emerald-100 text-emerald-800 h-12 w-12 rounded-lg flex items-center justify-center font-bold">
                30 Jun
              </div>
              <div>
                <p className="font-medium">Pembagian Rapor Semester Genap</p>
                <p className="text-sm text-gray-600">09.00 - 12.00 WIB</p>
              </div>
            </div>
          </div>
        </div>
      </main>

      <footer className="bg-gray-100 border-t py-6 mt-12">
        <div className="container mx-auto text-center text-gray-500 text-sm">
          &copy; {new Date().getFullYear()} PTQ Al Ihsan. Hak Cipta Dilindungi.
        </div>
      </footer>
    </div>
  )
}
