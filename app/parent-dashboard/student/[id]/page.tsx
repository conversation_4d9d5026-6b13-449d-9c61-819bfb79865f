"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { ArrowLeft, Calendar, BookOpen, Award, CheckCircle2, Loader2, AlertCircle } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { cn } from "@/lib/utils"

interface Student {
  id: string
  student_id: string
  name: string
  photo_url: string | null
  birth_date: string
  address: string
  phone: string | null
  email: string | null
  class: {
    id: string
    name: string
    level: string
  } | null
}

interface Achievement {
  id: string
  subject_id: string
  value: string
  grade: string
  notes: string | null
  achievement_date: string
  subjects: {
    id: string
    name: string
    category: string
  } | null
}

interface AttendanceStats {
  total: number
  present: number
  absent: number
  late: number
  excused: number
  percentage: number
}

interface TimelineDetail {
  id: string
  subject_id: string
  value: string
  grade: string
  notes: string | null
  subjects: {
    id: string
    name: string
    category: string
  } | null
}

interface TimelineActivity {
  id: string
  activity: string
}

interface BehaviorRecord {
  id: string
  behavior_grade: string
  notes: string | null
}

interface TimelineEntry {
  id: string
  month: string
  year: number
  created_at: string
  timeline_details: TimelineDetail[]
  timeline_activities: TimelineActivity[]
  behavior_records: BehaviorRecord[]
}

// Helper function to get grade color
const getGradeColor = (grade: string) => {
  switch (grade) {
    case "Jayyid Jiddan":
      return "text-emerald-100 bg-emerald-800/50"
    case "Jayyid":
      return "text-emerald-100 bg-emerald-700/50"
    case "Maqbul":
      return "text-emerald-100 bg-emerald-600/50"
    default:
      return "text-emerald-100 bg-emerald-500/50"
  }
}

// Helper function to format month year
const formatMonthYear = (month: string, year: number) => {
  const monthNames: { [key: string]: string } = {
    'January': 'Januari',
    'February': 'Februari',
    'March': 'Maret',
    'April': 'April',
    'May': 'Mei',
    'June': 'Juni',
    'July': 'Juli',
    'August': 'Agustus',
    'September': 'September',
    'October': 'Oktober',
    'November': 'November',
    'December': 'Desember'
  }
  return `${monthNames[month] || month} ${year}`
}

export default function StudentDetailPage({ params }: { params: Promise<{ id: string }> }) {
  const router = useRouter()
  const [mounted, setMounted] = useState(false)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [student, setStudent] = useState<Student | null>(null)
  const [achievements, setAchievements] = useState<Achievement[]>([])
  const [attendance, setAttendance] = useState<AttendanceStats | null>(null)
  const [timeline, setTimeline] = useState<TimelineEntry[]>([])
  const [studentId, setStudentId] = useState<string | null>(null)

  useEffect(() => {
    setMounted(true)
    const getParams = async () => {
      const resolvedParams = await params
      setStudentId(resolvedParams.id)
    }
    getParams()
  }, [])

  useEffect(() => {
    if (studentId) {
      loadStudentData()
    }
  }, [studentId])

  const loadStudentData = async () => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch(`/api/parent-dashboard?student_id=${studentId}`)
      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to load student data')
      }

      setStudent(data.student)
      setAchievements(data.achievements || [])
      setAttendance(data.attendance || null)
      setTimeline(data.timeline || [])

    } catch (error: any) {
      console.error('Error loading student data:', error)
      setError(error.message || 'Terjadi kesalahan saat memuat data')
    } finally {
      setLoading(false)
    }
  }

  if (!mounted) return null

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p>Memuat data santri...</p>
        </div>
      </div>
    )
  }

  if (error || !student) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <Alert className="max-w-md">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {error || 'Data santri tidak ditemukan'}
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <header className="bg-gradient-to-r from-emerald-700 to-emerald-800 text-white p-4 shadow-lg">
        <div className="container mx-auto">
          <Button
            variant="ghost"
            className="text-white hover:bg-white/10 -ml-2 mb-4"
            onClick={() => router.push("/parent-dashboard")}
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Kembali ke Dashboard
          </Button>
          <div className="flex flex-col md:flex-row gap-6 items-center md:items-start">
            <Avatar className="h-24 w-24 border-4 border-white/20">
              <AvatarImage src={student.photo_url || "/placeholder.svg"} alt={student.name} />
              <AvatarFallback className="text-2xl">{student.name.charAt(0)}</AvatarFallback>
            </Avatar>
            <div>
              <h1 className="text-3xl font-bold">{student.name}</h1>
              <p className="text-emerald-100">{student.class?.name || 'Belum ada kelas'}</p>
              <p className="text-emerald-200 text-sm">{student.student_id}</p>
              <div className="flex flex-wrap gap-2 mt-2">
                <Badge className="bg-white/20 hover:bg-white/30">Santri Aktif</Badge>
                <Badge className="bg-white/20 hover:bg-white/30">Tahfidz</Badge>
              </div>
            </div>
          </div>
        </div>
      </header>

      <main className="container mx-auto p-4 md:p-6">
        <Tabs defaultValue="profile" className="w-full">
          <TabsList className="grid w-full md:w-auto grid-cols-2">
            <TabsTrigger value="profile" className="flex items-center gap-2">
              <BookOpen className="h-4 w-4" />
              <span>Profil & Pencapaian</span>
            </TabsTrigger>
            <TabsTrigger value="timeline" className="flex items-center gap-2">
              <Calendar className="h-4 w-4" />
              <span>Timeline Bulanan</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="profile" className="mt-6">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <div className="lg:col-span-1">
                <Card>
                  <CardHeader>
                    <CardTitle>Informasi Santri</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <p className="text-sm text-gray-500">Nama Lengkap</p>
                      <p className="font-medium">{student.name}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">ID Santri</p>
                      <p className="font-medium">{student.student_id}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Kelas</p>
                      <p className="font-medium">{student.class?.name || 'Belum ada kelas'}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Alamat</p>
                      <p className="font-medium">{student.address || '-'}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Tanggal Lahir</p>
                      <p className="font-medium">{student.birth_date ? new Date(student.birth_date).toLocaleDateString('id-ID') : '-'}</p>
                    </div>
                    {attendance && (
                      <div>
                        <p className="text-sm text-gray-500">Kehadiran (30 hari terakhir)</p>
                        <p className="font-medium text-emerald-600">{attendance.percentage}%</p>
                        <p className="text-xs text-gray-400">
                          {attendance.present} hadir dari {attendance.total} hari
                        </p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>

              <div className="lg:col-span-2">
                <Card>
                  <CardHeader>
                    <CardTitle>Pencapaian Santri</CardTitle>
                  </CardHeader>
                  <CardContent>
                    {achievements.length > 0 ? (
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        {achievements.map((achievement) => (
                          <AchievementCard
                            key={achievement.id}
                            title={achievement.subjects?.name || 'Mata Pelajaran'}
                            icon="📖"
                            value={achievement.value}
                            grade={achievement.grade}
                            notes={achievement.notes}
                            date={achievement.achievement_date}
                          />
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-8">
                        <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                        <p className="text-gray-500">Belum ada pencapaian yang tercatat</p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="timeline" className="mt-6">
            <Card>
              <CardHeader>
                <CardTitle>Laporan Perkembangan Bulanan</CardTitle>
              </CardHeader>
              <CardContent>
                {timeline.length > 0 ? (
                  <div className="relative border-l-2 border-emerald-500 pl-6 ml-4">
                    {timeline.map((entry, index) => (
                      <TimelineReportItem
                        key={entry.id}
                        entry={entry}
                        isLast={index === timeline.length - 1}
                      />
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-500">Belum ada laporan timeline yang tercatat</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </main>

      <footer className="bg-gray-100 border-t py-6 mt-12">
        <div className="container mx-auto text-center text-gray-500 text-sm">
          &copy; {new Date().getFullYear()} PTQ Al Ihsan. Hak Cipta Dilindungi.
        </div>
      </footer>
    </div>
  )
}

interface AchievementCardProps {
  title: string
  icon: string
  value: string
  grade: string
  notes?: string | null
  date?: string
  className?: string
}

function AchievementCard({ title, icon, value, grade, notes, date, className }: AchievementCardProps) {
  return (
    <div className={cn("group", className)}>
      <p className="text-center text-gray-700 mb-2 font-medium">{title}</p>
      <div className="bg-emerald-50 rounded-xl p-4 shadow-sm border border-emerald-100 transition-all duration-300 group-hover:bg-emerald-100 group-hover:translate-y-[-3px] relative">
        <div className="absolute -top-3 left-1/2 transform -translate-x-1/2 bg-gradient-to-br from-emerald-400 to-emerald-600 rounded-full p-1 w-8 h-8 flex items-center justify-center shadow-lg border-2 border-white">
          <span className="text-sm">{icon}</span>
        </div>
        <div className="text-center pt-2 mt-2">
          <h3 className="text-lg md:text-xl font-bold text-gray-800 mb-2">{value}</h3>
          <Badge className={cn("font-medium px-3 py-1", getGradeColor(grade))}>{grade}</Badge>
          {notes && (
            <p className="text-xs text-gray-600 mt-2">{notes}</p>
          )}
          {date && (
            <p className="text-xs text-gray-500 mt-1">
              {new Date(date).toLocaleDateString('id-ID')}
            </p>
          )}
        </div>
      </div>
    </div>
  )
}

interface TimelineReportItemProps {
  entry: TimelineEntry
  isLast: boolean
}

function TimelineReportItem({ entry, isLast }: TimelineReportItemProps) {
  const monthYear = formatMonthYear(entry.month, entry.year)

  return (
    <div className={cn("mb-8", isLast ? "" : "pb-8")}>
      <div className="absolute -left-4 mt-1.5">
        <div className="bg-emerald-500 text-white rounded-full h-7 w-7 flex items-center justify-center">
          <Calendar className="h-4 w-4" />
        </div>
      </div>

      <h3 className="text-xl font-bold text-gray-800 mb-3">{monthYear}</h3>

      {/* Timeline Details (Subjects) */}
      {entry.timeline_details.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          {entry.timeline_details.map((detail) => (
            <Card key={detail.id}>
              <CardHeader className="pb-2">
                <CardTitle className="text-base">{detail.subjects?.name || 'Mata Pelajaran'}</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex justify-between items-center mb-2">
                  <p className="font-medium">{detail.value}</p>
                  <Badge className={cn("font-medium", getGradeColor(detail.grade))}>
                    {detail.grade}
                  </Badge>
                </div>
                {detail.notes && (
                  <p className="text-sm text-gray-600">{detail.notes}</p>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Behavior Records */}
      {entry.behavior_records.length > 0 && (
        <div className="mb-4">
          <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-100">
            <h4 className="font-medium mb-2 flex items-center gap-2">
              <Award className="h-4 w-4 text-emerald-500" />
              Perilaku
            </h4>
            {entry.behavior_records.map((behavior) => (
              <div key={behavior.id}>
                <p className="text-lg font-bold text-emerald-600">{behavior.behavior_grade}</p>
                {behavior.notes && (
                  <p className="text-sm text-gray-600 mt-1">{behavior.notes}</p>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Activities */}
      {entry.timeline_activities.length > 0 && (
        <div className="mt-4 bg-white p-4 rounded-lg shadow-sm border border-gray-100">
          <h4 className="font-medium mb-2">Aktivitas & Prestasi</h4>
          <ul className="list-disc pl-5 space-y-1">
            {entry.timeline_activities.map((activity) => (
              <li key={activity.id} className="text-gray-700">
                {activity.activity}
              </li>
            ))}
          </ul>
        </div>
      )}

      {/* Show empty state if no data */}
      {entry.timeline_details.length === 0 &&
       entry.timeline_activities.length === 0 &&
       entry.behavior_records.length === 0 && (
        <div className="bg-gray-50 p-4 rounded-lg text-center">
          <p className="text-gray-500 text-sm">Belum ada data untuk bulan ini</p>
        </div>
      )}
    </div>
  )
}
