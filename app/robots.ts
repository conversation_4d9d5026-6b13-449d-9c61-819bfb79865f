import { MetadataRoute } from 'next'

export default function robots(): MetadataRoute.Robots {
  const baseUrl = 'https://www.ptqalihsan.ac.id'

  return {
    rules: [
      {
        userAgent: '*',
        allow: [
          '/',
          '/tentang-kami',
          '/program', 
          '/ppdb',
          '/kontak',
          '/blog',
          '/blog/*'
        ],
        disallow: [
          '/dashboard/*',
          '/login',
          '/logout', 
          '/parent-dashboard/*',
          '/api/*',
          '/admin/*',
          '/temp/*',
          '/_next/*',
          '/uploads/*'
        ],
      },
      // Khusus untuk search engine bots
      {
        userAgent: 'Googlebot',
        allow: '/',
        disallow: [
          '/dashboard/*',
          '/api/*',
          '/admin/*'
        ]
      }
    ],
    sitemap: `${baseUrl}/sitemap.xml`,
    host: baseUrl
  }
} 