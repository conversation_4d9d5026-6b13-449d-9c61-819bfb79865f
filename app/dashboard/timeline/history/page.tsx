"use client"

import { useState, useEffect, Suspense } from "react"
import { use<PERSON><PERSON><PERSON>, useSearchParams } from "next/navigation"
import { 
  Calendar, 
  Users, 
  Filter, 
  Search, 
  Eye, 
  Clock, 
  BookOpen, 
  Activity,
  User,
  GraduationCap,
  ChevronLeft,
  ChevronRight,
  BarChart3,
  FileText,
  Download
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/scroll-area"

interface TimelineEntry {
  id: string
  student_id: string
  teacher_id: string
  month: string
  year: number
  created_at: string
  updated_at: string
  student_name: string
  student_id_display: string
  teacher_name: string
  teacher_employee_id: string
  class_name: string
  class_id_display: string
  subjects_count: number
  activities_count: number
  has_behavior: boolean
  input_type: string
  last_modified: string
  timeline_details: any[]
  timeline_activities: any[]
  behavior_records: any[]
}

interface HistoryResponse {
  timeline_entries: TimelineEntry[]
  total_count: number
  page: number
  limit: number
  total_pages: number
  filters: any
}

interface Class {
  id: string
  name: string
  class_id: string
}

interface Teacher {
  id: string
  name: string
  employee_id: string
}

function TimelineHistoryContent() {
  const router = useRouter()
  const searchParams = useSearchParams()
  
  // State
  const [historyData, setHistoryData] = useState<HistoryResponse | null>(null)
  const [classes, setClasses] = useState<Class[]>([])
  const [teachers, setTeachers] = useState<Teacher[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedEntry, setSelectedEntry] = useState<TimelineEntry | null>(null)
  
  // Filters
  const [filters, setFilters] = useState({
    class_id: searchParams.get('class_id') || '',
    month: searchParams.get('month') || '',
    year: searchParams.get('year') || '',
    teacher_id: searchParams.get('teacher_id') || '',
    student_search: searchParams.get('student_search') || '',
    page: parseInt(searchParams.get('page') || '1')
  })

  // Convert empty string to "all" for Select display
  const getSelectValue = (value: string) => value === '' ? 'all' : value

  const months = [
    'Januari', 'Februari', 'Maret', 'April', 'Mei', 'Juni',
    'Juli', 'Agustus', 'September', 'Oktober', 'November', 'Desember'
  ]

  const currentYear = new Date().getFullYear()

  // Load initial data
  useEffect(() => {
    loadClasses()
    loadTeachers()
  }, [])

  // Load history when filters change
  useEffect(() => {
    loadHistory()
  }, [filters])

  const loadClasses = async () => {
    try {
      const response = await fetch('/api/classes')
      if (response.ok) {
        const data = await response.json()
        setClasses(data)
      }
    } catch (error) {
      console.error('Error loading classes:', error)
    }
  }

  const loadTeachers = async () => {
    try {
      const response = await fetch('/api/teachers')
      if (response.ok) {
        const data = await response.json()
        setTeachers(data)
      }
    } catch (error) {
      console.error('Error loading teachers:', error)
    }
  }

  const loadHistory = async () => {
    setLoading(true)
    try {
      const params = new URLSearchParams()
      if (filters.class_id) params.append('class_id', filters.class_id)
      if (filters.month) params.append('month', filters.month)
      if (filters.year) params.append('year', filters.year)
      if (filters.teacher_id) params.append('teacher_id', filters.teacher_id)
      if (filters.student_search) params.append('student_search', filters.student_search)
      params.append('page', filters.page.toString())

      const response = await fetch(`/api/timeline/history?${params}`)
      if (response.ok) {
        const data = await response.json()
        setHistoryData(data)
      }
    } catch (error) {
      console.error('Error loading history:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleFilterChange = (key: string, value: string) => {
    // Convert "all" to empty string for API
    const apiValue = value === "all" ? "" : value
    const newFilters = { ...filters, [key]: apiValue, page: 1 }
    setFilters(newFilters)

    // Update URL
    const params = new URLSearchParams()
    Object.entries(newFilters).forEach(([k, v]) => {
      if (v) params.append(k, v.toString())
    })
    router.push(`/dashboard/timeline/history?${params}`)
  }

  const handlePageChange = (newPage: number) => {
    setFilters(prev => ({ ...prev, page: newPage }))
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('id-ID', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getInputTypeColor = (type: string) => {
    switch (type) {
      case 'manual': return 'bg-blue-100 text-blue-800'
      case 'updated': return 'bg-amber-100 text-amber-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">History Input Timeline</h1>
          <p className="text-gray-600">Riwayat input nilai timeline siswa</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={() => router.push('/dashboard/teacher/timeline-input')}>
            <FileText className="h-4 w-4 mr-2" />
            Input Timeline
          </Button>
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Export Data
          </Button>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filter Data
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="space-y-2">
              <Label>Kelas</Label>
              <Select value={getSelectValue(filters.class_id)} onValueChange={(value) => handleFilterChange('class_id', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Semua Kelas" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Semua Kelas</SelectItem>
                  {Array.isArray(classes) && classes.map((cls) => (
                    <SelectItem key={cls.id} value={cls.id}>
                      {cls.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Bulan</Label>
              <Select value={getSelectValue(filters.month)} onValueChange={(value) => handleFilterChange('month', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Semua Bulan" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Semua Bulan</SelectItem>
                  {Array.isArray(months) && months.map((month) => (
                    <SelectItem key={month} value={month}>
                      {month}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Tahun</Label>
              <Select value={getSelectValue(filters.year)} onValueChange={(value) => handleFilterChange('year', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Semua Tahun" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Semua Tahun</SelectItem>
                  <SelectItem value={currentYear.toString()}>{currentYear}</SelectItem>
                  <SelectItem value={(currentYear - 1).toString()}>{currentYear - 1}</SelectItem>
                  <SelectItem value={(currentYear + 1).toString()}>{currentYear + 1}</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Guru</Label>
              <Select value={getSelectValue(filters.teacher_id)} onValueChange={(value) => handleFilterChange('teacher_id', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Semua Guru" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Semua Guru</SelectItem>
                  {Array.isArray(teachers) && teachers.map((teacher) => (
                    <SelectItem key={teacher.id} value={teacher.id}>
                      {teacher.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="mt-4">
            <Label>Cari Siswa</Label>
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Cari nama siswa..."
                value={filters.student_search}
                onChange={(e) => handleFilterChange('student_search', e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Statistics */}
      {historyData && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <FileText className="h-8 w-8 text-blue-500" />
                <div>
                  <p className="text-2xl font-bold">{historyData.total_count}</p>
                  <p className="text-sm text-gray-600">Total Entry</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Users className="h-8 w-8 text-green-500" />
                <div>
                  <p className="text-2xl font-bold">{new Set(Array.isArray(historyData.timeline_entries) ? historyData.timeline_entries.map(e => e.student_id) : []).size}</p>
                  <p className="text-sm text-gray-600">Siswa Unik</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <GraduationCap className="h-8 w-8 text-purple-500" />
                <div>
                  <p className="text-2xl font-bold">{new Set(Array.isArray(historyData.timeline_entries) ? historyData.timeline_entries.map(e => e.teacher_id) : []).size}</p>
                  <p className="text-sm text-gray-600">Guru Aktif</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Calendar className="h-8 w-8 text-orange-500" />
                <div>
                  <p className="text-2xl font-bold">{new Set(Array.isArray(historyData.timeline_entries) ? historyData.timeline_entries.map(e => `${e.month}-${e.year}`) : []).size}</p>
                  <p className="text-sm text-gray-600">Periode Unik</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Timeline Entries */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Riwayat Input Timeline
          </CardTitle>
          <CardDescription>
            {historyData && `Menampilkan ${historyData.timeline_entries.length} dari ${historyData.total_count} entry`}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="space-y-4">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="animate-pulse">
                  <div className="h-20 bg-gray-200 rounded-lg"></div>
                </div>
              ))}
            </div>
          ) : historyData?.timeline_entries.length === 0 ? (
            <Alert>
              <AlertDescription>
                Tidak ada data timeline yang ditemukan dengan filter yang dipilih.
              </AlertDescription>
            </Alert>
          ) : (
            <div className="space-y-4">
              {Array.isArray(historyData?.timeline_entries) && historyData.timeline_entries.map((entry) => (
                <Card key={entry.id} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          <div className="flex items-center gap-2">
                            <User className="h-4 w-4 text-blue-500" />
                            <span className="font-medium">{entry.student_name}</span>
                            <Badge variant="outline" className="text-xs">
                              {entry.student_id_display}
                            </Badge>
                          </div>
                          <Separator orientation="vertical" className="h-4" />
                          <div className="flex items-center gap-2">
                            <GraduationCap className="h-4 w-4 text-green-500" />
                            <span className="text-sm">{entry.teacher_name}</span>
                          </div>
                          <Separator orientation="vertical" className="h-4" />
                          <div className="flex items-center gap-2">
                            <Users className="h-4 w-4 text-purple-500" />
                            <span className="text-sm">{entry.class_name}</span>
                          </div>
                        </div>

                        <div className="flex items-center gap-4 text-sm text-gray-600 mb-2">
                          <div className="flex items-center gap-1">
                            <Calendar className="h-4 w-4" />
                            <span>{entry.month} {entry.year}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <BookOpen className="h-4 w-4" />
                            <span>{entry.subjects_count} Mata Pelajaran</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <Activity className="h-4 w-4" />
                            <span>{entry.activities_count} Aktivitas</span>
                          </div>
                          {entry.has_behavior && (
                            <Badge variant="outline" className="text-xs">
                              Ada Penilaian Perilaku
                            </Badge>
                          )}
                        </div>

                        <div className="flex items-center gap-2 text-xs text-gray-500">
                          <Clock className="h-3 w-3" />
                          <span>Dibuat: {formatDate(entry.created_at)}</span>
                          {entry.created_at !== entry.updated_at && (
                            <>
                              <span>•</span>
                              <span>Diupdate: {formatDate(entry.updated_at)}</span>
                            </>
                          )}
                        </div>
                      </div>

                      <div className="flex items-center gap-2">
                        <Badge className={getInputTypeColor(entry.input_type)}>
                          {entry.input_type === 'manual' ? 'Input Manual' : 'Diperbarui'}
                        </Badge>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setSelectedEntry(entry)}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}

          {/* Pagination */}
          {historyData && historyData.total_pages > 1 && (
            <div className="flex items-center justify-between mt-6">
              <div className="text-sm text-gray-600">
                Halaman {historyData.page} dari {historyData.total_pages}
              </div>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(historyData.page - 1)}
                  disabled={historyData.page <= 1}
                >
                  <ChevronLeft className="h-4 w-4" />
                  Sebelumnya
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(historyData.page + 1)}
                  disabled={historyData.page >= historyData.total_pages}
                >
                  Selanjutnya
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Detail Dialog */}
      <Dialog open={!!selectedEntry} onOpenChange={() => setSelectedEntry(null)}>
        <DialogContent className="sm:max-w-[700px] max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Detail Timeline Entry</DialogTitle>
            <DialogDescription>
              {selectedEntry && `${selectedEntry.student_name} - ${selectedEntry.month} ${selectedEntry.year}`}
            </DialogDescription>
          </DialogHeader>

          {selectedEntry && (
            <div className="space-y-6">
              {/* Basic Info */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium">Siswa</Label>
                  <p className="text-sm">{selectedEntry.student_name} ({selectedEntry.student_id_display})</p>
                </div>
                <div>
                  <Label className="text-sm font-medium">Kelas</Label>
                  <p className="text-sm">{selectedEntry.class_name}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium">Guru</Label>
                  <p className="text-sm">{selectedEntry.teacher_name}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium">Periode</Label>
                  <p className="text-sm">{selectedEntry.month} {selectedEntry.year}</p>
                </div>
              </div>

              <Separator />

              {/* Subject Details */}
              {Array.isArray(selectedEntry.timeline_details) && selectedEntry.timeline_details.length > 0 && (
                <div>
                  <h4 className="font-medium mb-3">Nilai Mata Pelajaran ({selectedEntry.timeline_details.length})</h4>
                  <div className="space-y-3">
                    {selectedEntry.timeline_details.map((detail, index) => (
                      <Card key={index} className="p-3">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-2">
                              <BookOpen className="h-4 w-4 text-blue-500" />
                              <span className="font-medium">{detail.subjects?.name}</span>
                              <Badge variant="outline" className="text-xs">
                                {detail.subjects?.category}
                              </Badge>
                            </div>
                            {detail.value && (
                              <p className="text-sm text-gray-600 mb-1">
                                <strong>Pencapaian:</strong> {detail.value}
                              </p>
                            )}
                            {detail.notes && (
                              <p className="text-sm text-gray-600">
                                <strong>Catatan:</strong> {detail.notes}
                              </p>
                            )}
                          </div>
                          <Badge className="ml-2">
                            {detail.grade}
                          </Badge>
                        </div>
                      </Card>
                    ))}
                  </div>
                </div>
              )}

              {/* Activities */}
              {Array.isArray(selectedEntry.timeline_activities) && selectedEntry.timeline_activities.length > 0 && (
                <div>
                  <h4 className="font-medium mb-3">Aktivitas ({selectedEntry.timeline_activities.length})</h4>
                  <div className="space-y-2">
                    {selectedEntry.timeline_activities.map((activity, index) => (
                      <div key={index} className="flex items-center gap-2 p-2 bg-gray-50 rounded">
                        <Activity className="h-4 w-4 text-green-500" />
                        <span className="text-sm">{activity.activity}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Behavior */}
              {Array.isArray(selectedEntry.behavior_records) && selectedEntry.behavior_records.length > 0 && (
                <div>
                  <h4 className="font-medium mb-3">Penilaian Perilaku</h4>
                  {selectedEntry.behavior_records.map((behavior, index) => (
                    <Card key={index} className="p-3">
                      <div className="flex items-center gap-2 mb-2">
                        <User className="h-4 w-4 text-purple-500" />
                        <Badge>{behavior.behavior_grade}</Badge>
                      </div>
                      {behavior.notes && (
                        <p className="text-sm text-gray-600">
                          <strong>Catatan:</strong> {behavior.notes}
                        </p>
                      )}
                    </Card>
                  ))}
                </div>
              )}

              <Separator />

              {/* Metadata */}
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <Label className="text-sm font-medium">Dibuat</Label>
                  <p className="text-sm text-gray-600">{formatDate(selectedEntry.created_at)}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium">Terakhir Diupdate</Label>
                  <p className="text-sm text-gray-600">{formatDate(selectedEntry.updated_at)}</p>
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}

export default function TimelineHistoryPage() {
  return (
    <Suspense fallback={
      <div className="container mx-auto p-6">
        <div className="space-y-6">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/3 mb-2"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          </div>
          <div className="h-32 bg-gray-200 rounded"></div>
          <div className="grid grid-cols-4 gap-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-20 bg-gray-200 rounded"></div>
            ))}
          </div>
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-24 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    }>
      <TimelineHistoryContent />
    </Suspense>
  )
}
