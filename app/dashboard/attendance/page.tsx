"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { Calendar, Users, CheckCircle, XCircle, Clock, AlertTriangle, Plus, Filter, Search, Save } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Checkbox } from "@/components/ui/checkbox"

interface Student {
  id: string
  student_id: string
  name: string
  class_students: Array<{
    classes: {
      id: string
      name: string
    }
  }>
}

interface Class {
  id: string
  name: string
  level: string
}

interface AttendanceRecord {
  id?: string
  student_id: string
  class_id: string
  attendance_date: string
  status: 'present' | 'absent' | 'late' | 'excused'
  notes?: string
  student_name?: string
}

export default function AttendancePage() {
  const router = useRouter()
  const [students, setStudents] = useState<Student[]>([])
  const [classes, setClasses] = useState<Class[]>([])
  const [attendanceRecords, setAttendanceRecords] = useState<AttendanceRecord[]>([])
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)

  // Form state
  const [selectedClass, setSelectedClass] = useState('')
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0])
  const [attendanceData, setAttendanceData] = useState<Record<string, AttendanceRecord>>({})

  // Filters for viewing attendance
  const [viewFilters, setViewFilters] = useState({
    class_id: '',
    start_date: '',
    end_date: '',
    status: ''
  })

  const [showSuccess, setShowSuccess] = useState(false)
  const [successMessage, setSuccessMessage] = useState('')

  // Load initial data
  useEffect(() => {
    loadClasses()
  }, [])

  useEffect(() => {
    if (selectedClass) {
      loadStudentsInClass(selectedClass)
      loadExistingAttendance(selectedClass, selectedDate)
    }
  }, [selectedClass, selectedDate])

  useEffect(() => {
    if (viewFilters.class_id || viewFilters.start_date || viewFilters.end_date) {
      loadAttendanceHistory()
    }
  }, [viewFilters])

  const loadClasses = async () => {
    try {
      const response = await fetch('/api/classes')
      const data = await response.json()
      if (response.ok) {
        setClasses(data.classes || [])
      }
    } catch (error) {
      console.error('Error loading classes:', error)
    }
  }

  const loadStudentsInClass = async (classId: string) => {
    try {
      setLoading(true)
      const response = await fetch(`/api/students?classId=${classId}&limit=100`)
      const data = await response.json()
      if (response.ok) {
        setStudents(data.students || [])
        
        // Initialize attendance data for all students
        const initialAttendance: Record<string, AttendanceRecord> = {}
        data.students?.forEach((student: Student) => {
          initialAttendance[student.id] = {
            student_id: student.id,
            class_id: classId,
            attendance_date: selectedDate,
            status: 'present',
            notes: '',
            student_name: student.name
          }
        })
        setAttendanceData(initialAttendance)
      }
    } catch (error) {
      console.error('Error loading students:', error)
    } finally {
      setLoading(false)
    }
  }

  const loadExistingAttendance = async (classId: string, date: string) => {
    try {
      const response = await fetch(`/api/attendance?class_id=${classId}&date=${date}`)
      const data = await response.json()
      if (response.ok && data.attendance) {
        // Update attendance data with existing records
        const existingAttendance: Record<string, AttendanceRecord> = {}
        data.attendance.forEach((record: any) => {
          existingAttendance[record.student_id] = {
            id: record.id,
            student_id: record.student_id,
            class_id: record.class_id,
            attendance_date: record.attendance_date,
            status: record.status,
            notes: record.notes || '',
            student_name: record.students?.name
          }
        })
        
        setAttendanceData(prev => ({
          ...prev,
          ...existingAttendance
        }))
      }
    } catch (error) {
      console.error('Error loading existing attendance:', error)
    }
  }

  const loadAttendanceHistory = async () => {
    try {
      const params = new URLSearchParams()
      if (viewFilters.class_id) params.append('class_id', viewFilters.class_id)
      if (viewFilters.start_date) params.append('start_date', viewFilters.start_date)
      if (viewFilters.end_date) params.append('end_date', viewFilters.end_date)

      const response = await fetch(`/api/attendance?${params}`)
      const data = await response.json()
      if (response.ok) {
        let records = data.attendance || []
        
        // Filter by status if specified
        if (viewFilters.status) {
          records = records.filter((record: any) => record.status === viewFilters.status)
        }
        
        setAttendanceRecords(records)
      }
    } catch (error) {
      console.error('Error loading attendance history:', error)
    }
  }

  const updateAttendanceStatus = (studentId: string, status: AttendanceRecord['status']) => {
    setAttendanceData(prev => ({
      ...prev,
      [studentId]: {
        ...prev[studentId],
        status
      }
    }))
  }

  const updateAttendanceNotes = (studentId: string, notes: string) => {
    setAttendanceData(prev => ({
      ...prev,
      [studentId]: {
        ...prev[studentId],
        notes
      }
    }))
  }

  const handleBulkStatusUpdate = (status: AttendanceRecord['status']) => {
    const updatedData = { ...attendanceData }
    Object.keys(updatedData).forEach(studentId => {
      updatedData[studentId] = {
        ...updatedData[studentId],
        status
      }
    })
    setAttendanceData(updatedData)
  }

  const saveAttendance = async () => {
    try {
      setSaving(true)
      
      const attendanceRecords = Object.values(attendanceData)
      
      const response = await fetch('/api/attendance', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          attendance_records: attendanceRecords
        }),
      })

      const data = await response.json()

      if (response.ok) {
        setShowSuccess(true)
        setSuccessMessage(`Kehadiran berhasil disimpan! ${data.processed} record diproses.`)
        
        setTimeout(() => setShowSuccess(false), 3000)
      } else {
        alert('Error: ' + data.error)
      }
    } catch (error) {
      console.error('Error saving attendance:', error)
      alert('Terjadi kesalahan saat menyimpan kehadiran')
    } finally {
      setSaving(false)
    }
  }

  const getStatusIcon = (status: AttendanceRecord['status']) => {
    switch (status) {
      case 'present':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'absent':
        return <XCircle className="h-4 w-4 text-red-500" />
      case 'late':
        return <Clock className="h-4 w-4 text-yellow-500" />
      case 'excused':
        return <AlertTriangle className="h-4 w-4 text-blue-500" />
      default:
        return null
    }
  }

  const getStatusBadgeColor = (status: AttendanceRecord['status']) => {
    switch (status) {
      case 'present':
        return 'bg-green-100 text-green-800'
      case 'absent':
        return 'bg-red-100 text-red-800'
      case 'late':
        return 'bg-yellow-100 text-yellow-800'
      case 'excused':
        return 'bg-blue-100 text-blue-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusLabel = (status: AttendanceRecord['status']) => {
    switch (status) {
      case 'present':
        return 'Hadir'
      case 'absent':
        return 'Tidak Hadir'
      case 'late':
        return 'Terlambat'
      case 'excused':
        return 'Izin'
      default:
        return status
    }
  }

  // Calculate statistics
  const totalStudents = students.length
  const presentCount = Object.values(attendanceData).filter(record => record.status === 'present').length
  const absentCount = Object.values(attendanceData).filter(record => record.status === 'absent').length
  const lateCount = Object.values(attendanceData).filter(record => record.status === 'late').length
  const excusedCount = Object.values(attendanceData).filter(record => record.status === 'excused').length

  return (
    <div className="container mx-auto p-4 md:p-6">
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-800 mb-2">Input Kehadiran</h1>
            <p className="text-gray-600">Kelola kehadiran siswa harian</p>
          </div>
        </div>
      </div>

      {showSuccess && (
        <Alert className="mb-6 bg-emerald-50 border-emerald-200">
          <CheckCircle className="h-4 w-4 text-emerald-600" />
          <AlertDescription className="text-emerald-800">
            {successMessage}
          </AlertDescription>
        </Alert>
      )}

      {/* Input Form */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Plus className="h-5 w-5" />
            Input Kehadiran Harian
          </CardTitle>
          <CardDescription>
            Pilih kelas dan tanggal untuk input kehadiran siswa
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div>
              <Label htmlFor="class-select">Kelas</Label>
              <Select value={selectedClass} onValueChange={setSelectedClass}>
                <SelectTrigger>
                  <SelectValue placeholder="Pilih kelas" />
                </SelectTrigger>
                <SelectContent>
                  {Array.isArray(classes) && classes.map((cls) => (
                    <SelectItem key={cls.id} value={cls.id}>
                      {cls.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="date-select">Tanggal</Label>
              <Input
                id="date-select"
                type="date"
                value={selectedDate}
                onChange={(e) => setSelectedDate(e.target.value)}
              />
            </div>

            <div className="flex items-end">
              <Button
                onClick={saveAttendance}
                disabled={!selectedClass || saving || totalStudents === 0}
                className="w-full gap-2"
              >
                {saving ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    Menyimpan...
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4" />
                    Simpan Kehadiran
                  </>
                )}
              </Button>
            </div>
          </div>

          {/* Statistics */}
          {selectedClass && totalStudents > 0 && (
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
              <Card>
                <CardContent className="p-3">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-6 w-6 text-green-500" />
                    <div>
                      <p className="text-lg font-bold">{presentCount}</p>
                      <p className="text-xs text-gray-600">Hadir</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-3">
                  <div className="flex items-center gap-2">
                    <XCircle className="h-6 w-6 text-red-500" />
                    <div>
                      <p className="text-lg font-bold">{absentCount}</p>
                      <p className="text-xs text-gray-600">Tidak Hadir</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-3">
                  <div className="flex items-center gap-2">
                    <Clock className="h-6 w-6 text-yellow-500" />
                    <div>
                      <p className="text-lg font-bold">{lateCount}</p>
                      <p className="text-xs text-gray-600">Terlambat</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-3">
                  <div className="flex items-center gap-2">
                    <AlertTriangle className="h-6 w-6 text-blue-500" />
                    <div>
                      <p className="text-lg font-bold">{excusedCount}</p>
                      <p className="text-xs text-gray-600">Izin</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {/* Bulk Actions */}
          {selectedClass && totalStudents > 0 && (
            <div className="flex gap-2 mb-4">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleBulkStatusUpdate('present')}
                className="gap-1"
              >
                <CheckCircle className="h-3 w-3" />
                Semua Hadir
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleBulkStatusUpdate('absent')}
                className="gap-1"
              >
                <XCircle className="h-3 w-3" />
                Semua Tidak Hadir
              </Button>
            </div>
          )}

          {/* Students List */}
          {loading ? (
            <div className="space-y-4">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="animate-pulse">
                  <div className="h-16 bg-gray-200 rounded"></div>
                </div>
              ))}
            </div>
          ) : !selectedClass ? (
            <div className="text-center py-8">
              <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">Pilih kelas untuk mulai input kehadiran</p>
            </div>
          ) : totalStudents === 0 ? (
            <div className="text-center py-8">
              <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">Tidak ada siswa dalam kelas ini</p>
            </div>
          ) : (
            <div className="space-y-3">
              {students.map((student) => {
                const attendance = attendanceData[student.id]
                if (!attendance) return null

                return (
                  <Card key={student.id} className="hover:shadow-sm transition-shadow">
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className="flex items-center gap-2">
                            {getStatusIcon(attendance.status)}
                            <div>
                              <p className="font-medium">{student.name}</p>
                              <p className="text-sm text-gray-600">{student.student_id}</p>
                            </div>
                          </div>
                        </div>

                        <div className="flex items-center gap-3">
                          <div className="flex gap-1">
                            {(['present', 'absent', 'late', 'excused'] as const).map((status) => (
                              <Button
                                key={status}
                                variant={attendance.status === status ? "default" : "outline"}
                                size="sm"
                                onClick={() => updateAttendanceStatus(student.id, status)}
                                className="gap-1"
                              >
                                {getStatusIcon(status)}
                                <span className="hidden sm:inline">
                                  {getStatusLabel(status)}
                                </span>
                              </Button>
                            ))}
                          </div>
                        </div>
                      </div>

                      {(attendance.status === 'absent' || attendance.status === 'late' || attendance.status === 'excused') && (
                        <div className="mt-3">
                          <Input
                            placeholder="Catatan (opsional)"
                            value={attendance.notes || ''}
                            onChange={(e) => updateAttendanceNotes(student.id, e.target.value)}
                            className="text-sm"
                          />
                        </div>
                      )}
                    </CardContent>
                  </Card>
                )
              })}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
