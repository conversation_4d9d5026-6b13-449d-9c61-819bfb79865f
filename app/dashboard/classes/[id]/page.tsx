import { Suspense } from "react"
import { notFound } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { 
  ArrowLeft, 
  Edit, 
  GraduationCap, 
  Users, 
  User, 
  Calendar,
  BookOpen,
  UserCheck
} from "lucide-react"
import Link from "next/link"
import { formatDistanceToNow } from "date-fns"
import { createServerClient } from "@/utils/supabase/server"

// Function to get class data
async function getClass(classId: string) {
  try {
    const supabase = await createServerClient()

    const { data: cls, error } = await supabase
      .from('classes')
      .select(`
        id,
        class_id,
        name,
        level,
        academic_year,
        homeroom_teacher_id,
        created_at,
        updated_at
      `)
      .eq('id', classId)
      .single()

    if (error) {
      console.error('Error fetching class:', error)
      return null
    }

    return cls
  } catch (error) {
    console.error('Failed to get class:', error)
    return null
  }
}

// Function to get homeroom teacher
async function getHomeroomTeacher(teacherId: string | null) {
  if (!teacherId) return null

  try {
    const supabase = await createServerClient()

    const { data: teacher, error } = await supabase
      .from('users')
      .select(`
        id,
        name,
        email
      `)
      .eq('id', teacherId)
      .eq('role', 'teacher')
      .single()

    if (error) {
      console.error('Error fetching homeroom teacher:', error)
      return null
    }

    return teacher
  } catch (error) {
    console.error('Failed to get homeroom teacher:', error)
    return null
  }
}

// Function to get class students
async function getClassStudents(classId: string) {
  try {
    const supabase = await createServerClient()

    const { data: relations, error } = await supabase
      .from('class_students')
      .select(`
        id,
        students!inner(
          id,
          student_id,
          name,
          gender,
          birth_date,
          status
        )
      `)
      .eq('class_id', classId)

    if (error) {
      console.error('Error fetching class students:', error)
      return []
    }

    return relations || []
  } catch (error) {
    console.error('Failed to get class students:', error)
    return []
  }
}

interface ClassDetailPageProps {
  params: Promise<{ id: string }>
}

export default async function ClassDetailPage({ params }: ClassDetailPageProps) {
  const { id } = await params
  
  console.log("ClassDetailPage - Class ID:", id)

  // Fetch class data, teacher, and students
  const [cls, studentRelations] = await Promise.all([
    getClass(id),
    getClassStudents(id)
  ])

  console.log("ClassDetailPage - Class data:", cls)
  console.log("ClassDetailPage - Student relations:", studentRelations)

  if (!cls) {
    notFound()
  }

  // Fetch homeroom teacher if exists
  const homeroomTeacher = await getHomeroomTeacher(cls.homeroom_teacher_id)

  const getLevelColor = (level: string) => {
    switch (level.toLowerCase()) {
      case 'sd': return 'bg-green-100 text-green-800'
      case 'smp': return 'bg-blue-100 text-blue-800'
      case 'sma': return 'bg-purple-100 text-purple-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <div className="container mx-auto py-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" asChild>
            <Link href="/dashboard/classes">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Kembali
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Detail Kelas</h1>
            <p className="text-muted-foreground">Informasi lengkap kelas dan siswa</p>
          </div>
        </div>
        <Button asChild>
          <Link href={`/dashboard/classes/${id}/edit`}>
            <Edit className="h-4 w-4 mr-2" />
            Edit Kelas
          </Link>
        </Button>
      </div>

      <Separator className="mb-6" />

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Class Info */}
        <div className="lg:col-span-2 space-y-6">
          <Card>
            <CardHeader>
              <div className="flex items-center space-x-4">
                <div className="p-3 bg-blue-100 rounded-lg">
                  <GraduationCap className="h-8 w-8 text-blue-600" />
                </div>
                <div>
                  <CardTitle className="text-2xl">{cls.name}</CardTitle>
                  <div className="flex items-center gap-2 mt-1">
                    <Badge className={getLevelColor(cls.level)}>
                      {cls.level}
                    </Badge>
                    <Badge variant="outline">
                      ID: {cls.class_id}
                    </Badge>
                  </div>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center space-x-3">
                  <BookOpen className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <p className="text-sm font-medium">Tingkat</p>
                    <p className="text-sm text-muted-foreground">{cls.level}</p>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <Calendar className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <p className="text-sm font-medium">Tahun Ajaran</p>
                    <p className="text-sm text-muted-foreground">{cls.academic_year}</p>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <UserCheck className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <p className="text-sm font-medium">Wali Kelas</p>
                    <p className="text-sm text-muted-foreground">
                      {homeroomTeacher ? homeroomTeacher.name : "Belum ditentukan"}
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <Users className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <p className="text-sm font-medium">Jumlah Siswa</p>
                    <p className="text-sm text-muted-foreground">{studentRelations.length} siswa</p>
                  </div>
                </div>
              </div>
              
              <div className="flex items-center space-x-3">
                <Calendar className="h-5 w-5 text-muted-foreground" />
                <div>
                  <p className="text-sm font-medium">Dibuat</p>
                  <p className="text-sm text-muted-foreground">
                    {formatDistanceToNow(new Date(cls.created_at), {
                      addSuffix: true
                    })}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Students List */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Daftar Siswa ({studentRelations.length})
              </CardTitle>
            </CardHeader>
            <CardContent>
              {studentRelations.length > 0 ? (
                <div className="space-y-3">
                  {studentRelations.map((relation) => (
                    <div key={relation.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center space-x-3">
                        <Avatar className="h-10 w-10">
                          <AvatarFallback>
                            {relation.students.name.split(' ').map((n: string) => n[0]).join('').toUpperCase()}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <p className="font-medium">{relation.students.name}</p>
                          <div className="flex items-center gap-2 text-sm text-muted-foreground">
                            <span>ID: {relation.students.student_id}</span>
                            <Badge 
                              variant={relation.students.status === 'active' ? 'default' : 'secondary'}
                              className="text-xs"
                            >
                              {relation.students.status === 'active' ? 'Aktif' : 'Tidak Aktif'}
                            </Badge>
                          </div>
                        </div>
                      </div>
                      <Button variant="outline" size="sm" asChild>
                        <Link href={`/dashboard/students/${relation.students.id}`}>
                          <User className="h-4 w-4" />
                        </Link>
                      </Button>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Users className="h-12 w-12 mx-auto text-muted-foreground" />
                  <h3 className="mt-4 text-lg font-medium">Belum ada siswa</h3>
                  <p className="mt-2 text-sm text-muted-foreground">
                    Kelas ini belum memiliki siswa
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
