import { Suspense } from "react"
import { notFound } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { ArrowLeft } from "lucide-react"
import Link from "next/link"
import { ClassForm } from "@/components/classes/class-form"
import { createServerClient } from "@/utils/supabase/server"

// Function to get class data for editing
async function getClass(classId: string) {
  try {
    const supabase = await createServerClient()

    const { data: cls, error } = await supabase
      .from('classes')
      .select(`
        id,
        class_id,
        name,
        level,
        academic_year,
        homeroom_teacher_id,
        created_at,
        updated_at
      `)
      .eq('id', classId)
      .single()

    if (error) {
      console.error('Error fetching class for edit:', error)
      return null
    }

    return cls
  } catch (error) {
    console.error('Failed to get class for edit:', error)
    return null
  }
}

interface EditClassPageProps {
  params: Promise<{ id: string }>
}

export default async function EditClassPage({ params }: EditClassPageProps) {
  const { id } = await params
  
  console.log("EditClassPage - Class ID:", id)

  // Fetch class data for editing
  const cls = await getClass(id)

  console.log("EditClassPage - Class data:", cls)

  if (!cls) {
    notFound()
  }

  return (
    <div className="container mx-auto py-6">
      {/* Header */}
      <div className="flex items-center gap-4 mb-6">
        <Button variant="outline" size="sm" asChild>
          <Link href={`/dashboard/classes/${id}`}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Kembali
          </Link>
        </Button>
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Edit Kelas</h1>
          <p className="text-muted-foreground">Edit data kelas: {cls.name}</p>
        </div>
      </div>

      <Separator className="mb-6" />

      <Suspense fallback={<div>Loading...</div>}>
        <ClassForm classId={id} />
      </Suspense>
    </div>
  )
}
