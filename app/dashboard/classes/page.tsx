import { Suspense } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { PlusCircle } from "lucide-react"
import Link from "next/link"
import { ClassList } from "@/components/classes/class-list"
import { createServerClient } from "@/utils/supabase/server"

// Function to get classes data from Supabase
async function getClasses() {
  try {
    const supabase = await createServerClient()

    const { data: classes, error } = await supabase
      .from('classes')
      .select(`
        id,
        class_id,
        name,
        level,
        academic_year,
        homeroom_teacher_id,
        created_at,
        updated_at
      `)
      .order('level', { ascending: true })
      .order('name', { ascending: true })

    if (error) {
      console.error('Error fetching classes:', error)
      throw error
    }

    return classes || []
  } catch (error) {
    console.error('Failed to get classes:', error)
    return []
  }
}

// Function to get class-student counts
async function getClassStudentCounts() {
  try {
    const supabase = await createServerClient()

    const { data: relations, error } = await supabase
      .from('class_students')
      .select(`
        class_id,
        students!inner(id, name)
      `)

    if (error) {
      console.error('Error fetching class-student relations:', error)
      return {}
    }

    // Count students per class
    const counts: Record<string, number> = {}
    relations?.forEach((relation) => {
      counts[relation.class_id] = (counts[relation.class_id] || 0) + 1
    })

    return counts
  } catch (error) {
    console.error('Failed to get class-student counts:', error)
    return {}
  }
}

// Function to get teachers data for homeroom teacher names
async function getTeachers() {
  try {
    const supabase = await createServerClient()

    const { data: teachers, error } = await supabase
      .from('users')
      .select(`
        id,
        name,
        email
      `)
      .eq('role', 'teacher')

    if (error) {
      console.error('Error fetching teachers:', error)
      return []
    }

    return teachers || []
  } catch (error) {
    console.error('Failed to get teachers:', error)
    return []
  }
}

export default async function ClassesPage() {
  // Pre-fetch classes data from the server for initial render
  const [classes, studentCounts, teachers] = await Promise.all([
    getClasses(),
    getClassStudentCounts(),
    getTeachers()
  ])

  // Create teacher lookup map
  const teacherMap = teachers.reduce((acc, teacher) => {
    acc[teacher.id] = teacher
    return acc
  }, {} as Record<string, any>)

  // Add student count and teacher info to each class
  const classesWithCounts = classes.map(cls => ({
    ...cls,
    studentCount: studentCounts[cls.id] || 0,
    homeroomTeacher: cls.homeroom_teacher_id ? teacherMap[cls.homeroom_teacher_id] : null
  }))

  return (
    <div className="container mx-auto py-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Data Kelas</h1>
          <p className="text-muted-foreground">Kelola data kelas dan wali kelas</p>
        </div>
        <Button asChild>
          <Link href="/dashboard/classes/new">
            <PlusCircle className="mr-2 h-4 w-4" />
            Tambah Kelas
          </Link>
        </Button>
      </div>
      <Separator className="my-6" />
      <Suspense fallback={<div>Loading...</div>}>
        <ClassList initialClasses={classesWithCounts} />
      </Suspense>
    </div>
  )
}
