import { Suspense } from "react"
import { Plus, FileSpreadsheet } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { StudentList } from "@/components/students/student-list"
import { StudentListSkeleton } from "@/components/students/student-list-skeleton"

export default function StudentsPage() {
  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Manajemen Siswa</h1>
          <p className="text-muted-foreground">Kelola data siswa dan hubungan dengan orangtua</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" asChild>
            <a href="/dashboard/students/import">
              <FileSpreadsheet className="mr-2 h-4 w-4" />
              Import Excel
            </a>
          </Button>
          <Button asChild>
            <a href="/dashboard/students/new">
              <Plus className="mr-2 h-4 w-4" />
              Tambah Siswa
            </a>
          </Button>
        </div>
      </div>

      <div className="border rounded-lg">
        <Suspense fallback={<StudentListSkeleton />}>
          <StudentList />
        </Suspense>
      </div>
    </div>
  )
}
