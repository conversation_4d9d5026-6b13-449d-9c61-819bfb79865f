import { notFound } from "next/navigation"
import { AddParentForm } from "@/components/students/add-parent-form"
import { createServerClient } from "@/utils/supabase/server"

// Real function to get student data from Supabase
async function getStudent(id: string) {
  try {
    const supabase = await createServerClient()

    const { data: student, error } = await supabase
      .from('students')
      .select(`
        id,
        student_id,
        name,
        gender,
        birth_date,
        address,
        photo_url,
        batch,
        status,
        user_id
      `)
      .eq('id', id)
      .single()

    if (error) {
      console.error('Error fetching student:', error)
      throw error
    }

    return student
  } catch (error) {
    console.error('Failed to get student:', error)
    throw error
  }
}

export default async function AddParentPage({
  params,
}: {
  params: Promise<{ id: string }>
}) {
  try {
    // Await params for Next.js 15 compatibility
    const { id } = await params

    console.log('AddParentPage - Student ID:', id)

    // Get student data from Supabase
    const student = await getStudent(id)

    console.log('AddParentPage - Student data:', student)

    return (
      <div className="container mx-auto py-6 space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Tambah Orangtua</h1>
          <p className="text-muted-foreground">Tambahkan orangtua/wali untuk siswa {student.name}</p>
        </div>

        <AddParentForm studentId={id} studentName={student.name} />
      </div>
    )
  } catch (error) {
    console.error('Error in AddParentPage:', error)
    console.error('Error details:', {
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    })
    notFound()
  }
}
