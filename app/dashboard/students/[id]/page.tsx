import { Suspense } from "react"
import { notFound } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { StudentDetail } from "@/components/students/student-detail"
import { StudentDetailSkeleton } from "@/components/students/student-detail-skeleton"
import { createServerClient } from "@/utils/supabase/server"

// Real function to get student data from Supabase
async function getStudent(id: string) {
  try {
    const supabase = await createServerClient()

    const { data: student, error } = await supabase
      .from('students')
      .select(`
        id,
        student_id,
        name,
        gender,
        birth_date,
        address,
        photo_url,
        batch,
        status,
        user_id,
        created_at,
        updated_at
      `)
      .eq('id', id)
      .single()

    if (error) {
      console.error('Error fetching student:', error)
      throw error
    }

    return student
  } catch (error) {
    console.error('Failed to get student:', error)
    throw error
  }
}

export default async function StudentDetailPage({
  params,
}: {
  params: Promise<{ id: string }>
}) {
  try {
    // Await params for Next.js 15 compatibility
    const { id } = await params

    console.log('StudentDetailPage - Student ID:', id)

    // Get student data from Supabase
    const student = await getStudent(id)

    console.log('StudentDetailPage - Student data:', student)

    return (
      <div className="container mx-auto py-6 space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Detail Siswa</h1>
            <p className="text-muted-foreground">Informasi lengkap siswa dan data orangtua</p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" asChild>
              <a href={`/dashboard/students/${id}/edit`}>Edit Siswa</a>
            </Button>
            <Button asChild>
              <a href="/dashboard/students">Kembali</a>
            </Button>
          </div>
        </div>

        <Suspense fallback={<StudentDetailSkeleton />}>
          <StudentDetail id={id} />
        </Suspense>
      </div>
    )
  } catch (error) {
    console.error('Error in StudentDetailPage:', error)
    console.error('Error details:', {
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    })
    notFound()
  }
}
