"use client"

import { useState, useCallback } from "react"
import { useRouter } from "next/navigation"
import { useDropzone } from "react-dropzone"
import { Upload, Download, FileSpreadsheet, AlertCircle, CheckCircle, X, Loader2, Users, ArrowLeft } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"

interface ImportResult {
  student_id: string
  student_name: string
  parents_count: number
  class_assigned: boolean
  status: string
}

interface ImportResponse {
  message: string
  results: ImportResult[]
  errors: string[]
  summary: {
    total_rows: number
    successful: number
    failed: number
    students_created: number
    parents_created: number
  }
}

export default function StudentsImportPage() {
  const router = useRouter()
  const [isUploading, setIsUploading] = useState(false)
  const [isDownloadingTemplate, setIsDownloadingTemplate] = useState(false)
  const [importResult, setImportResult] = useState<ImportResponse | null>(null)
  const [uploadedFile, setUploadedFile] = useState<File | null>(null)

  // Download template
  const handleDownloadTemplate = async () => {
    setIsDownloadingTemplate(true)
    try {
      const response = await fetch('/api/students/template')
      if (response.ok) {
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `Template_Import_Santri_${new Date().toISOString().split('T')[0]}.xlsx`
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)
      } else {
        alert('Gagal mengunduh template')
      }
    } catch (error) {
      console.error('Error downloading template:', error)
      alert('Terjadi kesalahan saat mengunduh template')
    } finally {
      setIsDownloadingTemplate(false)
    }
  }

  // Handle file drop
  const onDrop = useCallback((acceptedFiles: File[]) => {
    const file = acceptedFiles[0]
    if (file) {
      setUploadedFile(file)
      setImportResult(null)
    }
  }, [])

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
      'application/vnd.ms-excel': ['.xls']
    },
    maxFiles: 1
  })

  // Handle import
  const handleImport = async () => {
    if (!uploadedFile) return

    setIsUploading(true)
    try {
      const formData = new FormData()
      formData.append('file', uploadedFile)

      const response = await fetch('/api/students/import', {
        method: 'POST',
        body: formData
      })

      const result = await response.json()
      
      if (response.ok) {
        setImportResult(result)
      } else {
        alert(`Gagal import: ${result.error}`)
      }
    } catch (error) {
      console.error('Error importing file:', error)
      alert('Terjadi kesalahan saat import file')
    } finally {
      setIsUploading(false)
    }
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button variant="outline" onClick={() => router.back()}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Kembali
        </Button>
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Import Data Santri</h1>
          <p className="text-gray-600">Import data santri beserta orang tua dari file Excel</p>
        </div>
      </div>

      {/* Overview */}
      <Alert>
        <Users className="h-4 w-4" />
        <AlertDescription>
          <strong>Fitur Import Santri:</strong> Upload file Excel untuk menambahkan multiple santri beserta data orang tua sekaligus. 
          Sistem akan otomatis membuat akun orang tua dan menghubungkannya dengan santri.
        </AlertDescription>
      </Alert>

      {/* Step 1: Download Template */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <span className="bg-emerald-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm">1</span>
            Download Template Excel
          </CardTitle>
          <CardDescription>
            Download template Excel yang sudah berisi format dan contoh data yang benar
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Button 
            onClick={handleDownloadTemplate}
            disabled={isDownloadingTemplate}
            className="w-full gap-2"
            size="lg"
          >
            {isDownloadingTemplate ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Download className="h-4 w-4" />
            )}
            Download Template Excel
          </Button>
          
          <div className="mt-4 p-4 bg-blue-50 rounded-lg">
            <h4 className="font-medium text-blue-900 mb-2">Template berisi:</h4>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• Format data santri (nama, tanggal lahir, alamat, dll)</li>
              <li>• Format data orang tua (ayah, ibu, wali)</li>
              <li>• Contoh pengisian yang benar</li>
              <li>• Daftar kelas yang tersedia</li>
              <li>• Petunjuk penggunaan lengkap</li>
            </ul>
          </div>
        </CardContent>
      </Card>

      {/* Step 2: Upload File */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <span className="bg-emerald-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm">2</span>
            Upload File Excel
          </CardTitle>
          <CardDescription>
            Upload file Excel yang sudah diisi dengan data santri dan orang tua
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div
            {...getRootProps()}
            className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${
              isDragActive 
                ? 'border-emerald-500 bg-emerald-50' 
                : 'border-gray-300 hover:border-emerald-400'
            }`}
          >
            <input {...getInputProps()} />
            <Upload className="h-12 w-12 mx-auto mb-4 text-gray-400" />
            {uploadedFile ? (
              <div>
                <p className="text-lg font-medium text-emerald-600">{uploadedFile.name}</p>
                <p className="text-sm text-gray-500">
                  {(uploadedFile.size / 1024 / 1024).toFixed(2)} MB
                </p>
              </div>
            ) : (
              <div>
                <p className="text-lg text-gray-600 mb-2">
                  {isDragActive 
                    ? 'Drop file Excel di sini...' 
                    : 'Drag & drop file Excel atau klik untuk pilih file'
                  }
                </p>
                <p className="text-sm text-gray-400">
                  Mendukung format .xlsx dan .xls
                </p>
              </div>
            )}
          </div>

          {uploadedFile && (
            <div className="mt-6 flex gap-3">
              <Button 
                onClick={handleImport}
                disabled={isUploading}
                className="flex-1 gap-2"
                size="lg"
              >
                {isUploading ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <FileSpreadsheet className="h-4 w-4" />
                )}
                {isUploading ? 'Mengimport Data...' : 'Import Data Santri'}
              </Button>
              <Button 
                variant="outline" 
                onClick={() => setUploadedFile(null)}
                disabled={isUploading}
                size="lg"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Step 3: Import Results */}
      {importResult && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <span className="bg-emerald-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm">3</span>
              Hasil Import
              {importResult.summary.failed === 0 ? (
                <CheckCircle className="h-5 w-5 text-green-500" />
              ) : (
                <AlertCircle className="h-5 w-5 text-amber-500" />
              )}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Summary Statistics */}
            <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
              <div className="text-center p-4 bg-blue-50 rounded-lg">
                <div className="text-2xl font-bold text-blue-600">{importResult.summary.total_rows}</div>
                <div className="text-sm text-blue-600">Total Baris</div>
              </div>
              <div className="text-center p-4 bg-green-50 rounded-lg">
                <div className="text-2xl font-bold text-green-600">{importResult.summary.successful}</div>
                <div className="text-sm text-green-600">Berhasil</div>
              </div>
              <div className="text-center p-4 bg-red-50 rounded-lg">
                <div className="text-2xl font-bold text-red-600">{importResult.summary.failed}</div>
                <div className="text-sm text-red-600">Gagal</div>
              </div>
              <div className="text-center p-4 bg-purple-50 rounded-lg">
                <div className="text-2xl font-bold text-purple-600">{importResult.summary.students_created}</div>
                <div className="text-sm text-purple-600">Santri Dibuat</div>
              </div>
              <div className="text-center p-4 bg-orange-50 rounded-lg">
                <div className="text-2xl font-bold text-orange-600">{importResult.summary.parents_created}</div>
                <div className="text-sm text-orange-600">Orang Tua Dibuat</div>
              </div>
            </div>

            {/* Success Message */}
            {importResult.summary.successful > 0 && (
              <Alert>
                <CheckCircle className="h-4 w-4" />
                <AlertDescription>
                  <strong>Import berhasil!</strong> {importResult.summary.students_created} santri dan {importResult.summary.parents_created} orang tua berhasil ditambahkan ke sistem.
                </AlertDescription>
              </Alert>
            )}

            {/* Action Buttons */}
            {importResult.summary.successful > 0 && (
              <div className="flex gap-3">
                <Button onClick={() => router.push('/dashboard/students')} className="gap-2">
                  <Users className="h-4 w-4" />
                  Lihat Data Santri
                </Button>
                <Button variant="outline" onClick={() => router.push('/dashboard/parents')} className="gap-2">
                  <Users className="h-4 w-4" />
                  Lihat Data Orang Tua
                </Button>
              </div>
            )}

            {/* Errors */}
            {importResult.errors.length > 0 && (
              <div>
                <h4 className="text-lg font-medium text-red-600 mb-3">Error yang Ditemukan:</h4>
                <ScrollArea className="h-48 w-full border rounded-lg p-4 bg-red-50">
                  <div className="space-y-2">
                    {importResult.errors.map((error, index) => (
                      <div key={index} className="text-sm text-red-700 p-2 bg-red-100 rounded">
                        {error}
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              </div>
            )}

            {/* Successful imports */}
            {importResult.results.length > 0 && (
              <div>
                <h4 className="text-lg font-medium text-green-600 mb-3">
                  Data yang Berhasil Diimport ({importResult.results.length}):
                </h4>
                <ScrollArea className="h-48 w-full border rounded-lg p-4 bg-green-50">
                  <div className="space-y-2">
                    {importResult.results.map((result, index) => (
                      <div key={index} className="flex items-center justify-between p-2 bg-green-100 rounded">
                        <span className="text-sm text-green-700 font-medium">
                          ✓ {result.student_name}
                        </span>
                        <div className="flex gap-2">
                          <Badge variant="outline" className="text-xs">
                            {result.parents_count} Orang Tua
                          </Badge>
                          {result.class_assigned && (
                            <Badge variant="outline" className="text-xs text-blue-600">
                              Kelas Assigned
                            </Badge>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Important Notes */}
      <Alert>
        <AlertCircle className="h-4 w-4" />
        <AlertDescription className="text-sm">
          <strong>Catatan Penting:</strong>
          <ul className="mt-2 space-y-1 list-disc list-inside">
            <li>Pastikan format tanggal konsisten (YYYY-MM-DD atau DD/MM/YYYY)</li>
            <li>Nama santri adalah field wajib yang harus diisi</li>
            <li>Minimal isi data satu orang tua (Ayah atau Ibu)</li>
            <li>Data Wali opsional, hanya jika berbeda dari orang tua</li>
            <li>Sistem akan membuat akun orang tua secara otomatis</li>
            <li>Backup data sebelum melakukan import massal</li>
          </ul>
        </AlertDescription>
      </Alert>
    </div>
  )
}
