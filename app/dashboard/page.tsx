// Server Component - Dashboard
import { Suspense } from "react"
import { Skeleton } from "@/components/ui/skeleton"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"

import {
  Users,
  GraduationCap,
  BookOpen,
  TrendingUp,
  Calendar,
  Award,
  UserPlus,
  FileText,
  Target,
  Star
} from "lucide-react"
import Link from "next/link"
import { getStudents, getClasses } from "@/lib/supabase-crud"
import StudentListClient from "@/components/dashboard/student-list-client"
import { RealTimeDashboard } from "@/components/dashboard/real-time-dashboard"

// Helper function to get dashboard statistics from API
async function getDashboardStats() {
  try {
    // Get students and classes for the student list first
    const [students, classes] = await Promise.all([
      getStudents(),
      getClasses()
    ])

    // Try to fetch dashboard statistics from API
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'}/api/dashboard/stats`, {
        cache: 'no-store' // Always get fresh data
      })

      if (response.ok) {
        const data = await response.json()
        return {
          ...data.statistics,
          classDistribution: data.classDistribution,
          recentActivities: data.recentActivities,
          performanceMetrics: data.performanceMetrics,
          monthlyProgress: data.monthlyProgress,
          students,
          classes
        }
      }
    } catch (apiError) {
      console.error('API fetch failed, using fallback data:', apiError)
    }

    // Use fallback data with real students and classes
    return {
      totalStudents: students.length,
      totalClasses: classes.length,
      activeStudents: students.filter(s => s.status === 'active').length,
      graduatedStudents: students.filter(s => s.status === 'graduated').length,
      classDistribution: classes.map(cls => ({
        name: cls.name,
        studentCount: students.filter(s => s.batch === cls.name).length,
        capacity: 30, // Default capacity since we don't have this field in database
        percentage: Math.round((students.filter(s => s.batch === cls.name).length / 30) * 100)
      })),
      recentActivities: [
        {
          type: 'student_added',
          message: `Data santri berhasil dimuat dari database`,
          time: 'Baru saja',
          user: 'Sistem'
        }
      ],
      performanceMetrics: [
        {
          subject: 'Al-Quran',
          average: 85,
          target: 90,
          students: students.length
        },
        {
          subject: 'Hadits',
          average: 80,
          target: 85,
          students: students.length
        }
      ],
      monthlyProgress: {
        hafalanBaru: '+2 Juz',
        pencapaianSelesai: '+5 Target',
        santriAktif: students.filter(s => s.status === 'active').length,
        tingkatKehadiran: '92%'
      },
      students,
      classes
    }
  } catch (error) {
    console.error('Error fetching dashboard stats:', error)

    // Fallback to basic data if API fails
    try {
      const [students, classes] = await Promise.all([
        getStudents(),
        getClasses()
      ])

      return {
        totalStudents: students.length,
        totalClasses: classes.length,
        activeStudents: students.filter(s => s.status === 'active').length,
        graduatedStudents: students.filter(s => s.status === 'graduated').length,
        classDistribution: classes.map(cls => ({
          name: cls.name,
          studentCount: students.filter(s => s.batch === cls.name).length,
          capacity: 30, // Default capacity since we don't have this field in database
          percentage: 0
        })),
        recentActivities: [],
        performanceMetrics: [],
        monthlyProgress: {
          hafalanBaru: '+0 Juz',
          pencapaianSelesai: '+0 Target',
          santriAktif: students.filter(s => s.status === 'active').length,
          tingkatKehadiran: '0%'
        },
        students,
        classes
      }
    } catch (fallbackError) {
      console.error('Error in fallback data fetch:', fallbackError)
      return {
        totalStudents: 0,
        totalClasses: 0,
        activeStudents: 0,
        graduatedStudents: 0,
        classDistribution: [],
        recentActivities: [],
        performanceMetrics: [],
        monthlyProgress: {
          hafalanBaru: '+0 Juz',
          pencapaianSelesai: '+0 Target',
          santriAktif: 0,
          tingkatKehadiran: '0%'
        },
        students: [],
        classes: []
      }
    }
  }
}

export default async function DashboardPage() {
  // Fetch dashboard data
  const stats = await getDashboardStats()

  // Get unique batches for filtering
  const batches = stats.classes.map((c: any) => c.name)
  
  return (
    <div className="container mx-auto p-4 md:p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-800">Dashboard</h1>
          <p className="text-gray-600">Selamat datang di sistem manajemen PTQ Al Ihsan</p>
        </div>
        <div className="flex gap-2">
          <Button asChild>
            <Link href="/dashboard/students/new">
              <UserPlus className="h-4 w-4 mr-2" />
              Tambah Santri
            </Link>
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Total Santri"
          value={stats.totalStudents}
          icon={<Users className="h-8 w-8" />}
          description={`${stats.activeStudents} aktif`}
          trend="+12%"
          trendUp={true}
        />
        <StatCard
          title="Total Kelas"
          value={stats.totalClasses}
          icon={<GraduationCap className="h-8 w-8" />}
          description="Kelas aktif"
          trend="+2"
          trendUp={true}
        />
        <StatCard
          title="Santri Lulus"
          value={stats.graduatedStudents}
          icon={<Award className="h-8 w-8" />}
          description="Tahun ini"
          trend="+8%"
          trendUp={true}
        />
        <StatCard
          title="Pencapaian"
          value="87%"
          icon={<Target className="h-8 w-8" />}
          description="Rata-rata"
          trend="+5%"
          trendUp={true}
        />
      </div>

      {/* Real-time Dashboard */}
      <RealTimeDashboard
        initialData={{
          statistics: {
            totalStudents: stats.totalStudents,
            totalClasses: stats.totalClasses,
            activeStudents: stats.activeStudents,
            graduatedStudents: stats.graduatedStudents
          },
          classDistribution: stats.classDistribution,
          performanceMetrics: stats.performanceMetrics,
          recentActivities: stats.recentActivities,
          monthlyProgress: stats.monthlyProgress
        }}
      />

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Star className="h-5 w-5" />
            Aksi Cepat
          </CardTitle>
          <CardDescription>
            Akses cepat ke fitur yang sering digunakan
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
            <QuickActionCard
              href="/dashboard/students"
              icon={<Users className="h-6 w-6" />}
              title="Kelola Santri"
              description="Lihat dan kelola data santri"
            />
            <QuickActionCard
              href="/dashboard/classes"
              icon={<GraduationCap className="h-6 w-6" />}
              title="Kelola Kelas"
              description="Atur kelas dan kapasitas"
            />
            <QuickActionCard
              href="/dashboard/teacher/timeline-input"
              icon={<BookOpen className="h-6 w-6" />}
              title="Input Nilai"
              description="Input nilai santri"
            />
            <QuickActionCard
              href="/dashboard/achievements"
              icon={<Award className="h-6 w-6" />}
              title="Pencapaian"
              description="Lihat pencapaian santri"
            />
            <QuickActionCard
              href="/dashboard/attendance"
              icon={<Calendar className="h-6 w-6" />}
              title="Absensi"
              description="Kelola absensi santri"
            />
            <QuickActionCard
              href="/dashboard/settings"
              icon={<FileText className="h-6 w-6" />}
              title="Pengaturan"
              description="Kelola website"
            />
          </div>
        </CardContent>
      </Card>



      {/* Student List */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Daftar Santri
          </CardTitle>
          <CardDescription>
            Pilih santri untuk melihat detail pencapaian
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Suspense fallback={<StudentListSkeleton />}>
            <StudentListClient students={stats.students} batches={batches} />
          </Suspense>
        </CardContent>
      </Card>
    </div>
  )
}

// StatCard Component
interface StatCardProps {
  title: string
  value: string | number
  icon: React.ReactNode
  description: string
  trend: string
  trendUp: boolean
}

function StatCard({ title, value, icon, description, trend, trendUp }: StatCardProps) {
  return (
    <Card>
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className="p-2 bg-emerald-100 rounded-lg text-emerald-600">
              {icon}
            </div>
            <div>
              <p className="text-sm font-medium text-gray-600">{title}</p>
              <p className="text-2xl font-bold text-gray-900">{value}</p>
            </div>
          </div>
          <div className="text-right">
            <div className={`flex items-center gap-1 text-sm ${trendUp ? 'text-green-600' : 'text-red-600'}`}>
              <TrendingUp className={`h-4 w-4 ${trendUp ? '' : 'rotate-180'}`} />
              {trend}
            </div>
            <p className="text-xs text-gray-500">{description}</p>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

// QuickActionCard Component
interface QuickActionCardProps {
  href: string
  icon: React.ReactNode
  title: string
  description: string
}

function QuickActionCard({ href, icon, title, description }: QuickActionCardProps) {
  return (
    <Link href={href}>
      <Card className="hover:shadow-md transition-shadow cursor-pointer h-full">
        <CardContent className="p-4 text-center">
          <div className="flex flex-col items-center gap-2">
            <div className="p-2 bg-emerald-100 rounded-lg text-emerald-600">
              {icon}
            </div>
            <div>
              <h3 className="font-medium text-sm">{title}</h3>
              <p className="text-xs text-gray-500 mt-1">{description}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </Link>
  )
}

function StudentListSkeleton() {
  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <Skeleton className="h-10 w-[200px]" />
        <div className="flex gap-2">
          <Skeleton className="h-10 w-[200px]" />
          <Skeleton className="h-10 w-32" />
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-6">
        {[...Array(6)].map((_, i) => (
          <Skeleton key={i} className="h-[200px] w-full rounded-lg" />
        ))}
      </div>
    </div>
  )
}
