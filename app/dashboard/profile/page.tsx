// Server Component
import { Suspense } from "react"
import { redirect } from "next/navigation"
import { createServerClient } from "@/utils/supabase/server"
import { Skeleton } from "@/components/ui/skeleton"
import ParentProfile from "@/components/dashboard/parent-profile"

// Loading fallback
function ProfileSkeleton() {
  return (
    <div className="space-y-4">
      <Skeleton className="h-8 w-48 mb-4" />
      <Skeleton className="h-[300px] w-full" />
    </div>
  )
}

export default async function ProfilePage() {
  // Authentication is handled by middleware
  const supabase = await createServerClient()
  const { data: { session } } = await supabase.auth.getSession()
  
  if (!session) {
    redirect('/login')
  }
  
  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-6">My Profile</h1>
      <Suspense fallback={<ProfileSkeleton />}>
        <div className="max-w-2xl">
          <ParentProfile userId={session.user.id} />
        </div>
      </Suspense>
    </div>
  )
} 