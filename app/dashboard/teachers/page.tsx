import { Suspense } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { PlusCircle } from "lucide-react"
import Link from "next/link"
import { TeacherList } from "@/components/teachers/teacher-list"
import { createServerClient } from "@/utils/supabase/server"

// Function to get teachers data from Supabase
async function getTeachers() {
  try {
    const supabase = await createServerClient()

    const { data: teachers, error } = await supabase
      .from('teachers')
      .select(`
        id,
        teacher_id,
        name,
        specialization,
        photo_url,
        join_date,
        status,
        created_at,
        updated_at
      `)
      .eq('status', 'active')
      .order('name', { ascending: true })

    if (error) {
      console.error('Error fetching teachers:', error)
      throw error
    }

    return teachers || []
  } catch (error) {
    console.error('Failed to get teachers:', error)
    return []
  }
}

// Function to get teacher-class relationships
async function getTeacherClassCounts() {
  try {
    const supabase = await createServerClient()

    const { data: classes, error } = await supabase
      .from('classes')
      .select(`
        homeroom_teacher_id
      `)
      .not('homeroom_teacher_id', 'is', null)

    if (error) {
      console.error('Error fetching teacher-class relations:', error)
      return {}
    }

    // Count classes per teacher
    const counts: Record<string, number> = {}
    classes?.forEach((cls) => {
      if (cls.homeroom_teacher_id) {
        counts[cls.homeroom_teacher_id] = (counts[cls.homeroom_teacher_id] || 0) + 1
      }
    })

    return counts
  } catch (error) {
    console.error('Failed to get teacher-class counts:', error)
    return {}
  }
}

export default async function TeachersPage() {
  // Pre-fetch teachers data from the server for initial render
  const [teachers, classCounts] = await Promise.all([
    getTeachers(),
    getTeacherClassCounts()
  ])

  // Add class count to each teacher
  const teachersWithCounts = teachers.map(teacher => ({
    ...teacher,
    classCount: classCounts[teacher.id] || 0
  }))

  return (
    <div className="container mx-auto py-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Data Guru</h1>
          <p className="text-muted-foreground">Kelola data guru dan wali kelas</p>
        </div>
        <Button asChild>
          <Link href="/dashboard/teachers/new">
            <PlusCircle className="mr-2 h-4 w-4" />
            Tambah Guru
          </Link>
        </Button>
      </div>
      <Separator className="my-6" />
      <Suspense fallback={<div>Loading...</div>}>
        <TeacherList initialTeachers={teachersWithCounts} />
      </Suspense>
    </div>
  )
}
