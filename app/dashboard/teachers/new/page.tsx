import { Suspense } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { ArrowLeft } from "lucide-react"
import Link from "next/link"
import { TeacherForm } from "@/components/teachers/teacher-form"

export default function NewTeacherPage() {
  return (
    <div className="container mx-auto py-6">
      {/* Header */}
      <div className="flex items-center gap-4 mb-6">
        <Button variant="outline" size="sm" asChild>
          <Link href="/dashboard/teachers">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Kembali
          </Link>
        </Button>
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Tambah Guru Baru</h1>
          <p className="text-muted-foreground">Tambahkan data guru baru ke sistem</p>
        </div>
      </div>

      <Separator className="mb-6" />

      <div className="max-w-2xl">
        <Suspense fallback={<div>Loading...</div>}>
          <TeacherForm mode="create" />
        </Suspense>
      </div>
    </div>
  )
}
