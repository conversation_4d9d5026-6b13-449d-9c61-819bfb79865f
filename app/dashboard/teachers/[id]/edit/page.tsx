import { Suspense } from "react"
import { notFound } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { ArrowLeft } from "lucide-react"
import Link from "next/link"
import { TeacherForm } from "@/components/teachers/teacher-form"
import { createServerClient } from "@/utils/supabase/server"

// Function to get teacher data
async function getTeacher(teacherId: string) {
  try {
    const supabase = await createServerClient()

    const { data: teacher, error } = await supabase
      .from('teachers')
      .select(`
        id,
        teacher_id,
        name,
        specialization,
        photo_url,
        join_date,
        status,
        created_at,
        updated_at
      `)
      .eq('id', teacherId)
      .single()

    if (error) {
      console.error('Error fetching teacher:', error)
      return null
    }

    return teacher
  } catch (error) {
    console.error('Failed to get teacher:', error)
    return null
  }
}

interface EditTeacherPageProps {
  params: Promise<{ id: string }>
}

export default async function EditTeacherPage({ params }: EditTeacherPageProps) {
  const { id } = await params
  
  console.log("EditTeacherPage - Teacher ID:", id)

  // Fetch teacher data
  const teacher = await getTeacher(id)

  console.log("EditTeacherPage - Teacher data:", teacher)

  if (!teacher) {
    notFound()
  }

  return (
    <div className="container mx-auto py-6">
      {/* Header */}
      <div className="flex items-center gap-4 mb-6">
        <Button variant="outline" size="sm" asChild>
          <Link href={`/dashboard/teachers/${id}`}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Kembali
          </Link>
        </Button>
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Edit Data Guru</h1>
          <p className="text-muted-foreground">Perbarui informasi guru</p>
        </div>
      </div>

      <Separator className="mb-6" />

      <div className="max-w-2xl">
        <Suspense fallback={<div>Loading...</div>}>
          <TeacherForm 
            mode="edit" 
            teacherId={id}
            initialData={teacher}
          />
        </Suspense>
      </div>
    </div>
  )
}
