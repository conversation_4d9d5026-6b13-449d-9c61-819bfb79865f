import { Suspense } from "react"
import { BlogForm } from "@/components/blog/blog-form"

interface EditBlogPageProps {
  params: {
    id: string
  }
}

export default function EditBlogPage({ params }: EditBlogPageProps) {
  return (
    <div className="container mx-auto py-6">
      <Suspense fallback={<div>Loading...</div>}>
        <BlogForm mode="edit" postId={params.id} />
      </Suspense>
    </div>
  )
}
