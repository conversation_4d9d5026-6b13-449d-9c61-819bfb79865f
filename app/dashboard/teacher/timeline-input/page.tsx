"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { Save, Calendar, Users, Plus, Trash2, CheckCircle, AlertCircle, Loader2, History } from "lucide-react"
import ExcelImport from "@/components/timeline/excel-import"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  Dialog,
  DialogContent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/dialog"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"

// Types
interface Class {
  id: string
  name: string
  class_id: string
  level: string
  academic_year: string
}

interface Subject {
  id: string
  name: string
  category: string
  description?: string
}

interface Student {
  id: string
  student_id: string
  name: string
  photo_url?: string
  timeline_entry?: any
  behavior_record?: any
  attendance_percentage: number
}

// Grade options (removed - now using manual input)

// Behavior options
const behaviorOptions = ["Sangat Baik", "Baik", "Cukup", "Perlu Bimbingan"]

// Month options
const months = [
  "Januari",
  "Februari",
  "Maret",
  "April",
  "Mei",
  "Juni",
  "Juli",
  "Agustus",
  "September",
  "Oktober",
  "November",
  "Desember",
]

// Current year
const currentYear = new Date().getFullYear()

export default function TimelineInputPage() {
  const router = useRouter()
  const [selectedClass, setSelectedClass] = useState("")
  const [selectedMonth, setSelectedMonth] = useState("")
  const [selectedYear, setSelectedYear] = useState(currentYear.toString())
  const [classes, setClasses] = useState<Class[]>([])
  const [subjects, setSubjects] = useState<Subject[]>([])
  const [students, setStudents] = useState<Student[]>([])
  const [timelineData, setTimelineData] = useState<Record<string, any>>({})
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [showSuccess, setShowSuccess] = useState(false)
  const [loading, setLoading] = useState(true)
  const [loadingStudents, setLoadingStudents] = useState(false)
  const [bulkData, setBulkData] = useState<Record<string, any>>({})
  const [showBulkDialog, setShowBulkDialog] = useState(false)

  // Handle activity changes
  const handleActivityChange = (studentId: string, index: number, value: string) => {
    setTimelineData((prev) => ({
      ...prev,
      [studentId]: {
        ...prev[studentId],
        activities: prev[studentId]?.activities.map((activity: string, i: number) =>
          i === index ? value : activity
        ) || [],
      },
    }))
  }

  const handleAddActivity = (studentId: string) => {
    setTimelineData((prev) => ({
      ...prev,
      [studentId]: {
        ...prev[studentId],
        activities: [...(prev[studentId]?.activities || []), ""],
      },
    }))
  }

  const handleRemoveActivity = (studentId: string, index: number) => {
    setTimelineData((prev) => ({
      ...prev,
      [studentId]: {
        ...prev[studentId],
        activities: prev[studentId]?.activities.filter((_: any, i: number) => i !== index) || [],
      },
    }))
  }

  // Form validation
  const isFormValid = () => {
    return selectedClass && selectedMonth && selectedYear && students.length > 0
  }

  // Handle bulk input
  const handleBulkApply = () => {
    if (!bulkData || Object.keys(bulkData).length === 0) return

    const updatedTimelineData = { ...timelineData }

    students.forEach(student => {
      if (!updatedTimelineData[student.id]) {
        updatedTimelineData[student.id] = {
          subjects: {},
          activities: [""],
          behavior: "",
          behavior_notes: "",
          attendance_percentage: student.attendance_percentage || 0
        }
      }

      // Apply bulk data to each student
      if (bulkData.subjects) {
        Object.keys(bulkData.subjects).forEach(subjectId => {
          if (!updatedTimelineData[student.id].subjects[subjectId]) {
            updatedTimelineData[student.id].subjects[subjectId] = {
              value: "",
              grade: "",
              notes: ""
            }
          }

          // Only apply if current value is empty
          if (bulkData.subjects[subjectId].value && !updatedTimelineData[student.id].subjects[subjectId].value) {
            updatedTimelineData[student.id].subjects[subjectId].value = bulkData.subjects[subjectId].value
          }
          if (bulkData.subjects[subjectId].grade && !updatedTimelineData[student.id].subjects[subjectId].grade) {
            updatedTimelineData[student.id].subjects[subjectId].grade = bulkData.subjects[subjectId].grade
          }
          if (bulkData.subjects[subjectId].notes && !updatedTimelineData[student.id].subjects[subjectId].notes) {
            updatedTimelineData[student.id].subjects[subjectId].notes = bulkData.subjects[subjectId].notes
          }
        })
      }

      // Apply bulk behavior
      if (bulkData.behavior && !updatedTimelineData[student.id].behavior) {
        updatedTimelineData[student.id].behavior = bulkData.behavior
      }
      if (bulkData.behavior_notes && !updatedTimelineData[student.id].behavior_notes) {
        updatedTimelineData[student.id].behavior_notes = bulkData.behavior_notes
      }

      // Apply bulk activity
      if (bulkData.activity && updatedTimelineData[student.id].activities.length === 1 && !updatedTimelineData[student.id].activities[0]) {
        updatedTimelineData[student.id].activities = [bulkData.activity]
      }
    })

    setTimelineData(updatedTimelineData)
    setShowBulkDialog(false)
    setBulkData({})
  }

  // Fetch initial data
  useEffect(() => {
    const fetchInitialData = async () => {
      try {
        setLoading(true)

        // Fetch classes
        const classesResponse = await fetch('/api/classes')
        if (classesResponse.ok) {
          const classesData = await classesResponse.json()
          setClasses(classesData.classes || [])
        }

        // Fetch subjects
        const subjectsResponse = await fetch('/api/timeline/subjects')
        if (subjectsResponse.ok) {
          const subjectsData = await subjectsResponse.json()
          setSubjects(subjectsData.subjects || [])
        }

      } catch (error) {
        console.error('Error fetching initial data:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchInitialData()
  }, [])

  // Handle class selection
  const handleClassChange = async (value: string) => {
    setSelectedClass(value)
    setStudents([])
    setTimelineData({})

    // Only fetch timeline data if month and year are selected
    if (!selectedMonth || !selectedYear) {
      return
    }

    setLoadingStudents(true)

    try {
      // Fetch students and existing timeline data
      const response = await fetch(`/api/timeline?class_id=${value}&month=${selectedMonth}&year=${selectedYear}`)
      if (response.ok) {
        const data = await response.json()
        setStudents(data.students || [])

        // Initialize timeline data
        const initialData: Record<string, any> = {}
        data.students?.forEach((student: Student) => {
          initialData[student.id] = {
            subjects: {},
            activities: [""],
            behavior: student.behavior_record?.behavior_grade || "",
            behavior_notes: student.behavior_record?.notes || "",
            attendance_percentage: student.attendance_percentage || 0
          }

          // Initialize subject data
          subjects.forEach(subject => {
            const existingDetail = student.timeline_entry?.timeline_details?.find(
              (detail: any) => detail.subject_id === subject.id
            )
            initialData[student.id].subjects[subject.id] = {
              value: existingDetail?.value || "",
              grade: existingDetail?.grade || "",
              notes: existingDetail?.notes || ""
            }
          })

          // Set activities
          if (student.timeline_entry?.timeline_activities?.length > 0) {
            initialData[student.id].activities = student.timeline_entry.timeline_activities.map(
              (activity: any) => activity.activity
            )
          }
        })

        setTimelineData(initialData)
      }
    } catch (error) {
      console.error('Error fetching students:', error)
    } finally {
      setLoadingStudents(false)
    }
  }

  // Handle month/year change - refetch data if class is selected
  const handleMonthYearChange = async (month?: string, year?: string) => {
    const newMonth = month || selectedMonth
    const newYear = year || selectedYear

    if (month) setSelectedMonth(month)
    if (year) setSelectedYear(year)

    // Only fetch if all required params are available
    if (!selectedClass || !newMonth || !newYear) {
      return
    }

    setLoadingStudents(true)

    try {
      const response = await fetch(`/api/timeline?class_id=${selectedClass}&month=${newMonth}&year=${newYear}`)
      if (response.ok) {
        const data = await response.json()
        setStudents(data.students || [])

        // Initialize timeline data
        const initialData: Record<string, any> = {}
        data.students?.forEach((student: Student) => {
          initialData[student.id] = {
            subjects: {},
            activities: [""],
            behavior: student.behavior_record?.behavior_grade || "",
            behavior_notes: student.behavior_record?.notes || "",
            attendance_percentage: student.attendance_percentage || 0
          }

          // Initialize subject data
          subjects.forEach(subject => {
            const existingDetail = student.timeline_entry?.timeline_details?.find(
              (detail: any) => detail.subject_id === subject.id
            )
            initialData[student.id].subjects[subject.id] = {
              value: existingDetail?.value || "",
              grade: existingDetail?.grade || "",
              notes: existingDetail?.notes || ""
            }
          })

          // Set activities
          if (student.timeline_entry?.timeline_activities?.length > 0) {
            initialData[student.id].activities = student.timeline_entry.timeline_activities.map(
              (activity: any) => activity.activity
            )
          }
        })

        setTimelineData(initialData)
      }
    } catch (error) {
      console.error('Error fetching timeline data:', error)
    } finally {
      setLoadingStudents(false)
    }
  }



  // Handle form submission
  const handleSubmit = async () => {
    setIsSubmitting(true)

    try {
      const response = await fetch('/api/timeline', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          class_id: selectedClass,
          month: selectedMonth,
          year: selectedYear,
          timeline_data: timelineData,
        }),
      })

      if (response.ok) {
        setShowSuccess(true)
        // Hide success message after 3 seconds
        setTimeout(() => {
          setShowSuccess(false)
        }, 3000)
      } else {
        const error = await response.json()
        alert(`Gagal menyimpan data: ${error.error}`)
      }
    } catch (error) {
      console.error('Error submitting timeline:', error)
      alert('Terjadi kesalahan saat menyimpan data')
    } finally {
      setIsSubmitting(false)
    }
  }



  if (loading) {
    return (
      <div className="container mx-auto p-4 md:p-6">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-emerald-600" />
            <p className="text-gray-600">Memuat data...</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto p-4 md:p-6">
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-800 mb-2">Input Timeline Siswa</h1>
            <p className="text-gray-600">Masukkan data perkembangan bulanan untuk siswa dalam satu kelas</p>
          </div>
          <Button
            variant="outline"
            onClick={() => router.push('/dashboard/timeline/history')}
            className="gap-2"
          >
            <History className="h-4 w-4" />
            Lihat History
          </Button>
        </div>
      </div>

      {showSuccess && (
        <Alert className="mb-6 bg-emerald-50 border-emerald-200">
          <CheckCircle className="h-4 w-4 text-emerald-600" />
          <AlertTitle className="text-emerald-600">Berhasil!</AlertTitle>
          <AlertDescription className="text-emerald-700">Data timeline siswa berhasil disimpan.</AlertDescription>
        </Alert>
      )}

      <Card className="mb-8">
        <CardHeader>
          <CardTitle>Pilih Kelas dan Periode</CardTitle>
          <CardDescription>Pilih kelas dan periode untuk input data timeline</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="space-y-2">
              <Label htmlFor="class">Kelas</Label>
              <Select value={selectedClass} onValueChange={handleClassChange}>
                <SelectTrigger id="class">
                  <SelectValue placeholder="Pilih kelas" />
                </SelectTrigger>
                <SelectContent>
                  {classes.map((cls) => (
                    <SelectItem key={cls.id} value={cls.id}>
                      {cls.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="month">Bulan</Label>
              <Select value={selectedMonth} onValueChange={(value) => handleMonthYearChange(value, undefined)}>
                <SelectTrigger id="month">
                  <SelectValue placeholder="Pilih bulan" />
                </SelectTrigger>
                <SelectContent>
                  {months.map((month) => (
                    <SelectItem key={month} value={month}>
                      {month}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="year">Tahun</Label>
              <Select value={selectedYear} onValueChange={(value) => handleMonthYearChange(undefined, value)}>
                <SelectTrigger id="year">
                  <SelectValue placeholder="Pilih tahun" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value={currentYear.toString()}>{currentYear}</SelectItem>
                  <SelectItem value={(currentYear - 1).toString()}>{currentYear - 1}</SelectItem>
                  <SelectItem value={(currentYear + 1).toString()}>{currentYear + 1}</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {loadingStudents ? (
        <Card>
          <CardContent className="p-8 text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-emerald-600" />
            <p className="text-gray-600">Memuat data siswa...</p>
          </CardContent>
        </Card>
      ) : selectedClass && selectedMonth && selectedYear && students.length > 0 ? (
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-bold text-gray-800">
              Data Siswa {classes.find((c) => c.id === selectedClass)?.name}
            </h2>
            <div className="flex items-center gap-2">
              <ExcelImport
                classId={selectedClass}
                className={classes.find(c => c.id === selectedClass)?.name || ""}
                month={selectedMonth}
                year={selectedYear}
                onImportSuccess={() => {
                  // Refresh data after successful import
                  handleMonthYearChange(selectedMonth, selectedYear)
                }}
              />
              <Badge className="bg-emerald-100 text-emerald-800 hover:bg-emerald-200">{students.length} Siswa</Badge>
            </div>
          </div>

          <Accordion type="single" collapsible className="w-full space-y-4">
            {students.map((student) => (
              <AccordionItem
                key={student.id}
                value={student.id}
                className="border border-gray-200 rounded-lg overflow-hidden"
              >
                <AccordionTrigger className="px-4 py-3 hover:bg-gray-50">
                  <div className="flex items-center gap-3 text-left">
                    <Avatar>
                      <AvatarImage src={student.photo_url || "/placeholder.svg?height=40&width=40"} alt={student.name} />
                      <AvatarFallback>{student.name.charAt(0)}</AvatarFallback>
                    </Avatar>
                    <div>
                      <p className="font-medium">{student.name}</p>
                      <p className="text-sm text-gray-500">ID: {student.student_id}</p>
                      <p className="text-xs text-emerald-600">Kehadiran: {student.attendance_percentage}%</p>
                    </div>
                  </div>
                </AccordionTrigger>
                <AccordionContent className="px-4 py-3 border-t border-gray-200">
                  <div className="space-y-6">
                    {/* Subjects Progress */}
                    <div className="space-y-6">
                      {subjects.map((subject) => (
                        <div key={subject.id} className="border rounded-lg p-4">
                          <h3 className="font-medium mb-4 text-emerald-700">{subject.name}</h3>
                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div className="space-y-2">
                              <Label htmlFor={`${subject.id}-value-${student.id}`}>Pencapaian</Label>
                              <Input
                                id={`${subject.id}-value-${student.id}`}
                                placeholder="Contoh: Juz 1 ayat 1-50"
                                value={timelineData[student.id]?.subjects?.[subject.id]?.value || ""}
                                onChange={(e) =>
                                  setTimelineData((prev) => ({
                                    ...prev,
                                    [student.id]: {
                                      ...prev[student.id],
                                      subjects: {
                                        ...prev[student.id]?.subjects,
                                        [subject.id]: {
                                          ...prev[student.id]?.subjects?.[subject.id],
                                          value: e.target.value,
                                        },
                                      },
                                    },
                                  }))
                                }
                              />
                            </div>

                            <div className="space-y-2">
                              <Label htmlFor={`${subject.id}-grade-${student.id}`}>Nilai</Label>
                              <Input
                                id={`${subject.id}-grade-${student.id}`}
                                placeholder="Masukkan nilai"
                                value={timelineData[student.id]?.subjects?.[subject.id]?.grade || ""}
                                onChange={(e) =>
                                  setTimelineData((prev) => ({
                                    ...prev,
                                    [student.id]: {
                                      ...prev[student.id],
                                      subjects: {
                                        ...prev[student.id]?.subjects,
                                        [subject.id]: {
                                          ...prev[student.id]?.subjects?.[subject.id],
                                          grade: e.target.value,
                                        },
                                      },
                                    },
                                  }))
                                }
                              />
                            </div>

                            <div className="space-y-2">
                              <Label htmlFor={`${subject.id}-notes-${student.id}`}>Catatan</Label>
                              <Textarea
                                id={`${subject.id}-notes-${student.id}`}
                                placeholder="Masukkan catatan"
                                value={timelineData[student.id]?.subjects?.[subject.id]?.notes || ""}
                                onChange={(e) =>
                                  setTimelineData((prev) => ({
                                    ...prev,
                                    [student.id]: {
                                      ...prev[student.id],
                                      subjects: {
                                        ...prev[student.id]?.subjects,
                                        [subject.id]: {
                                          ...prev[student.id]?.subjects?.[subject.id],
                                          notes: e.target.value,
                                        },
                                      },
                                    },
                                  }))
                                }
                              />
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>

                    {/* Behavior */}
                    <div className="border rounded-lg p-4">
                      <h3 className="font-medium mb-4 text-emerald-700">Perilaku</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor={`behavior-${student.id}`}>Nilai Perilaku</Label>
                          <Select
                            value={timelineData[student.id]?.behavior || ""}
                            onValueChange={(value) =>
                              setTimelineData((prev) => ({
                                ...prev,
                                [student.id]: {
                                  ...prev[student.id],
                                  behavior: value,
                                },
                              }))
                            }
                          >
                            <SelectTrigger id={`behavior-${student.id}`}>
                              <SelectValue placeholder="Pilih perilaku" />
                            </SelectTrigger>
                            <SelectContent>
                              {behaviorOptions.map((behavior) => (
                                <SelectItem key={behavior} value={behavior}>
                                  {behavior}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor={`behavior-notes-${student.id}`}>Catatan Perilaku</Label>
                          <Textarea
                            id={`behavior-notes-${student.id}`}
                            placeholder="Masukkan catatan perilaku"
                            value={timelineData[student.id]?.behavior_notes || ""}
                            onChange={(e) =>
                              setTimelineData((prev) => ({
                                ...prev,
                                [student.id]: {
                                  ...prev[student.id],
                                  behavior_notes: e.target.value,
                                },
                              }))
                            }
                          />
                        </div>
                      </div>
                    </div>

                    {/* Activities */}
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <h3 className="font-medium">Aktivitas & Prestasi</h3>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleAddActivity(student.id)}
                          className="h-8 gap-1"
                        >
                          <Plus className="h-3.5 w-3.5" />
                          Tambah
                        </Button>
                      </div>

                      <div className="space-y-2">
                        {timelineData[student.id]?.activities.map((activity: string, index: number) => (
                          <div key={index} className="flex items-center gap-2">
                            <Input
                              placeholder={`Aktivitas ${index + 1}`}
                              value={activity}
                              onChange={(e) => handleActivityChange(student.id, index, e.target.value)}
                            />
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => handleRemoveActivity(student.id, index)}
                              className="h-10 w-10 text-red-500 hover:text-red-600 hover:bg-red-50"
                              disabled={timelineData[student.id]?.activities.length <= 1}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>

          <div className="flex justify-end gap-4 mt-8">
            <Button variant="outline" onClick={() => router.back()}>
              Batal
            </Button>
            <Button onClick={handleSubmit} disabled={!isFormValid() || isSubmitting} className="gap-2">
              {isSubmitting ? "Menyimpan..." : "Simpan Data Timeline"}
              <Save className="h-4 w-4" />
            </Button>
          </div>
        </div>
      ) : selectedClass && (!selectedMonth || !selectedYear) ? (
        <Card>
          <CardContent className="p-8 text-center">
            <Calendar className="h-12 w-12 text-amber-500 mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">Pilih Bulan dan Tahun</h3>
            <p className="text-gray-500">Silakan pilih bulan dan tahun untuk memuat data timeline siswa.</p>
          </CardContent>
        </Card>
      ) : selectedClass ? (
        <Card>
          <CardContent className="p-8 text-center">
            <AlertCircle className="h-12 w-12 text-amber-500 mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">Tidak Ada Siswa</h3>
            <p className="text-gray-500 mb-4">Tidak ada siswa yang ditemukan di kelas ini.</p>
            <Button variant="outline" onClick={() => setSelectedClass("")}>
              Pilih Kelas Lain
            </Button>
          </CardContent>
        </Card>
      ) : (
        <Card>
          <CardContent className="p-8 text-center">
            <Users className="h-12 w-12 text-emerald-500 mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">Pilih Kelas</h3>
            <p className="text-gray-500">Silakan pilih kelas untuk melihat daftar siswa dan input data timeline.</p>
          </CardContent>
        </Card>
      )}

      {/* Bulk Input Dialog */}
      <Dialog open={showBulkDialog} onOpenChange={setShowBulkDialog}>
        <DialogTrigger asChild>
          <Button
            variant="outline"
            className="fixed bottom-6 right-6 shadow-lg gap-2"
            disabled={!selectedClass || students.length === 0}
          >
            <Calendar className="h-4 w-4" />
            Input Massal
          </Button>
        </DialogTrigger>
        <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Input Data Massal</DialogTitle>
            <DialogDescription>
              Input data yang sama untuk semua siswa dalam kelas ini. Data yang sudah diisi per siswa tidak akan
              ditimpa.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-6 py-4">
            {/* Subjects Section */}
            <div className="space-y-4">
              <h3 className="font-medium text-emerald-700">Mata Pelajaran</h3>
              {subjects.map((subject) => (
                <div key={subject.id} className="border rounded-lg p-4 space-y-3">
                  <h4 className="font-medium">{subject.name}</h4>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                    <div className="space-y-2">
                      <Label htmlFor={`bulk-${subject.id}-value`}>Pencapaian</Label>
                      <Input
                        id={`bulk-${subject.id}-value`}
                        placeholder="Contoh: Juz 1 ayat 1-50"
                        value={bulkData.subjects?.[subject.id]?.value || ""}
                        onChange={(e) =>
                          setBulkData((prev) => ({
                            ...prev,
                            subjects: {
                              ...prev.subjects,
                              [subject.id]: {
                                ...prev.subjects?.[subject.id],
                                value: e.target.value,
                              },
                            },
                          }))
                        }
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor={`bulk-${subject.id}-grade`}>Nilai</Label>
                      <Input
                        id={`bulk-${subject.id}-grade`}
                        placeholder="Masukkan nilai"
                        value={bulkData.subjects?.[subject.id]?.grade || ""}
                        onChange={(e) =>
                          setBulkData((prev) => ({
                            ...prev,
                            subjects: {
                              ...prev.subjects,
                              [subject.id]: {
                                ...prev.subjects?.[subject.id],
                                grade: e.target.value,
                              },
                            },
                          }))
                        }
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor={`bulk-${subject.id}-notes`}>Catatan</Label>
                      <Input
                        id={`bulk-${subject.id}-notes`}
                        placeholder="Masukkan catatan"
                        value={bulkData.subjects?.[subject.id]?.notes || ""}
                        onChange={(e) =>
                          setBulkData((prev) => ({
                            ...prev,
                            subjects: {
                              ...prev.subjects,
                              [subject.id]: {
                                ...prev.subjects?.[subject.id],
                                notes: e.target.value,
                              },
                            },
                          }))
                        }
                      />
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Behavior Section */}
            <div className="space-y-4">
              <h3 className="font-medium text-emerald-700">Perilaku</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="bulk-behavior">Nilai Perilaku</Label>
                  <Select
                    value={bulkData.behavior || ""}
                    onValueChange={(value) =>
                      setBulkData((prev) => ({
                        ...prev,
                        behavior: value,
                      }))
                    }
                  >
                    <SelectTrigger id="bulk-behavior">
                      <SelectValue placeholder="Pilih perilaku" />
                    </SelectTrigger>
                    <SelectContent>
                      {behaviorOptions.map((behavior) => (
                        <SelectItem key={behavior} value={behavior}>
                          {behavior}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="bulk-behavior-notes">Catatan Perilaku</Label>
                  <Input
                    id="bulk-behavior-notes"
                    placeholder="Masukkan catatan perilaku"
                    value={bulkData.behavior_notes || ""}
                    onChange={(e) =>
                      setBulkData((prev) => ({
                        ...prev,
                        behavior_notes: e.target.value,
                      }))
                    }
                  />
                </div>
              </div>
            </div>

            {/* Activity Section */}
            <div className="space-y-4">
              <h3 className="font-medium text-emerald-700">Aktivitas</h3>
              <div className="space-y-2">
                <Label htmlFor="bulk-activity">Aktivitas Umum</Label>
                <Textarea
                  id="bulk-activity"
                  placeholder="Masukkan aktivitas yang diikuti oleh semua siswa"
                  value={bulkData.activity || ""}
                  onChange={(e) =>
                    setBulkData((prev) => ({
                      ...prev,
                      activity: e.target.value,
                    }))
                  }
                />
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowBulkDialog(false)}>
              Batal
            </Button>
            <Button onClick={handleBulkApply}>
              Terapkan ke Semua Siswa
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
