import { Suspense } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { ArrowLeft } from "lucide-react"
import Link from "next/link"
import { ParentForm } from "@/components/parents/parent-form"

export default function NewParentPage() {
  return (
    <div className="container mx-auto py-6">
      {/* Header */}
      <div className="flex items-center gap-4 mb-6">
        <Button variant="outline" size="sm" asChild>
          <Link href="/dashboard/parents">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Kembali
          </Link>
        </Button>
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Tambah Orangtua</h1>
          <p className="text-muted-foreground">Tambahkan data orangtua/wali baru</p>
        </div>
      </div>

      <Separator className="mb-6" />

      <Suspense fallback={<div>Loading...</div>}>
        <ParentForm />
      </Suspense>
    </div>
  )
}
