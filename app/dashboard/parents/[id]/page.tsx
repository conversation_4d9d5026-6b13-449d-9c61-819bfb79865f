import { Suspense } from "react"
import { notFound } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { 
  ArrowLeft, 
  Edit, 
  Phone, 
  Mail, 
  MapPin, 
  Briefcase, 
  Users, 
  Calendar,
  User
} from "lucide-react"
import Link from "next/link"
import { formatDistanceToNow } from "date-fns"
import { createServerClient } from "@/utils/supabase/server"

// Function to get parent data
async function getParent(parentId: string) {
  try {
    const supabase = await createServerClient()

    const { data: parent, error } = await supabase
      .from('parents')
      .select(`
        id,
        profile_id,
        name,
        phone,
        email,
        address,
        occupation,
        created_at,
        updated_at
      `)
      .eq('id', parentId)
      .single()

    if (error) {
      console.error('Error fetching parent:', error)
      return null
    }

    return parent
  } catch (error) {
    console.error('Failed to get parent:', error)
    return null
  }
}

// Function to get parent's students
async function getParentStudents(parentId: string) {
  try {
    const supabase = await createServerClient()

    const { data: relations, error } = await supabase
      .from('student_parent')
      .select(`
        id,
        relationship,
        is_primary,
        students!inner(
          id,
          student_id,
          name,
          gender,
          birth_date,
          status
        )
      `)
      .eq('parent_id', parentId)

    if (error) {
      console.error('Error fetching parent students:', error)
      return []
    }

    return relations || []
  } catch (error) {
    console.error('Failed to get parent students:', error)
    return []
  }
}

interface ParentDetailPageProps {
  params: Promise<{ id: string }>
}

export default async function ParentDetailPage({ params }: ParentDetailPageProps) {
  const { id } = await params
  
  console.log("ParentDetailPage - Parent ID:", id)

  // Fetch parent data and students
  const [parent, studentRelations] = await Promise.all([
    getParent(id),
    getParentStudents(id)
  ])

  console.log("ParentDetailPage - Parent data:", parent)
  console.log("ParentDetailPage - Student relations:", studentRelations)

  if (!parent) {
    notFound()
  }

  return (
    <div className="container mx-auto py-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" asChild>
            <Link href="/dashboard/parents">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Kembali
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Detail Orangtua</h1>
            <p className="text-muted-foreground">Informasi lengkap orangtua/wali</p>
          </div>
        </div>
        <Button asChild>
          <Link href={`/dashboard/parents/${id}/edit`}>
            <Edit className="h-4 w-4 mr-2" />
            Edit Data
          </Link>
        </Button>
      </div>

      <Separator className="mb-6" />

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Parent Info */}
        <div className="lg:col-span-2 space-y-6">
          <Card>
            <CardHeader>
              <div className="flex items-center space-x-4">
                <Avatar className="h-16 w-16">
                  <AvatarImage src={`/placeholder.svg?height=64&width=64`} alt={parent.name} />
                  <AvatarFallback className="text-lg">
                    {parent.name.split(' ').map(n => n[0]).join('').toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <CardTitle className="text-2xl">{parent.name}</CardTitle>
                  <p className="text-muted-foreground">ID: {parent.id}</p>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center space-x-3">
                  <Phone className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <p className="text-sm font-medium">Telepon</p>
                    <p className="text-sm text-muted-foreground">{parent.phone || "Tidak ada"}</p>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <Mail className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <p className="text-sm font-medium">Email</p>
                    <p className="text-sm text-muted-foreground">{parent.email || "Tidak ada"}</p>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <Briefcase className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <p className="text-sm font-medium">Pekerjaan</p>
                    <p className="text-sm text-muted-foreground">{parent.occupation || "Tidak diketahui"}</p>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <Calendar className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <p className="text-sm font-medium">Terdaftar</p>
                    <p className="text-sm text-muted-foreground">
                      {formatDistanceToNow(new Date(parent.created_at), {
                        addSuffix: true
                      })}
                    </p>
                  </div>
                </div>
              </div>
              
              {parent.address && (
                <div className="flex items-start space-x-3">
                  <MapPin className="h-5 w-5 text-muted-foreground mt-0.5" />
                  <div>
                    <p className="text-sm font-medium">Alamat</p>
                    <p className="text-sm text-muted-foreground">{parent.address}</p>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Students List */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Anak/Siswa ({studentRelations.length})
              </CardTitle>
            </CardHeader>
            <CardContent>
              {studentRelations.length > 0 ? (
                <div className="space-y-4">
                  {studentRelations.map((relation) => (
                    <div key={relation.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center space-x-3">
                        <Avatar className="h-10 w-10">
                          <AvatarFallback>
                            {relation.students.name.split(' ').map((n: string) => n[0]).join('').toUpperCase()}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <p className="font-medium">{relation.students.name}</p>
                          <div className="flex items-center gap-2 text-sm text-muted-foreground">
                            <span>
                              {relation.relationship === "father"
                                ? "Ayah"
                                : relation.relationship === "mother"
                                  ? "Ibu"
                                  : "Wali"}
                            </span>
                            {relation.is_primary && (
                              <Badge variant="outline" className="text-xs">
                                Wali Utama
                              </Badge>
                            )}
                          </div>
                        </div>
                      </div>
                      <Button variant="outline" size="sm" asChild>
                        <Link href={`/dashboard/students/${relation.students.id}`}>
                          <User className="h-4 w-4" />
                        </Link>
                      </Button>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Users className="h-12 w-12 mx-auto text-muted-foreground" />
                  <h3 className="mt-4 text-lg font-medium">Belum ada siswa</h3>
                  <p className="mt-2 text-sm text-muted-foreground">
                    Orangtua ini belum terhubung dengan siswa manapun
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
