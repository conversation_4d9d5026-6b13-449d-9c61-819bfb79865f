import { Suspense } from "react"
import { notFound } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { ArrowLeft } from "lucide-react"
import Link from "next/link"
import { ParentForm } from "@/components/parents/parent-form"
import { createServerClient } from "@/utils/supabase/server"

// Function to get parent data for editing
async function getParent(parentId: string) {
  try {
    const supabase = await createServerClient()

    const { data: parent, error } = await supabase
      .from('parents')
      .select(`
        id,
        profile_id,
        name,
        phone,
        email,
        address,
        occupation,
        created_at,
        updated_at
      `)
      .eq('id', parentId)
      .single()

    if (error) {
      console.error('Error fetching parent for edit:', error)
      return null
    }

    return parent
  } catch (error) {
    console.error('Failed to get parent for edit:', error)
    return null
  }
}

interface EditParentPageProps {
  params: Promise<{ id: string }>
}

export default async function EditParentPage({ params }: EditParentPageProps) {
  const { id } = await params
  
  console.log("EditParentPage - Parent ID:", id)

  // Fetch parent data for editing
  const parent = await getParent(id)

  console.log("EditParentPage - Parent data:", parent)

  if (!parent) {
    notFound()
  }

  return (
    <div className="container mx-auto py-6">
      {/* Header */}
      <div className="flex items-center gap-4 mb-6">
        <Button variant="outline" size="sm" asChild>
          <Link href={`/dashboard/parents/${id}`}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Kembali
          </Link>
        </Button>
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Edit Orangtua</h1>
          <p className="text-muted-foreground">Edit data orangtua/wali: {parent.name}</p>
        </div>
      </div>

      <Separator className="mb-6" />

      <Suspense fallback={<div>Loading...</div>}>
        <ParentForm parentId={id} />
      </Suspense>
    </div>
  )
}
