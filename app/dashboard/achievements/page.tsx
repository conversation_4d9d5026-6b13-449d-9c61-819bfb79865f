"use client"

import { useState, useEffect } from "react"
import { use<PERSON>out<PERSON> } from "next/navigation"
import { Plus, Trophy, BookOpen, Users, Calendar, Search, Filter, Eye, Edit, Trash2, Award } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Alert, AlertDescription } from "@/components/ui/alert"

interface Achievement {
  id: string
  student_id: string
  subject_id: string
  value: string
  grade: string
  notes: string
  verified_by: string
  achievement_date: string
  created_at: string
  updated_at: string
  students: {
    id: string
    student_id: string
    name: string
    class_students: Array<{
      classes: {
        id: string
        name: string
      }
    }>
  }
  subjects: {
    id: string
    name: string
    category: string
  }
  teachers: {
    id: string
    name: string
    employee_id: string
  }
}

interface Class {
  id: string
  name: string
}

interface Subject {
  id: string
  name: string
  category: string
}

interface Teacher {
  id: string
  name: string
  user_id: string
}

interface Student {
  id: string
  student_id: string
  name: string
  classes?: Array<{
    id: string
    name: string
  }>
}

export default function AchievementsPage() {
  const router = useRouter()
  const [achievements, setAchievements] = useState<Achievement[]>([])
  const [classes, setClasses] = useState<Class[]>([])
  const [subjects, setSubjects] = useState<Subject[]>([])
  const [teachers, setTeachers] = useState<Teacher[]>([])
  const [students, setStudents] = useState<Student[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedAchievement, setSelectedAchievement] = useState<Achievement | null>(null)
  const [showCreateDialog, setShowCreateDialog] = useState(false)
  const [showDetailDialog, setShowDetailDialog] = useState(false)

  // Filters
  const [filters, setFilters] = useState({
    classId: '',
    subjectId: '',
    search: '',
    limit: 50,
    offset: 0
  })

  // Form data for creating/editing achievements
  const [formData, setFormData] = useState({
    student_id: '',
    subject_id: '',
    value: '',
    grade: '',
    notes: '',
    verified_by: '',
    achievement_date: new Date().toISOString().split('T')[0]
  })

  const [total, setTotal] = useState(0)
  const [showSuccess, setShowSuccess] = useState(false)
  const [successMessage, setSuccessMessage] = useState('')

  // Load initial data
  useEffect(() => {
    loadAchievements()
    loadClasses()
    loadSubjects()
    loadTeachers()
    loadStudents()
  }, [filters])

  const loadAchievements = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams()
      
      if (filters.classId) params.append('classId', filters.classId)
      if (filters.subjectId) params.append('subjectId', filters.subjectId)
      params.append('limit', filters.limit.toString())
      params.append('offset', filters.offset.toString())

      const response = await fetch(`/api/achievements?${params}`)
      const data = await response.json()

      if (response.ok) {
        setAchievements(data.achievements || [])
        setTotal(data.total || 0)
      } else {
        console.error('Failed to load achievements:', data.error)
      }
    } catch (error) {
      console.error('Error loading achievements:', error)
    } finally {
      setLoading(false)
    }
  }

  const loadClasses = async () => {
    try {
      const response = await fetch('/api/classes')
      const data = await response.json()
      if (response.ok) {
        setClasses(data.classes || [])
      }
    } catch (error) {
      console.error('Error loading classes:', error)
    }
  }

  const loadSubjects = async () => {
    try {
      const response = await fetch('/api/subjects')
      const data = await response.json()
      if (response.ok) {
        setSubjects(data.subjects || [])
      } else {
        console.error('Error loading subjects:', data.error)
        // Set fallback subjects if API fails
        setSubjects([
          { id: "fallback-1", name: "Al-Quran", category: "Agama" },
          { id: "fallback-2", name: "Hadits", category: "Agama" },
          { id: "fallback-3", name: "Fiqh", category: "Agama" },
          { id: "fallback-4", name: "Bahasa Arab", category: "Bahasa" },
          { id: "fallback-5", name: "Matematika", category: "Umum" }
        ])
      }
    } catch (error) {
      console.error('Error loading subjects:', error)
      // Set fallback subjects if network error
      setSubjects([
        { id: "fallback-1", name: "Al-Quran", category: "Agama" },
        { id: "fallback-2", name: "Hadits", category: "Agama" },
        { id: "fallback-3", name: "Fiqh", category: "Agama" },
        { id: "fallback-4", name: "Bahasa Arab", category: "Bahasa" },
        { id: "fallback-5", name: "Matematika", category: "Umum" }
      ])
    }
  }

  const loadTeachers = async () => {
    try {
      const response = await fetch('/api/teachers')
      const data = await response.json()
      if (response.ok) {
        setTeachers(data.teachers || [])
      }
    } catch (error) {
      console.error('Error loading teachers:', error)
    }
  }

  const loadStudents = async () => {
    try {
      const response = await fetch('/api/students')
      const data = await response.json()
      if (response.ok) {
        setStudents(data.students || [])
      }
    } catch (error) {
      console.error('Error loading students:', error)
    }
  }

  const handleFilterChange = (key: string, value: string) => {
    // Convert "all" to empty string for API
    const apiValue = value === "all" ? "" : value
    setFilters(prev => ({ ...prev, [key]: apiValue, offset: 0 }))
  }

  // Convert empty string to "all" for Select display
  const getSelectValue = (value: string) => value === '' ? 'all' : value

  const handleCreateAchievement = async () => {
    try {
      const response = await fetch('/api/achievements', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      })

      const data = await response.json()

      if (response.ok) {
        setShowSuccess(true)
        setSuccessMessage('Pencapaian berhasil ditambahkan!')
        setShowCreateDialog(false)
        setFormData({
          student_id: '',
          subject_id: '',
          value: '',
          grade: '',
          notes: '',
          verified_by: '',
          achievement_date: new Date().toISOString().split('T')[0]
        })
        loadAchievements()
        
        setTimeout(() => setShowSuccess(false), 3000)
      } else {
        alert('Error: ' + data.error)
      }
    } catch (error) {
      console.error('Error creating achievement:', error)
      alert('Terjadi kesalahan saat menambahkan pencapaian')
    }
  }

  const handleDeleteAchievement = async (achievementId: string) => {
    if (!confirm('Apakah Anda yakin ingin menghapus pencapaian ini?')) {
      return
    }

    try {
      const response = await fetch(`/api/achievements?id=${achievementId}`, {
        method: 'DELETE',
      })

      const data = await response.json()

      if (response.ok) {
        setShowSuccess(true)
        setSuccessMessage('Pencapaian berhasil dihapus!')
        loadAchievements()
        
        setTimeout(() => setShowSuccess(false), 3000)
      } else {
        alert('Error: ' + data.error)
      }
    } catch (error) {
      console.error('Error deleting achievement:', error)
      alert('Terjadi kesalahan saat menghapus pencapaian')
    }
  }

  const getGradeBadgeColor = (grade: string) => {
    if (grade === 'A' || grade === 'A+') return 'bg-green-100 text-green-800'
    if (grade === 'B' || grade === 'B+') return 'bg-blue-100 text-blue-800'
    if (grade === 'C' || grade === 'C+') return 'bg-yellow-100 text-yellow-800'
    return 'bg-gray-100 text-gray-800'
  }

  // Filter achievements based on search
  const filteredAchievements = achievements.filter(achievement =>
    achievement.students?.name?.toLowerCase().includes(filters.search.toLowerCase()) ||
    achievement.subjects?.name?.toLowerCase().includes(filters.search.toLowerCase()) ||
    achievement.value?.toLowerCase().includes(filters.search.toLowerCase())
  )

  return (
    <div className="container mx-auto p-4 md:p-6">
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-800 mb-2">Pencapaian Siswa</h1>
            <p className="text-gray-600">Kelola dan pantau pencapaian akademik siswa</p>
          </div>
          <Button
            onClick={() => setShowCreateDialog(true)}
            className="gap-2"
          >
            <Plus className="h-4 w-4" />
            Tambah Pencapaian
          </Button>
        </div>
      </div>

      {showSuccess && (
        <Alert className="mb-6 bg-emerald-50 border-emerald-200">
          <Trophy className="h-4 w-4 text-emerald-600" />
          <AlertDescription className="text-emerald-800">
            {successMessage}
          </AlertDescription>
        </Alert>
      )}

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Trophy className="h-8 w-8 text-yellow-500" />
              <div>
                <p className="text-2xl font-bold">{total}</p>
                <p className="text-sm text-gray-600">Total Pencapaian</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Users className="h-8 w-8 text-blue-500" />
              <div>
                <p className="text-2xl font-bold">{new Set(achievements.map(a => a.student_id)).size}</p>
                <p className="text-sm text-gray-600">Siswa Berprestasi</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <BookOpen className="h-8 w-8 text-green-500" />
              <div>
                <p className="text-2xl font-bold">{new Set(achievements.map(a => a.subject_id)).size}</p>
                <p className="text-sm text-gray-600">Mata Pelajaran</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Calendar className="h-8 w-8 text-purple-500" />
              <div>
                <p className="text-2xl font-bold">
                  {achievements.filter(a => {
                    const achievementDate = new Date(a.achievement_date)
                    const currentMonth = new Date().getMonth()
                    const currentYear = new Date().getFullYear()
                    return achievementDate.getMonth() === currentMonth && achievementDate.getFullYear() === currentYear
                  }).length}
                </p>
                <p className="text-sm text-gray-600">Bulan Ini</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filter Pencapaian
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <Label htmlFor="search">Cari Siswa/Mata Pelajaran</Label>
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  id="search"
                  placeholder="Cari..."
                  value={filters.search}
                  onChange={(e) => handleFilterChange('search', e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            <div>
              <Label htmlFor="class-filter">Kelas</Label>
              <Select value={getSelectValue(filters.classId)} onValueChange={(value) => handleFilterChange('classId', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Semua Kelas" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Semua Kelas</SelectItem>
                  {Array.isArray(classes) && classes.map((cls) => (
                    <SelectItem key={cls.id} value={cls.id}>
                      {cls.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="subject-filter">Mata Pelajaran</Label>
              <Select value={getSelectValue(filters.subjectId)} onValueChange={(value) => handleFilterChange('subjectId', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Semua Mata Pelajaran" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Semua Mata Pelajaran</SelectItem>
                  {Array.isArray(subjects) && subjects.map((subject) => (
                    <SelectItem key={subject.id} value={subject.id}>
                      {subject.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-end">
              <Button
                variant="outline"
                onClick={() => {
                  setFilters({
                    classId: '',
                    subjectId: '',
                    search: '',
                    limit: 50,
                    offset: 0
                  })
                }}
                className="w-full"
              >
                Reset Filter
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Achievements List */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Award className="h-5 w-5" />
            Daftar Pencapaian ({filteredAchievements.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="space-y-4">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="animate-pulse">
                  <div className="h-20 bg-gray-200 rounded"></div>
                </div>
              ))}
            </div>
          ) : filteredAchievements.length === 0 ? (
            <div className="text-center py-8">
              <Trophy className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">Belum ada pencapaian yang tercatat</p>
            </div>
          ) : (
            <div className="space-y-4">
              {filteredAchievements.map((achievement) => (
                <Card key={achievement.id} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          <div className="flex items-center gap-2">
                            <Users className="h-4 w-4 text-blue-500" />
                            <span className="font-medium">{achievement.students?.name}</span>
                          </div>
                          <Badge variant="outline">
                            {achievement.students?.class_students?.[0]?.classes?.name || 'No Class'}
                          </Badge>
                        </div>

                        <div className="flex items-center gap-3 mb-2">
                          <div className="flex items-center gap-2">
                            <BookOpen className="h-4 w-4 text-green-500" />
                            <span className="text-sm text-gray-600">{achievement.subjects?.name}</span>
                          </div>
                          <Badge className={getGradeBadgeColor(achievement.grade)}>
                            {achievement.grade || 'No Grade'}
                          </Badge>
                        </div>

                        <div className="mb-2">
                          <p className="font-medium text-gray-800">{achievement.value}</p>
                          {achievement.notes && (
                            <p className="text-sm text-gray-600 mt-1">{achievement.notes}</p>
                          )}
                        </div>

                        <div className="flex items-center gap-4 text-xs text-gray-500">
                          <span>Diverifikasi: {achievement.teachers?.name}</span>
                          <span>Tanggal: {new Date(achievement.achievement_date).toLocaleDateString('id-ID')}</span>
                        </div>
                      </div>

                      <div className="flex items-center gap-2 ml-4">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            setSelectedAchievement(achievement)
                            setShowDetailDialog(true)
                          }}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDeleteAchievement(achievement.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Create Achievement Dialog */}
      <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Tambah Pencapaian Baru</DialogTitle>
            <DialogDescription>
              Tambahkan pencapaian baru untuk siswa
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div>
              <Label htmlFor="student">Siswa</Label>
              <Select value={formData.student_id} onValueChange={(value) => setFormData(prev => ({ ...prev, student_id: value }))}>
                <SelectTrigger>
                  <SelectValue placeholder="Pilih siswa" />
                </SelectTrigger>
                <SelectContent>
                  {Array.isArray(students) && students.map((student) => (
                    <SelectItem key={student.id} value={student.id}>
                      <div className="flex items-center gap-2">
                        <span>{student.name}</span>
                        <span className="text-xs text-gray-500">({student.student_id})</span>
                        {student.classes && student.classes.length > 0 && (
                          <span className="text-xs text-blue-600">- {student.classes[0].name}</span>
                        )}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="subject">Mata Pelajaran</Label>
              <Select value={formData.subject_id} onValueChange={(value) => setFormData(prev => ({ ...prev, subject_id: value }))}>
                <SelectTrigger>
                  <SelectValue placeholder="Pilih mata pelajaran" />
                </SelectTrigger>
                <SelectContent>
                  {Array.isArray(subjects) && subjects.map((subject) => (
                    <SelectItem key={subject.id} value={subject.id}>
                      {subject.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="value">Pencapaian</Label>
              <Input
                id="value"
                placeholder="Contoh: Hafal Juz 30"
                value={formData.value}
                onChange={(e) => setFormData(prev => ({ ...prev, value: e.target.value }))}
              />
            </div>

            <div>
              <Label htmlFor="grade">Nilai</Label>
              <Select value={formData.grade} onValueChange={(value) => setFormData(prev => ({ ...prev, grade: value }))}>
                <SelectTrigger>
                  <SelectValue placeholder="Pilih nilai" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="A+">A+</SelectItem>
                  <SelectItem value="A">A</SelectItem>
                  <SelectItem value="B+">B+</SelectItem>
                  <SelectItem value="B">B</SelectItem>
                  <SelectItem value="C+">C+</SelectItem>
                  <SelectItem value="C">C</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="notes">Catatan</Label>
              <Textarea
                id="notes"
                placeholder="Catatan tambahan..."
                value={formData.notes}
                onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
              />
            </div>

            <div>
              <Label htmlFor="verified_by">Diverifikasi oleh</Label>
              <Select value={formData.verified_by} onValueChange={(value) => setFormData(prev => ({ ...prev, verified_by: value }))}>
                <SelectTrigger>
                  <SelectValue placeholder="Pilih guru" />
                </SelectTrigger>
                <SelectContent>
                  {Array.isArray(teachers) && teachers.map((teacher) => (
                    <SelectItem key={teacher.id} value={teacher.id}>
                      {teacher.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="achievement_date">Tanggal Pencapaian</Label>
              <Input
                id="achievement_date"
                type="date"
                value={formData.achievement_date}
                onChange={(e) => setFormData(prev => ({ ...prev, achievement_date: e.target.value }))}
              />
            </div>

            <div className="flex gap-2 pt-4">
              <Button
                variant="outline"
                onClick={() => setShowCreateDialog(false)}
                className="flex-1"
              >
                Batal
              </Button>
              <Button
                onClick={handleCreateAchievement}
                className="flex-1"
                disabled={!formData.student_id || !formData.subject_id || !formData.value || !formData.verified_by}
              >
                Simpan
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Detail Achievement Dialog */}
      <Dialog open={showDetailDialog} onOpenChange={setShowDetailDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Detail Pencapaian</DialogTitle>
          </DialogHeader>

          {selectedAchievement && (
            <div className="space-y-4">
              <div>
                <Label>Siswa</Label>
                <p className="font-medium">{selectedAchievement.students?.name}</p>
                <p className="text-sm text-gray-600">
                  {selectedAchievement.students?.class_students?.[0]?.classes?.name || 'No Class'}
                </p>
              </div>

              <div>
                <Label>Mata Pelajaran</Label>
                <p className="font-medium">{selectedAchievement.subjects?.name}</p>
                <p className="text-sm text-gray-600">{selectedAchievement.subjects?.category}</p>
              </div>

              <div>
                <Label>Pencapaian</Label>
                <p className="font-medium">{selectedAchievement.value}</p>
              </div>

              <div>
                <Label>Nilai</Label>
                <Badge className={getGradeBadgeColor(selectedAchievement.grade)}>
                  {selectedAchievement.grade || 'No Grade'}
                </Badge>
              </div>

              {selectedAchievement.notes && (
                <div>
                  <Label>Catatan</Label>
                  <p className="text-sm">{selectedAchievement.notes}</p>
                </div>
              )}

              <div>
                <Label>Diverifikasi oleh</Label>
                <p className="font-medium">{selectedAchievement.teachers?.name}</p>
              </div>

              <div>
                <Label>Tanggal Pencapaian</Label>
                <p className="font-medium">
                  {new Date(selectedAchievement.achievement_date).toLocaleDateString('id-ID')}
                </p>
              </div>

              <div className="pt-4">
                <Button
                  variant="outline"
                  onClick={() => setShowDetailDialog(false)}
                  className="w-full"
                >
                  Tutup
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}
