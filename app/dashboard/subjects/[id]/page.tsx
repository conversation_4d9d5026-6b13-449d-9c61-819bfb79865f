import Link from "next/link"
import { ChevronLeft, Pencil } from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { getSubject } from "@/lib/api/subjects"

export const metadata = {
  title: "Detail Subject",
  description: "Informasi detail tentang subject pencapaian",
}

export default async function SubjectDetailPage({ params }: { params: { id: string } }) {
  const subjectData = await getSubject(params.id)
  
  if (!subjectData) {
    return (
      <div className="container mx-auto py-6">
        <div className="mb-6">
          <Button variant="outline" size="sm" asChild>
            <Link href="/dashboard/subjects">
              <ChevronLeft className="mr-2 h-4 w-4" />
              Kembali
            </Link>
          </Button>
        </div>
        <div className="flex flex-col items-center justify-center py-12">
          <h1 className="text-2xl font-bold">Subject tidak ditemukan</h1>
          <p className="text-muted-foreground mt-2">Subject yang Anda cari tidak ditemukan atau telah dihapus.</p>
          <Button asChild className="mt-4">
            <Link href="/dashboard/subjects">Kembali ke Daftar Subject</Link>
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto py-6">
      <div className="mb-6 flex justify-between">
        <Button variant="outline" size="sm" asChild>
          <Link href="/dashboard/subjects">
            <ChevronLeft className="mr-2 h-4 w-4" />
            Kembali
          </Link>
        </Button>
        
        <Button asChild>
          <Link href={`/dashboard/subjects/${params.id}/edit`}>
            <Pencil className="mr-2 h-4 w-4" />
            Edit Subject
          </Link>
        </Button>
      </div>
      
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">{subjectData.subject.name}</h1>
            <div className="flex items-center mt-2 gap-2">
              <Badge>{subjectData.subject.category}</Badge>
              <Badge variant={subjectData.subject.is_active ? "default" : "secondary"}>
                {subjectData.subject.is_active ? "Aktif" : "Tidak Aktif"}
              </Badge>
            </div>
          </div>
        </div>
        
        <Separator className="my-4" />
        
        <div className="grid gap-6 md:grid-cols-2 mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Deskripsi</CardTitle>
            </CardHeader>
            <CardContent>
              <p>{subjectData.subject.description}</p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle>Tujuan Pembelajaran</CardTitle>
            </CardHeader>
            <CardContent>
              {subjectData.objectives && subjectData.objectives.length > 0 ? (
                <ul className="list-disc pl-5 space-y-2">
                  {subjectData.objectives.map((obj: any) => (
                    <li key={obj.id}>{obj.objective}</li>
                  ))}
                </ul>
              ) : (
                <p className="text-muted-foreground">Belum ada tujuan pembelajaran yang ditambahkan.</p>
              )}
            </CardContent>
          </Card>
        </div>
        
        <Card className="mt-6">
          <CardHeader>
            <CardTitle>Guru Pengajar</CardTitle>
          </CardHeader>
          <CardContent>
            {subjectData.teachers && subjectData.teachers.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {subjectData.teachers.map((teacher: any) => (
                  <div key={teacher.id} className="flex items-center p-3 rounded-md border">
                    <div>
                      <h3 className="font-medium">{teacher.name}</h3>
                      <p className="text-sm text-muted-foreground">{teacher.specialization || 'Tidak ada spesialisasi'}</p>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-muted-foreground">Belum ada guru yang mengajar subject ini.</p>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
