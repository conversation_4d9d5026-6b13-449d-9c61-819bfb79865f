import Link from "next/link"
import { ChevronLeft } from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { SubjectForm } from "@/components/subjects/subject-form"

export const metadata = {
  title: "Edit Subject",
  description: "Edit subject pencapaian",
}

export default function EditSubjectPage({ params }: { params: { id: string } }) {
  return (
    <div className="container mx-auto py-6">
      <div className="mb-6">
        <Button variant="outline" size="sm" asChild>
          <Link href="/dashboard/subjects">
            <ChevronLeft className="mr-2 h-4 w-4" />
            Kembali
          </Link>
        </Button>
      </div>
      <div className="mb-6">
        <h1 className="text-3xl font-bold tracking-tight">Edit Subject</h1>
        <p className="text-muted-foreground">Edit subject pencapaian dan tujuan pembelajarannya</p>
      </div>
      <SubjectForm subjectId={params.id} />
    </div>
  )
}
