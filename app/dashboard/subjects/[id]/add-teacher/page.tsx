import Link from "next/link"
import { ChevronLeft } from "lucide-react"

import { Button } from "@/components/ui/button"
import { AddTeacherForm } from "@/components/subjects/add-teacher-form"

export const metadata = {
  title: "Tambah Guru ke Subject",
  description: "Tambahkan guru ke subject pencapaian",
}

export default function AddTeacherPage({ params }: { params: { id: string } }) {
  return (
    <div className="container mx-auto py-6">
      <div className="mb-6">
        <Button variant="outline" size="sm" asChild>
          <Link href={`/dashboard/subjects/${params.id}`}>
            <ChevronLeft className="mr-2 h-4 w-4" />
            Kembali
          </Link>
        </Button>
      </div>
      <div className="mb-6">
        <h1 className="text-3xl font-bold tracking-tight">Tambah Guru ke Subject</h1>
        <p className="text-muted-foreground">Tambahkan guru baru atau pilih dari guru yang sudah ada</p>
      </div>
      <AddTeacherForm subjectId={params.id} />
    </div>
  )
}
