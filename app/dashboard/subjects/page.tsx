import Link from "next/link"
import { PlusCircle } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { SubjectList } from "@/components/subjects/subject-list"
import { getAllSubjects } from "@/lib/api/subjects"

export const metadata = {
  title: "Manajemen Subject Pencapaian",
  description: "Kelola subject pencapaian dan relasi dengan guru",
}

export default async function SubjectsPage() {
  // Pre-fetch subjects data from the server for initial render
  const initialSubjects = await getAllSubjects()
  
  return (
    <div className="container mx-auto py-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Subject Pencapaian</h1>
          <p className="text-muted-foreground">Kelola subject pencapaian dan relasi dengan guru</p>
        </div>
        <Button asChild>
          <Link href="/dashboard/subjects/new">
            <PlusCircle className="mr-2 h-4 w-4" />
            Tambah Subject
          </Link>
        </Button>
      </div>
      <Separator className="my-6" />
      <SubjectList initialSubjects={initialSubjects} />
    </div>
  )
}
