import Link from "next/link"
import { ChevronLeft } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { SubjectForm } from "@/components/subjects/subject-form"

export const metadata = {
  title: "Tambah Subject Baru",
  description: "Tambahkan subject pencapaian baru",
}

export default function NewSubjectPage() {
  return (
    <div className="container mx-auto py-6">
      <div className="mb-6">
        <Button variant="outline" size="sm" asChild>
          <Link href="/dashboard/subjects">
            <ChevronLeft className="mr-2 h-4 w-4" />
            Kembali
          </Link>
        </Button>
      </div>
      <div className="mb-6">
        <h1 className="text-3xl font-bold tracking-tight">Tambah Subject Baru</h1>
        <p className="text-muted-foreground">Tambahkan subject pencapaian baru dan atur relasinya dengan guru</p>
      </div>
      <SubjectForm />
    </div>
  )
}
