"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import Image from "next/image"
import { ArrowLeft, CheckCircle, XCircle, Clock, Download, Mail, Phone, FileText, Check, X } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Textarea } from "@/components/ui/textarea"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"

// Sample PPDB applications data (same as in the list page)
const ppdbApplications = [
  {
    id: "PPDB-2024-001",
    name: "<PERSON>",
    email: "<EMAIL>",
    phone: "081234567890",
    parentName: "<PERSON><PERSON>",
    parentPhone: "081234567891",
    parentEmail: "<EMAIL>",
    submittedAt: "2024-03-15T08:30:00",
    status: "pending",
    gender: "male",
    birthPlace: "Semarang",
    birthDate: "2012-05-10",
    address: "Jl. Pahlawan No. 123, Ungaran, Semarang",
    previousSchool: "SD Negeri 1 Ungaran",
    documents: {
      photo: true,
      birthCertificate: true,
      familyCard: true,
      schoolCertificate: false,
    },
    quranMemo: "1 Juz",
    batch: "Gelombang 1",
    notes: "",
  },
  {
    id: "PPDB-2024-002",
    name: "Fatimah Azzahra",
    email: "<EMAIL>",
    phone: "085678901234",
    parentName: "Ahmad Hidayat",
    parentPhone: "085678901235",
    parentEmail: "<EMAIL>",
    submittedAt: "2024-03-16T10:15:00",
    status: "approved",
    gender: "female",
    birthPlace: "Jakarta",
    birthDate: "2011-08-15",
    address: "Jl. Diponegoro No. 45, Ungaran, Semarang",
    previousSchool: "MI Nurul Iman",
    documents: {
      photo: true,
      birthCertificate: true,
      familyCard: true,
      schoolCertificate: true,
    },
    quranMemo: "2 Juz",
    batch: "Gelombang 1",
    notes: "Hafal 2 juz Al-Qur'an",
  },
  {
    id: "PPDB-2024-003",
    name: "Muhammad Rizki",
    email: "<EMAIL>",
    phone: "089876543210",
    parentName: "Hasan Basri",
    parentPhone: "089876543211",
    parentEmail: "<EMAIL>",
    submittedAt: "2024-03-17T14:45:00",
    status: "rejected",
    gender: "male",
    birthPlace: "Bandung",
    birthDate: "2012-03-20",
    address: "Jl. Gatot Subroto No. 78, Ungaran, Semarang",
    previousSchool: "SD Islam Al-Azhar",
    documents: {
      photo: true,
      birthCertificate: true,
      familyCard: false,
      schoolCertificate: true,
    },
    quranMemo: "Belum ada",
    batch: "Gelombang 1",
    notes: "Dokumen tidak lengkap",
  },
  {
    id: "PPDB-2024-004",
    name: "Aisyah Putri",
    email: "<EMAIL>",
    phone: "087654321098",
    parentName: "Siti Aminah",
    parentPhone: "087654321099",
    parentEmail: "<EMAIL>",
    submittedAt: "2024-03-18T09:20:00",
    status: "pending",
    gender: "female",
    birthPlace: "Surabaya",
    birthDate: "2011-11-05",
    address: "Jl. Ahmad Yani No. 56, Ungaran, Semarang",
    previousSchool: "SD Muhammadiyah 3",
    documents: {
      photo: true,
      birthCertificate: true,
      familyCard: true,
      schoolCertificate: true,
    },
    quranMemo: "1.5 Juz",
    batch: "Gelombang 1",
    notes: "",
  },
  {
    id: "PPDB-2024-005",
    name: "Abdullah Malik",
    email: "<EMAIL>",
    phone: "082345678901",
    parentName: "Malik Ibrahim",
    parentPhone: "082345678902",
    parentEmail: "<EMAIL>",
    submittedAt: "2024-03-19T11:30:00",
    status: "approved",
    gender: "male",
    birthPlace: "Yogyakarta",
    birthDate: "2012-01-15",
    address: "Jl. Sudirman No. 34, Ungaran, Semarang",
    previousSchool: "MI Darussalam",
    documents: {
      photo: true,
      birthCertificate: true,
      familyCard: true,
      schoolCertificate: true,
    },
    quranMemo: "3 Juz",
    batch: "Gelombang 1",
    notes: "Hafal 3 juz Al-Qur'an",
  },
  {
    id: "PPDB-2024-006",
    name: "Zahra Nabila",
    email: "<EMAIL>",
    phone: "083456789012",
    parentName: "Nabila Putri",
    parentPhone: "083456789013",
    parentEmail: "<EMAIL>",
    submittedAt: "2024-03-20T13:45:00",
    status: "pending",
    gender: "female",
    birthPlace: "Malang",
    birthDate: "2011-09-25",
    address: "Jl. Veteran No. 67, Ungaran, Semarang",
    previousSchool: "SD Islam Terpadu",
    documents: {
      photo: true,
      birthCertificate: false,
      familyCard: true,
      schoolCertificate: true,
    },
    quranMemo: "0.5 Juz",
    batch: "Gelombang 1",
    notes: "Menunggu dokumen akta kelahiran",
  },
]

export default function PPDBDetailPage({ params }: { params: { id: string } }) {
  const router = useRouter()
  const [notes, setNotes] = useState("")
  const [showApproveDialog, setShowApproveDialog] = useState(false)
  const [showRejectDialog, setShowRejectDialog] = useState(false)

  // Find the application by ID
  const application = ppdbApplications.find((app) => app.id === params.id)

  // If application not found, redirect to the list page
  if (!application) {
    router.push("/dashboard/ppdb")
    return null
  }

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return new Intl.DateTimeFormat("id-ID", {
      day: "2-digit",
      month: "long",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    }).format(date)
  }

  // Format date without time
  const formatDateOnly = (dateString: string) => {
    const date = new Date(dateString)
    return new Intl.DateTimeFormat("id-ID", {
      day: "2-digit",
      month: "long",
      year: "numeric",
    }).format(date)
  }

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "approved":
        return <Badge className="bg-emerald-500">Diterima</Badge>
      case "rejected":
        return <Badge className="bg-red-500">Ditolak</Badge>
      case "pending":
        return <Badge className="bg-amber-500">Menunggu</Badge>
      default:
        return <Badge className="bg-gray-500">Unknown</Badge>
    }
  }

  // Handle approve application
  const handleApprove = () => {
    // In a real application, this would update the database
    console.log(`Approving application ${application.id} with notes: ${notes}`)
    setShowApproveDialog(false)
    // Redirect back to the list after a short delay
    setTimeout(() => router.push("/dashboard/ppdb"), 1000)
  }

  // Handle reject application
  const handleReject = () => {
    // In a real application, this would update the database
    console.log(`Rejecting application ${application.id} with notes: ${notes}`)
    setShowRejectDialog(false)
    // Redirect back to the list after a short delay
    setTimeout(() => router.push("/dashboard/ppdb"), 1000)
  }

  return (
    <div className="container mx-auto p-4 md:p-6">
      <div className="mb-6">
        <Button variant="outline" onClick={() => router.push("/dashboard/ppdb")} className="gap-2">
          <ArrowLeft className="h-4 w-4" />
          Kembali ke Daftar
        </Button>
      </div>

      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-800 mb-2">Detail Pendaftaran</h1>
          <div className="flex items-center gap-2">
            <p className="text-gray-600">{application.id}</p>
            {getStatusBadge(application.status)}
          </div>
        </div>

        <div className="flex gap-3">
          {application.status === "pending" && (
            <>
              <Dialog open={showApproveDialog} onOpenChange={setShowApproveDialog}>
                <DialogTrigger asChild>
                  <Button className="gap-2 bg-emerald-600 hover:bg-emerald-700">
                    <CheckCircle className="h-4 w-4" />
                    Terima
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Terima Pendaftaran</DialogTitle>
                    <DialogDescription>
                      Anda akan menerima pendaftaran {application.name}. Tambahkan catatan jika diperlukan.
                    </DialogDescription>
                  </DialogHeader>
                  <div className="py-4">
                    <Textarea
                      placeholder="Tambahkan catatan (opsional)"
                      value={notes}
                      onChange={(e) => setNotes(e.target.value)}
                      className="min-h-[100px]"
                    />
                  </div>
                  <DialogFooter>
                    <Button variant="outline" onClick={() => setShowApproveDialog(false)}>
                      Batal
                    </Button>
                    <Button onClick={handleApprove} className="bg-emerald-600 hover:bg-emerald-700">
                      Konfirmasi Penerimaan
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>

              <Dialog open={showRejectDialog} onOpenChange={setShowRejectDialog}>
                <DialogTrigger asChild>
                  <Button
                    variant="outline"
                    className="gap-2 text-red-600 border-red-200 hover:bg-red-50 hover:text-red-700"
                  >
                    <XCircle className="h-4 w-4" />
                    Tolak
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Tolak Pendaftaran</DialogTitle>
                    <DialogDescription>
                      Anda akan menolak pendaftaran {application.name}. Harap berikan alasan penolakan.
                    </DialogDescription>
                  </DialogHeader>
                  <div className="py-4">
                    <Textarea
                      placeholder="Alasan penolakan"
                      value={notes}
                      onChange={(e) => setNotes(e.target.value)}
                      className="min-h-[100px]"
                    />
                  </div>
                  <DialogFooter>
                    <Button variant="outline" onClick={() => setShowRejectDialog(false)}>
                      Batal
                    </Button>
                    <Button onClick={handleReject} variant="destructive">
                      Konfirmasi Penolakan
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </>
          )}

          <Button variant="outline" className="gap-2">
            <Download className="h-4 w-4" />
            Unduh PDF
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Informasi Pendaftaran</CardTitle>
              <CardDescription>Detail informasi pendaftaran santri</CardDescription>
            </CardHeader>
            <CardContent>
              <Tabs defaultValue="personal" className="w-full">
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="personal">Data Pribadi</TabsTrigger>
                  <TabsTrigger value="parent">Data Orang Tua</TabsTrigger>
                  <TabsTrigger value="education">Pendidikan</TabsTrigger>
                </TabsList>

                <TabsContent value="personal" className="mt-6 space-y-6">
                  <div className="flex flex-col md:flex-row gap-6">
                    <div className="md:w-1/3 flex justify-center">
                      <div className="relative w-48 h-64 rounded-lg overflow-hidden border border-gray-200">
                        <Image
                          src="/placeholder.svg?height=320&width=240"
                          alt={application.name}
                          fill
                          className="object-cover"
                        />
                      </div>
                    </div>
                    <div className="md:w-2/3 space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <p className="text-sm text-gray-500">Nama Lengkap</p>
                          <p className="font-medium">{application.name}</p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-500">Jenis Kelamin</p>
                          <p className="font-medium">{application.gender === "male" ? "Laki-laki" : "Perempuan"}</p>
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <p className="text-sm text-gray-500">Tempat Lahir</p>
                          <p className="font-medium">{application.birthPlace}</p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-500">Tanggal Lahir</p>
                          <p className="font-medium">{formatDateOnly(application.birthDate)}</p>
                        </div>
                      </div>

                      <div>
                        <p className="text-sm text-gray-500">Alamat</p>
                        <p className="font-medium">{application.address}</p>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <p className="text-sm text-gray-500">Email</p>
                          <p className="font-medium">{application.email}</p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-500">Nomor Telepon</p>
                          <p className="font-medium">{application.phone}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="parent" className="mt-6 space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-gray-500">Nama Orang Tua/Wali</p>
                      <p className="font-medium">{application.parentName}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Nomor Telepon</p>
                      <p className="font-medium">{application.parentPhone}</p>
                    </div>
                  </div>

                  <div>
                    <p className="text-sm text-gray-500">Email</p>
                    <p className="font-medium">{application.parentEmail}</p>
                  </div>

                  <div>
                    <p className="text-sm text-gray-500">Alamat</p>
                    <p className="font-medium">{application.address}</p>
                  </div>
                </TabsContent>

                <TabsContent value="education" className="mt-6 space-y-4">
                  <div>
                    <p className="text-sm text-gray-500">Asal Sekolah</p>
                    <p className="font-medium">{application.previousSchool}</p>
                  </div>

                  <div>
                    <p className="text-sm text-gray-500">Hafalan Al-Qur'an</p>
                    <p className="font-medium">{application.quranMemo}</p>
                  </div>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Dokumen Pendaftaran</CardTitle>
              <CardDescription>Dokumen yang telah diunggah</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center gap-3">
                    <FileText className="h-5 w-5 text-gray-500" />
                    <span>Pas Foto</span>
                  </div>
                  <div>
                    {application.documents.photo ? (
                      <Badge className="bg-emerald-100 text-emerald-800 hover:bg-emerald-200">
                        <Check className="h-3 w-3 mr-1" /> Lengkap
                      </Badge>
                    ) : (
                      <Badge variant="outline" className="text-red-500 border-red-200">
                        <X className="h-3 w-3 mr-1" /> Belum Lengkap
                      </Badge>
                    )}
                  </div>
                </div>

                <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center gap-3">
                    <FileText className="h-5 w-5 text-gray-500" />
                    <span>Akta Kelahiran</span>
                  </div>
                  <div>
                    {application.documents.birthCertificate ? (
                      <Badge className="bg-emerald-100 text-emerald-800 hover:bg-emerald-200">
                        <Check className="h-3 w-3 mr-1" /> Lengkap
                      </Badge>
                    ) : (
                      <Badge variant="outline" className="text-red-500 border-red-200">
                        <X className="h-3 w-3 mr-1" /> Belum Lengkap
                      </Badge>
                    )}
                  </div>
                </div>

                <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center gap-3">
                    <FileText className="h-5 w-5 text-gray-500" />
                    <span>Kartu Keluarga</span>
                  </div>
                  <div>
                    {application.documents.familyCard ? (
                      <Badge className="bg-emerald-100 text-emerald-800 hover:bg-emerald-200">
                        <Check className="h-3 w-3 mr-1" /> Lengkap
                      </Badge>
                    ) : (
                      <Badge variant="outline" className="text-red-500 border-red-200">
                        <X className="h-3 w-3 mr-1" /> Belum Lengkap
                      </Badge>
                    )}
                  </div>
                </div>

                <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center gap-3">
                    <FileText className="h-5 w-5 text-gray-500" />
                    <span>Ijazah/Surat Keterangan Lulus</span>
                  </div>
                  <div>
                    {application.documents.schoolCertificate ? (
                      <Badge className="bg-emerald-100 text-emerald-800 hover:bg-emerald-200">
                        <Check className="h-3 w-3 mr-1" /> Lengkap
                      </Badge>
                    ) : (
                      <Badge variant="outline" className="text-red-500 border-red-200">
                        <X className="h-3 w-3 mr-1" /> Belum Lengkap
                      </Badge>
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Status Pendaftaran</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col items-center gap-4 py-4">
                {application.status === "pending" && <Clock className="h-16 w-16 text-amber-500" />}
                {application.status === "approved" && <CheckCircle className="h-16 w-16 text-emerald-500" />}
                {application.status === "rejected" && <XCircle className="h-16 w-16 text-red-500" />}

                <div className="text-center">
                  <h3 className="text-xl font-bold mb-1">
                    {application.status === "pending" && "Menunggu Verifikasi"}
                    {application.status === "approved" && "Pendaftaran Diterima"}
                    {application.status === "rejected" && "Pendaftaran Ditolak"}
                  </h3>
                  <p className="text-gray-500">
                    {application.status === "pending" && "Pendaftaran sedang dalam proses verifikasi"}
                    {application.status === "approved" && "Pendaftaran telah disetujui"}
                    {application.status === "rejected" && "Pendaftaran tidak disetujui"}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Informasi Tambahan</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <p className="text-sm text-gray-500">Tanggal Pendaftaran</p>
                <p className="font-medium">{formatDate(application.submittedAt)}</p>
              </div>

              <div>
                <p className="text-sm text-gray-500">Gelombang</p>
                <p className="font-medium">{application.batch}</p>
              </div>

              <div>
                <p className="text-sm text-gray-500">Catatan</p>
                <p className="font-medium">{application.notes || "-"}</p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Kontak</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <Button variant="outline" className="w-full gap-2">
                <Mail className="h-4 w-4" />
                Email Pendaftar
              </Button>

              <Button variant="outline" className="w-full gap-2">
                <Phone className="h-4 w-4" />
                Hubungi via WhatsApp
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
