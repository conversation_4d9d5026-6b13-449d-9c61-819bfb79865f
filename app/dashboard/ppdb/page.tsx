"use client"

import { useState } from "react"
import { useRout<PERSON> } from "next/navigation"
import {
  Eye,
  Filter,
  Search,
  Download,
  CheckCircle,
  XCircle,
  Clock,
  ToggleLeft,
  ToggleRight,
  AlertCircle,
} from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { DropdownMenu, DropdownMenuContent, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Switch } from "@/components/ui/switch"
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"

// Sample PPDB applications data
const ppdbApplications = [
  {
    id: "PPDB-2024-001",
    name: "<PERSON>",
    email: "<EMAIL>",
    phone: "081234567890",
    parentName: "Budi Santoso",
    submittedAt: "2024-03-15T08:30:00",
    status: "pending",
    gender: "male",
    previousSchool: "SD Negeri 1 Ungaran",
    documents: {
      photo: true,
      birthCertificate: true,
      familyCard: true,
      schoolCertificate: false,
    },
    batch: "Gelombang 1",
    notes: "",
  },
  {
    id: "PPDB-2024-002",
    name: "Fatimah Azzahra",
    email: "<EMAIL>",
    phone: "085678901234",
    parentName: "Ahmad Hidayat",
    submittedAt: "2024-03-16T10:15:00",
    status: "approved",
    gender: "female",
    previousSchool: "MI Nurul Iman",
    documents: {
      photo: true,
      birthCertificate: true,
      familyCard: true,
      schoolCertificate: true,
    },
    batch: "Gelombang 1",
    notes: "Hafal 2 juz Al-Qur'an",
  },
  {
    id: "PPDB-2024-003",
    name: "Muhammad Rizki",
    email: "<EMAIL>",
    phone: "089876543210",
    parentName: "Hasan Basri",
    submittedAt: "2024-03-17T14:45:00",
    status: "rejected",
    gender: "male",
    previousSchool: "SD Islam Al-Azhar",
    documents: {
      photo: true,
      birthCertificate: true,
      familyCard: false,
      schoolCertificate: true,
    },
    batch: "Gelombang 1",
    notes: "Dokumen tidak lengkap",
  },
  {
    id: "PPDB-2024-004",
    name: "Aisyah Putri",
    email: "<EMAIL>",
    phone: "087654321098",
    parentName: "Siti Aminah",
    submittedAt: "2024-03-18T09:20:00",
    status: "pending",
    gender: "female",
    previousSchool: "SD Muhammadiyah 3",
    documents: {
      photo: true,
      birthCertificate: true,
      familyCard: true,
      schoolCertificate: true,
    },
    batch: "Gelombang 1",
    notes: "",
  },
  {
    id: "PPDB-2024-005",
    name: "Abdullah Malik",
    email: "<EMAIL>",
    phone: "082345678901",
    parentName: "Malik Ibrahim",
    submittedAt: "2024-03-19T11:30:00",
    status: "approved",
    gender: "male",
    previousSchool: "MI Darussalam",
    documents: {
      photo: true,
      birthCertificate: true,
      familyCard: true,
      schoolCertificate: true,
    },
    batch: "Gelombang 1",
    notes: "Hafal 3 juz Al-Qur'an",
  },
  {
    id: "PPDB-2024-006",
    name: "Zahra Nabila",
    email: "<EMAIL>",
    phone: "083456789012",
    parentName: "Nabila Putri",
    submittedAt: "2024-03-20T13:45:00",
    status: "pending",
    gender: "female",
    previousSchool: "SD Islam Terpadu",
    documents: {
      photo: true,
      birthCertificate: false,
      familyCard: true,
      schoolCertificate: true,
    },
    batch: "Gelombang 1",
    notes: "Menunggu dokumen akta kelahiran",
  },
]

export default function PPDBManagementPage() {
  const router = useRouter()
  const [searchQuery, setSearchQuery] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [batchFilter, setBatchFilter] = useState("all")
  const [isPPDBActive, setIsPPDBActive] = useState(true)

  // Filter applications based on search query and filters
  const filteredApplications = ppdbApplications.filter((app) => {
    const matchesSearch =
      app.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      app.id.toLowerCase().includes(searchQuery.toLowerCase()) ||
      app.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
      app.parentName.toLowerCase().includes(searchQuery.toLowerCase())

    const matchesStatus = statusFilter === "all" || app.status === statusFilter
    const matchesBatch = batchFilter === "all" || app.batch === batchFilter

    return matchesSearch && matchesStatus && matchesBatch
  })

  // Get unique batches for filtering
  const batches = Array.from(new Set(ppdbApplications.map((app) => app.batch)))

  // Handle PPDB menu toggle
  const handlePPDBToggle = (value: boolean) => {
    setIsPPDBActive(value)
    // In a real application, this would update a database setting
    console.log(`PPDB menu is now ${value ? "active" : "inactive"}`)
  }

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "approved":
        return <Badge className="bg-emerald-500">Diterima</Badge>
      case "rejected":
        return <Badge className="bg-red-500">Ditolak</Badge>
      case "pending":
        return <Badge className="bg-amber-500">Menunggu</Badge>
      default:
        return <Badge className="bg-gray-500">Unknown</Badge>
    }
  }

  // Get status icon
  const getStatusIcon = (status: string) => {
    switch (status) {
      case "approved":
        return <CheckCircle className="h-5 w-5 text-emerald-500" />
      case "rejected":
        return <XCircle className="h-5 w-5 text-red-500" />
      case "pending":
        return <Clock className="h-5 w-5 text-amber-500" />
      default:
        return <AlertCircle className="h-5 w-5 text-gray-500" />
    }
  }

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return new Intl.DateTimeFormat("id-ID", {
      day: "2-digit",
      month: "long",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    }).format(date)
  }

  return (
    <div className="container mx-auto p-4 md:p-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-800 mb-2">Manajemen PPDB</h1>
        <p className="text-gray-600">Kelola pendaftaran santri baru dan pengaturan menu PPDB</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle>Total Pendaftar</CardTitle>
            <CardDescription>Jumlah total pendaftar PPDB</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-4xl font-bold text-emerald-600">{ppdbApplications.length}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle>Status Pendaftaran</CardTitle>
            <CardDescription>Ringkasan status pendaftaran</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex justify-between items-center">
              <div className="text-center">
                <div className="text-2xl font-bold text-amber-500">
                  {ppdbApplications.filter((app) => app.status === "pending").length}
                </div>
                <div className="text-sm text-gray-500">Menunggu</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-emerald-500">
                  {ppdbApplications.filter((app) => app.status === "approved").length}
                </div>
                <div className="text-sm text-gray-500">Diterima</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-red-500">
                  {ppdbApplications.filter((app) => app.status === "rejected").length}
                </div>
                <div className="text-sm text-gray-500">Ditolak</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle>Status Menu PPDB</CardTitle>
            <CardDescription>Aktifkan atau nonaktifkan menu PPDB</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                {isPPDBActive ? (
                  <ToggleRight className="h-6 w-6 text-emerald-500" />
                ) : (
                  <ToggleLeft className="h-6 w-6 text-gray-400" />
                )}
                <span className={isPPDBActive ? "text-emerald-600 font-medium" : "text-gray-500"}>
                  {isPPDBActive ? "Menu PPDB Aktif" : "Menu PPDB Nonaktif"}
                </span>
              </div>
              <Switch checked={isPPDBActive} onCheckedChange={handlePPDBToggle} />
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="all" className="w-full">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
          <TabsList className="grid w-full md:w-auto grid-cols-3">
            <TabsTrigger value="all">Semua</TabsTrigger>
            <TabsTrigger value="pending">Menunggu</TabsTrigger>
            <TabsTrigger value="processed">Diproses</TabsTrigger>
          </TabsList>

          <div className="flex flex-col sm:flex-row w-full md:w-auto gap-3">
            <div className="relative flex-grow">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <Input
                type="text"
                placeholder="Cari nama, ID, atau email..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 py-2 bg-gray-50 border-gray-200"
              />
            </div>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" className="gap-2 whitespace-nowrap">
                  <Filter className="h-4 w-4" />
                  <span className="hidden sm:inline">Filter</span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-56">
                <div className="p-2">
                  <p className="text-sm font-medium mb-2">Status</p>
                  <Select value={statusFilter} onValueChange={setStatusFilter}>
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Pilih Status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">Semua Status</SelectItem>
                      <SelectItem value="pending">Menunggu</SelectItem>
                      <SelectItem value="approved">Diterima</SelectItem>
                      <SelectItem value="rejected">Ditolak</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="p-2 border-t">
                  <p className="text-sm font-medium mb-2">Gelombang</p>
                  <Select value={batchFilter} onValueChange={setBatchFilter}>
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Pilih Gelombang" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">Semua Gelombang</SelectItem>
                      {batches.map((batch) => (
                        <SelectItem key={batch} value={batch}>
                          {batch}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </DropdownMenuContent>
            </DropdownMenu>

            <Button variant="outline" className="gap-2 whitespace-nowrap">
              <Download className="h-4 w-4" />
              <span className="hidden sm:inline">Export</span>
            </Button>
          </div>
        </div>

        <TabsContent value="all" className="mt-0">
          <Card>
            <CardContent className="p-0">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>ID Pendaftaran</TableHead>
                    <TableHead>Nama</TableHead>
                    <TableHead className="hidden md:table-cell">Orang Tua</TableHead>
                    <TableHead className="hidden md:table-cell">Tanggal Daftar</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="text-right">Aksi</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredApplications.length > 0 ? (
                    filteredApplications.map((application) => (
                      <TableRow key={application.id}>
                        <TableCell className="font-medium">{application.id}</TableCell>
                        <TableCell>
                          <div>
                            <div className="font-medium">{application.name}</div>
                            <div className="text-sm text-gray-500">{application.email}</div>
                          </div>
                        </TableCell>
                        <TableCell className="hidden md:table-cell">{application.parentName}</TableCell>
                        <TableCell className="hidden md:table-cell">{formatDate(application.submittedAt)}</TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            {getStatusIcon(application.status)}
                            <span className="hidden md:inline">{getStatusBadge(application.status)}</span>
                          </div>
                        </TableCell>
                        <TableCell className="text-right">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => router.push(`/dashboard/ppdb/${application.id}`)}
                            className="gap-1"
                          >
                            <Eye className="h-4 w-4" />
                            <span className="hidden md:inline">Detail</span>
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-8 text-gray-500">
                        Tidak ada data pendaftaran yang ditemukan
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="pending" className="mt-0">
          <Card>
            <CardContent className="p-0">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>ID Pendaftaran</TableHead>
                    <TableHead>Nama</TableHead>
                    <TableHead className="hidden md:table-cell">Orang Tua</TableHead>
                    <TableHead className="hidden md:table-cell">Tanggal Daftar</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="text-right">Aksi</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredApplications
                    .filter((app) => app.status === "pending")
                    .map((application) => (
                      <TableRow key={application.id}>
                        <TableCell className="font-medium">{application.id}</TableCell>
                        <TableCell>
                          <div>
                            <div className="font-medium">{application.name}</div>
                            <div className="text-sm text-gray-500">{application.email}</div>
                          </div>
                        </TableCell>
                        <TableCell className="hidden md:table-cell">{application.parentName}</TableCell>
                        <TableCell className="hidden md:table-cell">{formatDate(application.submittedAt)}</TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            {getStatusIcon(application.status)}
                            <span className="hidden md:inline">{getStatusBadge(application.status)}</span>
                          </div>
                        </TableCell>
                        <TableCell className="text-right">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => router.push(`/dashboard/ppdb/${application.id}`)}
                            className="gap-1"
                          >
                            <Eye className="h-4 w-4" />
                            <span className="hidden md:inline">Detail</span>
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="processed" className="mt-0">
          <Card>
            <CardContent className="p-0">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>ID Pendaftaran</TableHead>
                    <TableHead>Nama</TableHead>
                    <TableHead className="hidden md:table-cell">Orang Tua</TableHead>
                    <TableHead className="hidden md:table-cell">Tanggal Daftar</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="text-right">Aksi</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredApplications
                    .filter((app) => app.status === "approved" || app.status === "rejected")
                    .map((application) => (
                      <TableRow key={application.id}>
                        <TableCell className="font-medium">{application.id}</TableCell>
                        <TableCell>
                          <div>
                            <div className="font-medium">{application.name}</div>
                            <div className="text-sm text-gray-500">{application.email}</div>
                          </div>
                        </TableCell>
                        <TableCell className="hidden md:table-cell">{application.parentName}</TableCell>
                        <TableCell className="hidden md:table-cell">{formatDate(application.submittedAt)}</TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            {getStatusIcon(application.status)}
                            <span className="hidden md:inline">{getStatusBadge(application.status)}</span>
                          </div>
                        </TableCell>
                        <TableCell className="text-right">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => router.push(`/dashboard/ppdb/${application.id}`)}
                            className="gap-1"
                          >
                            <Eye className="h-4 w-4" />
                            <span className="hidden md:inline">Detail</span>
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
