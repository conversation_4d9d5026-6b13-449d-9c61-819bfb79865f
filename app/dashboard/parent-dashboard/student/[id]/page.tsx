"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { ArrowLeft, Calendar, BookOpen, Award, CheckCircle2 } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { students } from "@/data/students"
import { cn } from "@/lib/utils"

// Helper function to get grade color
const getGradeColor = (grade: string) => {
  switch (grade) {
    case "Jayyid Jiddan":
      return "text-emerald-100 bg-emerald-800/50"
    case "Jayyid":
      return "text-emerald-100 bg-emerald-700/50"
    case "Maqbul":
      return "text-emerald-100 bg-emerald-600/50"
    default:
      return "text-emerald-100 bg-emerald-500/50"
  }
}

// Sample monthly reports data
const monthlyReports = [
  {
    month: "Januari 2024",
    achievements: {
      alQuran: {
        value: "Juz 1 ayat 1-50",
        grade: "Jayyid",
        notes: "Hafalan lancar, perlu perbaikan pada tajwid",
      },
      haditsArbain: {
        value: "Hadits 1-5",
        grade: "Jayyid Jiddan",
        notes: "Hafalan sangat baik dengan pemahaman yang baik",
      },
      attendance: "95%",
      behavior: "Sangat Baik",
      activities: ["Mengikuti lomba tahfidz tingkat kecamatan", "Menjadi imam shalat Dzuhur"],
    },
  },
  {
    month: "Februari 2024",
    achievements: {
      alQuran: {
        value: "Juz 1 ayat 51-100",
        grade: "Jayyid",
        notes: "Hafalan cukup lancar, tajwid sudah lebih baik",
      },
      haditsArbain: {
        value: "Hadits 6-10",
        grade: "Jayyid",
        notes: "Hafalan baik, perlu peningkatan pada pemahaman makna",
      },
      attendance: "90%",
      behavior: "Baik",
      activities: ["Mengikuti kajian kitab mingguan", "Berpartisipasi dalam kegiatan bakti sosial"],
    },
  },
  {
    month: "Maret 2024",
    achievements: {
      alQuran: {
        value: "Juz 1 ayat 101-150",
        grade: "Jayyid Jiddan",
        notes: "Hafalan sangat lancar dengan tajwid yang baik",
      },
      haditsArbain: {
        value: "Hadits 11-15",
        grade: "Jayyid Jiddan",
        notes: "Hafalan dan pemahaman sangat baik",
      },
      attendance: "98%",
      behavior: "Sangat Baik",
      activities: ["Menjadi ketua kelompok belajar", "Membantu mengajar santri junior"],
    },
  },
  {
    month: "April 2024",
    achievements: {
      alQuran: {
        value: "Juz 1 ayat 151-200",
        grade: "Maqbul",
        notes: "Hafalan cukup, perlu lebih banyak muroja'ah",
      },
      haditsArbain: {
        value: "Hadits 16-20",
        grade: "Jayyid",
        notes: "Hafalan baik, pemahaman meningkat",
      },
      attendance: "92%",
      behavior: "Baik",
      activities: ["Mengikuti pelatihan public speaking", "Berpartisipasi dalam program tahfidz intensif"],
    },
  },
]

export default function StudentDetailPage({ params }: { params: { id: string } }) {
  const router = useRouter()
  const [mounted, setMounted] = useState(false)

  // Find the student by ID
  const student = students.find((s) => s.id === params.id)

  // Handle hydration
  useEffect(() => {
    setMounted(true)
  }, [])

  // If student not found, redirect back to dashboard
  if (!student && mounted) {
    router.push("/parent-dashboard")
    return null
  }

  if (!mounted || !student) return null

  return (
    <div className="min-h-screen bg-gray-50">
      <header className="bg-gradient-to-r from-emerald-700 to-emerald-800 text-white p-4 shadow-lg">
        <div className="container mx-auto">
          <Button
            variant="ghost"
            className="text-white hover:bg-white/10 -ml-2 mb-4"
            onClick={() => router.push("/parent-dashboard")}
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Kembali ke Dashboard
          </Button>
          <div className="flex flex-col md:flex-row gap-6 items-center md:items-start">
            <Avatar className="h-24 w-24 border-4 border-white/20">
              <AvatarImage src={student.photo} alt={student.name} />
              <AvatarFallback className="text-2xl">{student.name.charAt(0)}</AvatarFallback>
            </Avatar>
            <div>
              <h1 className="text-3xl font-bold">{student.name}</h1>
              <p className="text-emerald-100">{student.batch}</p>
              <div className="flex flex-wrap gap-2 mt-2">
                <Badge className="bg-white/20 hover:bg-white/30">Santri Aktif</Badge>
                <Badge className="bg-white/20 hover:bg-white/30">Tahfidz</Badge>
              </div>
            </div>
          </div>
        </div>
      </header>

      <main className="container mx-auto p-4 md:p-6">
        <Tabs defaultValue="profile" className="w-full">
          <TabsList className="grid w-full md:w-auto grid-cols-2">
            <TabsTrigger value="profile" className="flex items-center gap-2">
              <BookOpen className="h-4 w-4" />
              <span>Profil & Pencapaian</span>
            </TabsTrigger>
            <TabsTrigger value="timeline" className="flex items-center gap-2">
              <Calendar className="h-4 w-4" />
              <span>Timeline Bulanan</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="profile" className="mt-6">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <div className="lg:col-span-1">
                <Card>
                  <CardHeader>
                    <CardTitle>Informasi Santri</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <p className="text-sm text-gray-500">Nama Lengkap</p>
                      <p className="font-medium">{student.name}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Angkatan</p>
                      <p className="font-medium">{student.batch}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Status</p>
                      <p className="font-medium">Aktif</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Pembimbing Tahfidz</p>
                      <p className="font-medium">Ustadz Ahmad Fauzi</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Wali Kelas</p>
                      <p className="font-medium">Ustadz Muhammad Rizki</p>
                    </div>
                  </CardContent>
                </Card>
              </div>

              <div className="lg:col-span-2">
                <Card>
                  <CardHeader>
                    <CardTitle>Pencapaian Santri</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      <AchievementCard
                        title="Hafalan Al Quran"
                        icon="📖"
                        value={student.achievements.alQuran.value}
                        grade={student.achievements.alQuran.grade}
                      />

                      <AchievementCard
                        title="Hafalan Hadits Arbain"
                        icon="📖"
                        value={student.achievements.haditsArbain.value}
                        grade={student.achievements.haditsArbain.grade}
                      />

                      <AchievementCard
                        title="Hafalan Ushul Tsalatsah"
                        icon="📖"
                        value={student.achievements.ushulTsalatsah.value}
                        grade={student.achievements.ushulTsalatsah.grade}
                      />

                      <AchievementCard
                        title="Hafalan Ghoyah Wa Taqrib"
                        icon="📖"
                        value={student.achievements.ghoyahWaTaqrib.value}
                        grade={student.achievements.ghoyahWaTaqrib.grade}
                      />

                      <AchievementCard
                        title="Hafalan Al Ajurumiyyah"
                        icon="📖"
                        value={student.achievements.alAjurumiyyah.value}
                        grade={student.achievements.alAjurumiyyah.grade}
                      />

                      <AchievementCard
                        title="Kemampuan Baca Kitab Gundul"
                        icon="📜"
                        value={student.achievements.kitabGundul.value}
                        grade={student.achievements.kitabGundul.grade}
                      />
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="timeline" className="mt-6">
            <Card>
              <CardHeader>
                <CardTitle>Laporan Perkembangan Bulanan</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="relative border-l-2 border-emerald-500 pl-6 ml-4">
                  {monthlyReports.map((report, index) => (
                    <MonthlyReportItem
                      key={report.month}
                      report={report}
                      isLast={index === monthlyReports.length - 1}
                    />
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </main>

      <footer className="bg-gray-100 border-t py-6 mt-12">
        <div className="container mx-auto text-center text-gray-500 text-sm">
          &copy; {new Date().getFullYear()} PTQ Al Ihsan. Hak Cipta Dilindungi.
        </div>
      </footer>
    </div>
  )
}

interface AchievementCardProps {
  title: string
  icon: string
  value: string
  grade: string
  className?: string
}

function AchievementCard({ title, icon, value, grade, className }: AchievementCardProps) {
  return (
    <div className={cn("group", className)}>
      <p className="text-center text-gray-700 mb-2 font-medium">{title}</p>
      <div className="bg-emerald-50 rounded-xl p-4 shadow-sm border border-emerald-100 transition-all duration-300 group-hover:bg-emerald-100 group-hover:translate-y-[-3px]">
        <div className="absolute -top-3 left-1/2 transform -translate-x-1/2 bg-gradient-to-br from-emerald-400 to-emerald-600 rounded-full p-1 w-8 h-8 flex items-center justify-center shadow-lg border-2 border-white">
          <span className="text-sm">{icon}</span>
        </div>
        <div className="text-center pt-2 mt-2">
          <h3 className="text-xl md:text-2xl font-bold text-gray-800 mb-2">{value}</h3>
          <Badge className={cn("font-medium px-3 py-1", getGradeColor(grade))}>{grade}</Badge>
        </div>
      </div>
    </div>
  )
}

interface MonthlyReportItemProps {
  report: (typeof monthlyReports)[0]
  isLast: boolean
}

function MonthlyReportItem({ report, isLast }: MonthlyReportItemProps) {
  return (
    <div className={cn("mb-8", isLast ? "" : "pb-8")}>
      <div className="absolute -left-4 mt-1.5">
        <div className="bg-emerald-500 text-white rounded-full h-7 w-7 flex items-center justify-center">
          <Calendar className="h-4 w-4" />
        </div>
      </div>

      <h3 className="text-xl font-bold text-gray-800 mb-3">{report.month}</h3>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-base">Hafalan Al-Qur'an</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex justify-between items-center mb-2">
              <p className="font-medium">{report.achievements.alQuran.value}</p>
              <Badge className={cn("font-medium", getGradeColor(report.achievements.alQuran.grade))}>
                {report.achievements.alQuran.grade}
              </Badge>
            </div>
            <p className="text-sm text-gray-600">{report.achievements.alQuran.notes}</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-base">Hafalan Hadits</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex justify-between items-center mb-2">
              <p className="font-medium">{report.achievements.haditsArbain.value}</p>
              <Badge className={cn("font-medium", getGradeColor(report.achievements.haditsArbain.grade))}>
                {report.achievements.haditsArbain.grade}
              </Badge>
            </div>
            <p className="text-sm text-gray-600">{report.achievements.haditsArbain.notes}</p>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-100">
          <h4 className="font-medium mb-2 flex items-center gap-2">
            <CheckCircle2 className="h-4 w-4 text-emerald-500" />
            Kehadiran
          </h4>
          <p className="text-lg font-bold text-emerald-600">{report.achievements.attendance}</p>
        </div>

        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-100">
          <h4 className="font-medium mb-2 flex items-center gap-2">
            <Award className="h-4 w-4 text-emerald-500" />
            Perilaku
          </h4>
          <p className="text-lg font-bold text-emerald-600">{report.achievements.behavior}</p>
        </div>
      </div>

      <div className="mt-4 bg-white p-4 rounded-lg shadow-sm border border-gray-100">
        <h4 className="font-medium mb-2">Aktivitas & Prestasi</h4>
        <ul className="list-disc pl-5 space-y-1">
          {report.achievements.activities.map((activity, index) => (
            <li key={index} className="text-gray-700">
              {activity}
            </li>
          ))}
        </ul>
      </div>
    </div>
  )
}
