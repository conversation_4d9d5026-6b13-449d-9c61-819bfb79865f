import { Skeleton } from "@/components/ui/skeleton"
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card"

export default function Loading() {
  return (
    <div className="min-h-screen bg-gray-50">
      <header className="bg-gradient-to-r from-emerald-700 to-emerald-800 text-white p-4 shadow-lg">
        <div className="container mx-auto">
          <Skeleton className="h-10 w-32 bg-white/20 mb-4" />
          <div className="flex flex-col md:flex-row gap-6 items-center md:items-start">
            <Skeleton className="h-24 w-24 rounded-full bg-white/20" />
            <div>
              <Skeleton className="h-8 w-48 bg-white/20 mb-2" />
              <Skeleton className="h-5 w-32 bg-white/20 mb-2" />
              <div className="flex gap-2 mt-2">
                <Skeleton className="h-6 w-24 rounded-full bg-white/20" />
                <Skeleton className="h-6 w-20 rounded-full bg-white/20" />
              </div>
            </div>
          </div>
        </div>
      </header>

      <main className="container mx-auto p-4 md:p-6">
        <Skeleton className="h-10 w-full md:w-64 mb-6" />

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-1">
            <Card>
              <CardHeader>
                <Skeleton className="h-6 w-32" />
              </CardHeader>
              <CardContent className="space-y-4">
                {[1, 2, 3, 4, 5].map((i) => (
                  <div key={i}>
                    <Skeleton className="h-4 w-24 mb-1" />
                    <Skeleton className="h-5 w-40" />
                  </div>
                ))}
              </CardContent>
            </Card>
          </div>

          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <Skeleton className="h-6 w-40" />
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  {[1, 2, 3, 4, 5, 6].map((i) => (
                    <div key={i} className="space-y-2">
                      <Skeleton className="h-5 w-32 mx-auto" />
                      <div className="bg-gray-100 rounded-xl p-4">
                        <Skeleton className="h-8 w-8 rounded-full mx-auto mb-4" />
                        <Skeleton className="h-6 w-20 mx-auto mb-2" />
                        <Skeleton className="h-5 w-16 rounded-full mx-auto" />
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
    </div>
  )
}
