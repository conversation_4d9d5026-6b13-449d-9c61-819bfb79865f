"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { User, GraduationCap } from "lucide-react"
import { students } from "@/data/students"

export default function ParentDashboard() {
  const router = useRouter()
  const [mounted, setMounted] = useState(false)

  // Simulate that we're getting only the students associated with this parent
  // In a real app, this would come from an API call based on the parent's ID
  const myStudents = students.slice(0, 2) // Just use the first 2 students as an example

  // Handle hydration
  useState(() => {
    setMounted(true)
  })

  if (!mounted) return null

  return (
    <div className="min-h-screen bg-gray-50">
      <header className="bg-gradient-to-r from-emerald-700 to-emerald-800 text-white p-4 shadow-lg">
        <div className="container mx-auto flex flex-col md:flex-row justify-between items-center">
          <div className="flex items-center gap-3">
            <GraduationCap className="h-8 w-8" />
            <h1 className="text-2xl font-bold mb-4 md:mb-0">PTQ Al Ihsan - Portal Orang Tua</h1>
          </div>
          <div className="flex items-center gap-3">
            <Avatar className="border-2 border-white/20">
              <AvatarImage src="/placeholder.svg?height=40&width=40" alt="Avatar" />
              <AvatarFallback>OT</AvatarFallback>
            </Avatar>
            <div>
              <span className="font-medium">Ahmad Hidayat</span>
              <p className="text-xs text-white/70">Orang Tua Santri</p>
            </div>
          </div>
        </div>
      </header>

      <main className="container mx-auto p-4 md:p-6">
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-gray-800 mb-4">Santri Saya</h2>
          <p className="text-gray-600 mb-6">Pilih santri untuk melihat profil dan laporan perkembangan</p>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {myStudents.map((student) => (
              <Card
                key={student.id}
                className="cursor-pointer hover:shadow-lg transition-all duration-300 hover:border-emerald-200 hover:translate-y-[-2px] overflow-hidden group"
                onClick={() => router.push(`/parent-dashboard/student/${student.id}`)}
              >
                <div className="h-2 bg-emerald-600 group-hover:bg-emerald-500 transition-colors" />
                <CardContent className="p-6">
                  <div className="flex items-center gap-4">
                    <Avatar className="h-16 w-16 border-2 border-emerald-100">
                      <AvatarImage src={student.photo} alt={student.name} />
                      <AvatarFallback>
                        <User />
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <h3 className="font-semibold text-lg">{student.name}</h3>
                      <p className="text-sm text-gray-500">{student.batch}</p>
                      <Button
                        variant="outline"
                        size="sm"
                        className="mt-2 text-emerald-600 border-emerald-200 hover:bg-emerald-50 hover:text-emerald-700"
                      >
                        Lihat Detail
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-md p-6 mb-8">
          <h3 className="text-xl font-bold text-gray-800 mb-4">Pengumuman Terbaru</h3>
          <div className="space-y-4">
            <div className="border-l-4 border-emerald-500 pl-4 py-1">
              <p className="font-medium">Jadwal Ujian Tahfidz Semester Genap</p>
              <p className="text-sm text-gray-600">Ujian Tahfidz akan dilaksanakan pada tanggal 15-20 Juni 2024</p>
              <p className="text-xs text-gray-500 mt-1">10 Mei 2024</p>
            </div>
            <div className="border-l-4 border-emerald-500 pl-4 py-1">
              <p className="font-medium">Pertemuan Orang Tua dan Guru</p>
              <p className="text-sm text-gray-600">Akan diadakan pada hari Sabtu, 25 Mei 2024 pukul 09.00 WIB</p>
              <p className="text-xs text-gray-500 mt-1">5 Mei 2024</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-md p-6">
          <h3 className="text-xl font-bold text-gray-800 mb-4">Kalender Kegiatan</h3>
          <div className="space-y-3">
            <div className="flex items-center gap-3">
              <div className="bg-emerald-100 text-emerald-800 h-12 w-12 rounded-lg flex items-center justify-center font-bold">
                25 Mei
              </div>
              <div>
                <p className="font-medium">Pertemuan Orang Tua dan Guru</p>
                <p className="text-sm text-gray-600">09.00 - 12.00 WIB</p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <div className="bg-emerald-100 text-emerald-800 h-12 w-12 rounded-lg flex items-center justify-center font-bold">
                15 Jun
              </div>
              <div>
                <p className="font-medium">Ujian Tahfidz Dimulai</p>
                <p className="text-sm text-gray-600">08.00 - 15.00 WIB</p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <div className="bg-emerald-100 text-emerald-800 h-12 w-12 rounded-lg flex items-center justify-center font-bold">
                30 Jun
              </div>
              <div>
                <p className="font-medium">Pembagian Rapor Semester Genap</p>
                <p className="text-sm text-gray-600">09.00 - 12.00 WIB</p>
              </div>
            </div>
          </div>
        </div>
      </main>

      <footer className="bg-gray-100 border-t py-6 mt-12">
        <div className="container mx-auto text-center text-gray-500 text-sm">
          &copy; {new Date().getFullYear()} PTQ Al Ihsan. Hak Cipta Dilindungi.
        </div>
      </footer>
    </div>
  )
}
