// Server Component
import { Suspense } from "react"
import { getStudents, getClasses, getParents } from "@/lib/supabase-crud"
import { createServerClient } from "@/utils/supabase/server"
import { Skeleton } from "@/components/ui/skeleton"
import StudentList<PERSON>ith<PERSON>rud from "@/components/dashboard/student-list-with-crud"
import { ParentOption } from "@/components/dashboard/parent-selector"

// Loading fallback
function StudentsSkeleton() {
  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center mb-6">
        <Skeleton className="h-8 w-32" />
        <Skeleton className="h-10 w-32" />
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {[...Array(6)].map((_, i) => (
          <Skeleton key={i} className="h-24 w-full" />
        ))}
      </div>
    </div>
  )
}

// This page shows a list of students with CRUD functionality
export default async function StudentsPage() {
  const supabase = await createServerClient()
  const { data: { session } } = await supabase.auth.getSession()
  
  if (!session) {
    return (
      <div className="container mx-auto p-4">
        <div className="bg-red-100 p-4 rounded-md text-red-800">
          Anda harus login untuk mengakses halaman ini.
        </div>
      </div>
    )
  }
  
  // Check user role from session metadata
  const userRole = session?.user?.app_metadata?.role || 'user'
  const isAdmin = userRole === 'admin'
  const isTeacher = userRole === 'teacher'
  const isAdminOrTeacher = isAdmin || isTeacher
  
  // Get user's parent ID if they are a parent
  const { data: parentData } = await supabase
    .from("parents")
    .select("id")
    .eq("profile_id", session.user.id)
    .single()
  
  // Allow access if the user is a parent, admin, or teacher
  if (!parentData && !isAdminOrTeacher) {
    return (
      <div className="container mx-auto p-4">
        <div className="bg-yellow-100 p-4 rounded-md text-yellow-800">
          Anda tidak memiliki akses ke halaman ini. Silakan hubungi administrator untuk mendapatkan akses.
        </div>
      </div>
    )
  }
  
  // Fetch students, classes and parents
  const [students, classes, parents] = await Promise.all([
    getStudents(),
    getClasses(),
    getParents(100) // Fetch up to 100 parents
  ])
  
  const classOptions = classes.map(c => ({
    id: c.id,
    name: c.name
  }))
  
  const parentOptions: ParentOption[] = parents.map(p => ({
    id: p.id,
    name: p.name,
    email: p.email,
    phone: p.phone
  }))
  
  return (
    <div className="container mx-auto p-4">
      <Suspense fallback={<StudentsSkeleton />}>
        <StudentListWithCrud 
          initialStudents={students} 
          classOptions={classOptions}
          parentOptions={parentOptions}
          parentId={parentData?.id}
        />
      </Suspense>
    </div>
  )
}