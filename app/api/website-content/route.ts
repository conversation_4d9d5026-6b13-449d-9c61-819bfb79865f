import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

const createServerClient = () => {
  return createClient(supabaseUrl, supabaseKey)
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const section = searchParams.get('section')
    const key = searchParams.get('key')

    const supabase = createServerClient()

    if (key && section) {
      // Get specific content by section and key
      const { data: content, error } = await supabase
        .from('website_content')
        .select(`
          *,
          website_images (
            id,
            key,
            file_path,
            alt_text,
            caption
          )
        `)
        .eq('section', section)
        .eq('key', key)
        .single()

      if (error && error.code !== 'PGRST116') {
        throw error
      }

      return NextResponse.json({ content })
    }

    // Build query
    let query = supabase
      .from('website_content')
      .select(`
        *,
        website_images (
          id,
          key,
          file_path,
          alt_text,
          caption
        )
      `)
      .eq('is_active', true)
      .order('order_index')

    if (section) {
      query = query.eq('section', section)
    }

    const { data: content, error } = await query

    if (error) {
      throw error
    }

    // Group content by section
    const groupedContent = content?.reduce((acc: any, item) => {
      const sect = item.section
      if (!acc[sect]) {
        acc[sect] = []
      }
      acc[sect].push(item)
      return acc
    }, {})

    return NextResponse.json({ 
      content: content || [],
      grouped: groupedContent || {}
    })

  } catch (error: any) {
    console.error('Error fetching website content:', error)
    return NextResponse.json(
      { error: error.message || 'Failed to fetch website content' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { 
      section, 
      key, 
      title, 
      content, 
      image_id, 
      order_index = 0, 
      is_active = true, 
      metadata 
    } = body

    if (!section || !key) {
      return NextResponse.json(
        { error: 'Section and key are required' },
        { status: 400 }
      )
    }

    const supabase = createServerClient()

    const { data: contentRecord, error } = await supabase
      .from('website_content')
      .upsert({
        section,
        key,
        title,
        content,
        image_id,
        order_index,
        is_active,
        metadata
      }, {
        onConflict: 'section,key'
      })
      .select(`
        *,
        website_images (
          id,
          key,
          file_path,
          alt_text,
          caption
        )
      `)
      .single()

    if (error) {
      throw error
    }

    return NextResponse.json({ content: contentRecord })

  } catch (error: any) {
    console.error('Error saving website content:', error)
    return NextResponse.json(
      { error: error.message || 'Failed to save website content' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { content } = body

    if (!Array.isArray(content)) {
      return NextResponse.json(
        { error: 'Content must be an array' },
        { status: 400 }
      )
    }

    const supabase = createServerClient()

    // Update multiple content items
    const updates = content.map(item => ({
      section: item.section,
      key: item.key,
      title: item.title,
      content: item.content,
      image_id: item.image_id,
      order_index: item.order_index || 0,
      is_active: item.is_active !== undefined ? item.is_active : true,
      metadata: item.metadata
    }))

    const { data, error } = await supabase
      .from('website_content')
      .upsert(updates, {
        onConflict: 'section,key'
      })
      .select(`
        *,
        website_images (
          id,
          key,
          file_path,
          alt_text,
          caption
        )
      `)

    if (error) {
      throw error
    }

    return NextResponse.json({ 
      message: 'Content updated successfully',
      content: data 
    })

  } catch (error: any) {
    console.error('Error updating website content:', error)
    return NextResponse.json(
      { error: error.message || 'Failed to update website content' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const section = searchParams.get('section')
    const key = searchParams.get('key')

    if (!section || !key) {
      return NextResponse.json(
        { error: 'Section and key are required' },
        { status: 400 }
      )
    }

    const supabase = createServerClient()

    const { error } = await supabase
      .from('website_content')
      .delete()
      .eq('section', section)
      .eq('key', key)

    if (error) {
      throw error
    }

    return NextResponse.json({ 
      message: 'Content deleted successfully' 
    })

  } catch (error: any) {
    console.error('Error deleting website content:', error)
    return NextResponse.json(
      { error: error.message || 'Failed to delete website content' },
      { status: 500 }
    )
  }
}
