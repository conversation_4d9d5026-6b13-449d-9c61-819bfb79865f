import { NextResponse } from 'next/server'
import { createServerClient } from '@/utils/supabase/server'

export async function GET() {
  try {
    const supabase = await createServerClient()
    
    // Check blog posts
    const { data: posts, error: postsError } = await supabase
      .from('blog_posts')
      .select('*')
    
    // Check blog categories
    const { data: categories, error: categoriesError } = await supabase
      .from('blog_categories')
      .select('*')
    
    // Check post-category relationships
    const { data: relations, error: relationsError } = await supabase
      .from('post_categories')
      .select('*')
    
    // Check profiles (authors)
    const { data: profiles, error: profilesError } = await supabase
      .from('profiles')
      .select('*')
    
    return NextResponse.json({
      blog_posts: {
        count: posts?.length || 0,
        data: posts,
        error: postsError
      },
      blog_categories: {
        count: categories?.length || 0,
        data: categories,
        error: categoriesError
      },
      post_categories: {
        count: relations?.length || 0,
        data: relations,
        error: relationsError
      },
      profiles: {
        count: profiles?.length || 0,
        data: profiles,
        error: profilesError
      }
    })
  } catch (error) {
    console.error('Test error:', error)
    return NextResponse.json({ error: error.message }, { status: 500 })
  }
}
