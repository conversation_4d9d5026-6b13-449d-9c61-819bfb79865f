import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

const createServerClient = () => {
  return createClient(supabaseUrl, supabaseKey)
}

export async function GET(request: NextRequest) {
  try {
    const supabase = createServerClient()

    // Get students data
    const { data: students, error: studentsError } = await supabase
      .from('students')
      .select(`
        id,
        name,
        batch,
        status,
        created_at,
        updated_at
      `)

    if (studentsError) {
      console.error('Error fetching students:', studentsError)
    }

    // Get classes data
    const { data: classes, error: classesError } = await supabase
      .from('classes')
      .select(`
        id,
        name,
        level,
        academic_year,
        created_at
      `)

    if (classesError) {
      console.error('Error fetching classes:', classesError)
    }

    // Get timeline details for performance metrics
    const { data: timelineData, error: timelineError } = await supabase
      .from('timeline_details')
      .select(`
        id,
        timeline_id,
        subject_id,
        value,
        grade,
        created_at,
        subject:subjects (
          id,
          name
        ),
        timeline_entry:timeline_entries (
          student_id,
          month,
          year,
          student:students (
            id,
            name,
            batch
          )
        )
      `)
      .gte('created_at', new Date(Date.now() - 90 * 24 * 60 * 60 * 1000).toISOString()) // Last 3 months

    if (timelineError) {
      console.error('Error fetching timeline details:', timelineError)
    }

    // Get achievements data
    const { data: achievements, error: achievementsError } = await supabase
      .from('achievements')
      .select(`
        id,
        student_id,
        subject_id,
        value,
        grade,
        notes,
        created_at,
        subject:subjects (
          id,
          name
        ),
        student:students (
          id,
          name,
          batch
        )
      `)
      .gte('created_at', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()) // Last 30 days
      .order('created_at', { ascending: false })
      .limit(20)

    if (achievementsError) {
      console.error('Error fetching achievements:', achievementsError)
    }

    // Calculate statistics
    const totalStudents = students?.length || 0
    const totalClasses = classes?.length || 0
    const activeStudents = students?.filter(s => s.status === 'active').length || 0
    const graduatedStudents = students?.filter(s => s.status === 'graduated').length || 0

    // Calculate class distribution
    const classDistribution = classes?.map(cls => {
      const studentCount = students?.filter(s => s.batch === cls.name).length || 0
      const defaultCapacity = 30 // Default capacity since we don't have this field in database
      return {
        name: cls.name,
        studentCount,
        capacity: defaultCapacity,
        percentage: Math.round((studentCount / defaultCapacity) * 100)
      }
    }) || []

    // Calculate performance metrics from timeline data
    const performanceMetrics = calculatePerformanceMetrics(timelineData || [])

    // Generate recent activities from achievements and timeline
    const recentActivities = generateRecentActivities(achievements || [], timelineData || [])

    // Calculate monthly progress
    const monthlyProgress = calculateMonthlyProgress(students || [], achievements || [], timelineData || [])

    return NextResponse.json({
      statistics: {
        totalStudents,
        totalClasses,
        activeStudents,
        graduatedStudents
      },
      classDistribution,
      performanceMetrics,
      recentActivities,
      monthlyProgress
    })

  } catch (error: any) {
    console.error('Error fetching dashboard stats:', error)
    return NextResponse.json(
      { error: error.message || 'Failed to fetch dashboard statistics' },
      { status: 500 }
    )
  }
}

// Helper function to calculate performance metrics
function calculatePerformanceMetrics(timelineData: any[]) {
  const subjectStats: { [key: string]: { grades: string[], name: string } } = {}

  timelineData.forEach(item => {
    if (item.subject && item.grade) {
      const subjectName = item.subject.name
      if (!subjectStats[subjectName]) {
        subjectStats[subjectName] = { grades: [], name: subjectName }
      }
      subjectStats[subjectName].grades.push(item.grade)
    }
  })

  return Object.values(subjectStats).map(subject => {
    // Convert grades to numeric scores for calculation
    const scores = subject.grades.map(grade => gradeToScore(grade))
    const average = scores.length > 0
      ? Math.round(scores.reduce((sum, score) => sum + score, 0) / scores.length)
      : 0

    return {
      subject: subject.name,
      average,
      target: getTargetScore(subject.name),
      students: subject.grades.length
    }
  })
}

// Helper function to convert grade to numeric score
function gradeToScore(grade: string): number {
  const gradeMap: { [key: string]: number } = {
    'Jayyid Jiddan': 90,
    'Jayyid': 80,
    'Maqbul': 70,
    'A': 90,
    'B+': 85,
    'B': 80,
    'C+': 75,
    'C': 70,
    'D': 60
  }
  return gradeMap[grade] || 70
}

// Helper function to get target scores
function getTargetScore(subjectName: string): number {
  const targets: { [key: string]: number } = {
    'Tahfidz Al-Qur\'an': 80,
    'Hadits Arbain': 75,
    'Fiqih': 80,
    'Public Speaking': 70,
    'Aqidah': 75,
    'Nahwu': 70
  }
  return targets[subjectName] || 75
}

// Helper function to generate recent activities
function generateRecentActivities(achievements: any[], timelineData: any[]) {
  const activities: any[] = []

  // Add achievement activities
  achievements.slice(0, 10).forEach(achievement => {
    activities.push({
      type: 'achievement',
      message: `${achievement.student?.name} mencapai ${achievement.subject?.name}: ${achievement.value}`,
      time: formatTimeAgo(achievement.created_at),
      user: 'Sistem'
    })
  })

  // Add recent timeline updates
  const recentTimeline = timelineData
    .filter(item => item.created_at && item.timeline_entry?.student)
    .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
    .slice(0, 5)

  recentTimeline.forEach(item => {
    activities.push({
      type: 'timeline_updated',
      message: `Nilai ${item.subject?.name} untuk ${item.timeline_entry?.student?.name} diperbarui`,
      time: formatTimeAgo(item.created_at),
      user: 'Guru'
    })
  })

  // Sort by time and return latest 15
  return activities
    .sort((a, b) => {
      const timeA = parseTimeAgo(a.time)
      const timeB = parseTimeAgo(b.time)
      return timeA - timeB
    })
    .slice(0, 15)
}

// Helper function to calculate monthly progress
function calculateMonthlyProgress(students: any[], achievements: any[], timelineData: any[]) {
  const currentMonth = new Date().getMonth() + 1
  const currentYear = new Date().getFullYear()

  // Calculate new achievements this month
  const thisMonthAchievements = achievements.filter(a => {
    const date = new Date(a.created_at)
    return date.getMonth() + 1 === currentMonth && date.getFullYear() === currentYear
  })

  // Calculate new hafalan (memorization achievements)
  const hafalanAchievements = thisMonthAchievements.filter(a =>
    a.subject?.name?.toLowerCase().includes('quran') ||
    a.subject?.name?.toLowerCase().includes('tahfidz') ||
    a.value?.toLowerCase().includes('juz')
  )

  const totalHafalanJuz = hafalanAchievements.reduce((sum, a) => {
    const match = a.value?.match(/(\d+)/)
    return sum + (match ? parseInt(match[1]) : 0)
  }, 0)

  // Calculate completed targets this month
  const completedTargets = thisMonthAchievements.filter(a =>
    a.value?.toLowerCase().includes('selesai') ||
    a.grade === 'Jayyid Jiddan'
  ).length

  // Calculate attendance rate (mock calculation)
  const attendanceRate = 94 // This would come from actual attendance data

  return {
    hafalanBaru: `+${totalHafalanJuz} Juz`,
    pencapaianSelesai: `+${completedTargets} Target`,
    santriAktif: students.filter(s => s.status === 'active').length,
    tingkatKehadiran: `${attendanceRate}%`
  }
}

// Helper function to format time ago
function formatTimeAgo(dateString: string): string {
  const date = new Date(dateString)
  const now = new Date()
  const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))

  if (diffInMinutes < 60) {
    return `${diffInMinutes} menit yang lalu`
  } else if (diffInMinutes < 1440) {
    const hours = Math.floor(diffInMinutes / 60)
    return `${hours} jam yang lalu`
  } else {
    const days = Math.floor(diffInMinutes / 1440)
    return `${days} hari yang lalu`
  }
}

// Helper function to parse time ago for sorting
function parseTimeAgo(timeString: string): number {
  const match = timeString.match(/(\d+)\s+(menit|jam|hari)/)
  if (!match) return 0

  const value = parseInt(match[1])
  const unit = match[2]

  switch (unit) {
    case 'menit': return value
    case 'jam': return value * 60
    case 'hari': return value * 1440
    default: return 0
  }
}
