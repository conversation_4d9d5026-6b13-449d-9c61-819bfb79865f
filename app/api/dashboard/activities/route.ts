import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

const createServerClient = () => {
  return createClient(supabaseUrl, supabaseKey)
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const limit = parseInt(searchParams.get('limit') || '20')
    const days = parseInt(searchParams.get('days') || '7')

    const supabase = createServerClient()
    const cutoffDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000).toISOString()

    // Get recent student additions
    const { data: newStudents, error: studentsError } = await supabase
      .from('students')
      .select('id, name, batch, created_at')
      .gte('created_at', cutoffDate)
      .order('created_at', { ascending: false })
      .limit(10)

    if (studentsError) {
      console.error('Error fetching new students:', studentsError)
    }

    // Get recent achievements
    const { data: achievements, error: achievementsError } = await supabase
      .from('achievements')
      .select(`
        id,
        achievement_type,
        achievement_value,
        notes,
        created_at,
        students (
          id,
          name,
          batch
        )
      `)
      .gte('created_at', cutoffDate)
      .order('created_at', { ascending: false })
      .limit(15)

    if (achievementsError) {
      console.error('Error fetching achievements:', achievementsError)
    }

    // Get recent timeline updates
    const { data: timelineUpdates, error: timelineError } = await supabase
      .from('timeline')
      .select(`
        id,
        month,
        year,
        score,
        created_at,
        updated_at,
        students (
          id,
          name,
          batch
        ),
        subjects (
          id,
          name
        )
      `)
      .gte('updated_at', cutoffDate)
      .order('updated_at', { ascending: false })
      .limit(15)

    if (timelineError) {
      console.error('Error fetching timeline updates:', timelineError)
    }

    // Get recent parent registrations (if student_parent table exists)
    const { data: parentRegistrations, error: parentError } = await supabase
      .from('student_parent')
      .select(`
        id,
        created_at,
        students (
          id,
          name,
          batch
        ),
        parents (
          id,
          name
        )
      `)
      .gte('created_at', cutoffDate)
      .order('created_at', { ascending: false })
      .limit(10)

    if (parentError) {
      console.error('Error fetching parent registrations:', parentError)
    }

    // Combine all activities
    const activities: any[] = []

    // Add student additions
    newStudents?.forEach(student => {
      activities.push({
        id: `student_${student.id}`,
        type: 'student_added',
        message: `Santri baru ${student.name} ditambahkan ke batch ${student.batch}`,
        time: formatTimeAgo(student.created_at),
        timestamp: student.created_at,
        user: 'Admin',
        data: student
      })
    })

    // Add achievements
    achievements?.forEach(achievement => {
      activities.push({
        id: `achievement_${achievement.id}`,
        type: 'achievement',
        message: `${achievement.students?.name} mencapai ${achievement.achievement_type}: ${achievement.achievement_value}`,
        time: formatTimeAgo(achievement.created_at),
        timestamp: achievement.created_at,
        user: 'Ustadz',
        data: achievement
      })
    })

    // Add timeline updates
    timelineUpdates?.forEach(update => {
      activities.push({
        id: `timeline_${update.id}`,
        type: 'timeline_updated',
        message: `Nilai ${update.subjects?.name} untuk ${update.students?.name} diperbarui (${update.score}/100)`,
        time: formatTimeAgo(update.updated_at),
        timestamp: update.updated_at,
        user: 'Guru',
        data: update
      })
    })

    // Add parent registrations
    parentRegistrations?.forEach(registration => {
      activities.push({
        id: `parent_${registration.id}`,
        type: 'parent_registered',
        message: `Orang tua ${registration.parents?.name} terdaftar untuk santri ${registration.students?.name}`,
        time: formatTimeAgo(registration.created_at),
        timestamp: registration.created_at,
        user: 'Sistem',
        data: registration
      })
    })

    // Sort by timestamp and limit
    const sortedActivities = activities
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
      .slice(0, limit)

    return NextResponse.json({
      activities: sortedActivities,
      total: activities.length
    })

  } catch (error: any) {
    console.error('Error fetching dashboard activities:', error)
    return NextResponse.json(
      { error: error.message || 'Failed to fetch dashboard activities' },
      { status: 500 }
    )
  }
}

// Helper function to format time ago
function formatTimeAgo(dateString: string): string {
  const date = new Date(dateString)
  const now = new Date()
  const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))

  if (diffInMinutes < 1) {
    return 'Baru saja'
  } else if (diffInMinutes < 60) {
    return `${diffInMinutes} menit yang lalu`
  } else if (diffInMinutes < 1440) {
    const hours = Math.floor(diffInMinutes / 60)
    return `${hours} jam yang lalu`
  } else if (diffInMinutes < 10080) {
    const days = Math.floor(diffInMinutes / 1440)
    return `${days} hari yang lalu`
  } else {
    const weeks = Math.floor(diffInMinutes / 10080)
    return `${weeks} minggu yang lalu`
  }
}

// POST endpoint to add new activity (for real-time updates)
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { type, message, user, data } = body

    // This could be used to log custom activities
    // For now, we'll just return success
    return NextResponse.json({
      success: true,
      message: 'Activity logged successfully'
    })

  } catch (error: any) {
    console.error('Error logging activity:', error)
    return NextResponse.json(
      { error: error.message || 'Failed to log activity' },
      { status: 500 }
    )
  }
}
