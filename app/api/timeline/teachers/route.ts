import { NextRequest, NextResponse } from "next/server"
import { createServerClient } from "@/utils/supabase/server"

// GET - Get teachers for timeline input (for admin to select)
export async function GET(request: NextRequest) {
  try {
    const supabase = await createServerClient()
    const { data: { session } } = await supabase.auth.getSession()
    
    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    // Check user role
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', session.user.id)
      .single()

    if (profileError || !profile) {
      return NextResponse.json(
        { error: "Profile not found" },
        { status: 404 }
      )
    }

    // Only admin and teachers can access this
    if (profile.role !== 'admin' && profile.role !== 'teacher') {
      return NextResponse.json(
        { error: "Access denied" },
        { status: 403 }
      )
    }

    // Get all active teachers
    const { data: teachers, error: teachersError } = await supabase
      .from('teachers')
      .select(`
        id,
        teacher_id,
        name,
        specialization,
        status,
        profiles (
          full_name,
          role
        )
      `)
      .eq('status', 'active')
      .order('name')

    if (teachersError) {
      return NextResponse.json(
        { error: teachersError.message },
        { status: 500 }
      )
    }

    // Add option for "No specific teacher" for admin
    const teacherOptions = [
      {
        id: null,
        teacher_id: 'ADMIN',
        name: 'Admin Input (Tidak ada guru spesifik)',
        specialization: 'Administrator',
        status: 'active'
      },
      ...(teachers || [])
    ]

    return NextResponse.json({ teachers: teacherOptions })

  } catch (error: any) {
    console.error('Error fetching teachers for timeline:', error)
    return NextResponse.json(
      { error: error.message || "Failed to fetch teachers" },
      { status: 500 }
    )
  }
}
