import { NextRequest, NextResponse } from "next/server"
import { createServerClient } from "@/utils/supabase/server"
import * as XLSX from 'xlsx'

// GET - Generate Excel template for timeline import
export async function GET(request: NextRequest) {
  try {
    const supabase = await createServerClient()
    const { data: { session } } = await supabase.auth.getSession()
    
    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const classId = searchParams.get('class_id')

    if (!classId) {
      return NextResponse.json(
        { error: "class_id is required" },
        { status: 400 }
      )
    }

    // Get students in the class
    const { data: classStudents, error: classError } = await supabase
      .from('class_students')
      .select(`
        student_id,
        students (
          id,
          student_id,
          name
        )
      `)
      .eq('class_id', classId)

    if (classError) {
      return NextResponse.json(
        { error: classError.message },
        { status: 500 }
      )
    }

    // Get all subjects
    const { data: subjects, error: subjectsError } = await supabase
      .from('subjects')
      .select('id, name, category')
      .order('category', { ascending: true })
      .order('name', { ascending: true })

    if (subjectsError) {
      return NextResponse.json(
        { error: subjectsError.message },
        { status: 500 }
      )
    }

    // Get class info
    const { data: classInfo, error: classInfoError } = await supabase
      .from('classes')
      .select('name, class_id')
      .eq('id', classId)
      .single()

    if (classInfoError) {
      return NextResponse.json(
        { error: classInfoError.message },
        { status: 500 }
      )
    }

    // Create Excel workbook
    const workbook = XLSX.utils.book_new()

    // Create headers
    const headers = [
      'Nama Siswa',
      'ID Siswa',
      ...subjects?.map(subject => subject.name) || [],
      'Aktivitas',
      'Perilaku',
      'Catatan Perilaku'
    ]

    // Create template data with students
    const templateData = classStudents?.map(cs => {
      const student = cs.students
      const row: any = {
        'Nama Siswa': student.name,
        'ID Siswa': student.student_id,
        'Aktivitas': '',
        'Perilaku': '',
        'Catatan Perilaku': ''
      }

      // Add empty columns for each subject
      subjects?.forEach(subject => {
        row[subject.name] = ''
      })

      return row
    }) || []

    // Create example row
    const exampleRow: any = {
      'Nama Siswa': 'Contoh: Ahmad Fauzi',
      'ID Siswa': 'Contoh: STD-001',
      'Aktivitas': 'Contoh: Lomba tahfidz, Kajian rutin',
      'Perilaku': 'Contoh: Sangat Baik',
      'Catatan Perilaku': 'Contoh: Aktif dalam kegiatan'
    }

    // Add example values for subjects
    subjects?.forEach((subject, index) => {
      if (index === 0) {
        exampleRow[subject.name] = 'A|Juz 1 ayat 1-50|Hafalan lancar'
      } else if (index === 1) {
        exampleRow[subject.name] = 'B+|Hadits 1-5|Perlu latihan'
      } else {
        exampleRow[subject.name] = 'A-|Materi lengkap|Baik'
      }
    })

    // Add example row at the top
    const finalData = [exampleRow, ...templateData]

    // Create worksheet
    const worksheet = XLSX.utils.json_to_sheet(finalData)

    // Set column widths
    const colWidths = [
      { wch: 20 }, // Nama Siswa
      { wch: 15 }, // ID Siswa
      ...subjects?.map(() => ({ wch: 25 })) || [], // Subjects
      { wch: 30 }, // Aktivitas
      { wch: 15 }, // Perilaku
      { wch: 25 }  // Catatan Perilaku
    ]
    worksheet['!cols'] = colWidths

    // Add worksheet to workbook
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Timeline Data')

    // Create instructions sheet
    const instructions = [
      ['PETUNJUK PENGGUNAAN TEMPLATE IMPORT TIMELINE'],
      [''],
      ['1. FORMAT DATA:'],
      ['   - Jangan mengubah header kolom'],
      ['   - Hapus baris contoh sebelum import'],
      ['   - Pastikan nama siswa sesuai dengan data di sistem'],
      [''],
      ['2. FORMAT NILAI MATA PELAJARAN:'],
      ['   Format: NILAI|PENCAPAIAN|CATATAN'],
      ['   Contoh: A|Juz 1 ayat 1-50|Hafalan lancar'],
      ['   - NILAI: A+, A, A-, B+, B, B-, C+, C, C-, D, E'],
      ['   - PENCAPAIAN: Deskripsi pencapaian siswa'],
      ['   - CATATAN: Catatan tambahan (opsional)'],
      [''],
      ['3. FORMAT AKTIVITAS:'],
      ['   Pisahkan dengan koma untuk multiple aktivitas'],
      ['   Contoh: Lomba tahfidz, Kajian rutin, Piket kelas'],
      [''],
      ['4. FORMAT PERILAKU:'],
      ['   Pilihan: Sangat Baik, Baik, Cukup, Perlu Perbaikan'],
      [''],
      ['5. TIPS:'],
      ['   - Gunakan copy-paste untuk mempercepat input'],
      ['   - Periksa data sebelum import'],
      ['   - Backup data sebelum import massal'],
      [''],
      ['MATA PELAJARAN YANG TERSEDIA:'],
      ...subjects?.map(subject => [`   - ${subject.name} (${subject.category})`]) || []
    ]

    const instructionsSheet = XLSX.utils.aoa_to_sheet(instructions)
    instructionsSheet['!cols'] = [{ wch: 60 }]
    XLSX.utils.book_append_sheet(workbook, instructionsSheet, 'Petunjuk')

    // Generate Excel buffer
    const excelBuffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' })

    // Create filename
    const filename = `Template_Timeline_${classInfo?.class_id || 'Class'}_${new Date().toISOString().split('T')[0]}.xlsx`

    // Return Excel file
    return new NextResponse(excelBuffer, {
      status: 200,
      headers: {
        'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'Content-Disposition': `attachment; filename="${filename}"`,
      },
    })

  } catch (error: any) {
    console.error('Error generating template:', error)
    return NextResponse.json(
      { error: error.message || "Failed to generate template" },
      { status: 500 }
    )
  }
}
