import { NextRequest, NextResponse } from "next/server"
import { createServerClient } from "@/utils/supabase/server"

// GET - Fetch all active subjects
export async function GET(request: NextRequest) {
  try {
    const supabase = await createServerClient()
    const { data: { session } } = await supabase.auth.getSession()
    
    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    const { data: subjects, error } = await supabase
      .from('subjects')
      .select(`
        id,
        name,
        category,
        description
      `)
      .order('category', { ascending: true })
      .order('name', { ascending: true })

    if (error) {
      console.error('Error fetching subjects:', error)
      return NextResponse.json(
        { error: error.message },
        { status: 500 }
      )
    }

    // Group subjects by category
    const groupedSubjects = subjects?.reduce((acc, subject) => {
      const category = subject.category || 'Lainnya'
      if (!acc[category]) {
        acc[category] = []
      }
      acc[category].push(subject)
      return acc
    }, {} as Record<string, any[]>) || {}

    return NextResponse.json({ 
      subjects: subjects || [],
      grouped_subjects: groupedSubjects
    })

  } catch (error: any) {
    console.error('Error in subjects GET:', error)
    return NextResponse.json(
      { error: error.message || "Failed to fetch subjects" },
      { status: 500 }
    )
  }
}
