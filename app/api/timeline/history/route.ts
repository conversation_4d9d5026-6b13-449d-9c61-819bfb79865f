import { NextRequest, NextResponse } from "next/server"
import { createServerClient } from "@/utils/supabase/server"

// GET - Get timeline history with filters
export async function GET(request: NextRequest) {
  try {
    const supabase = await createServerClient()
    const { data: { session } } = await supabase.auth.getSession()
    
    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const classId = searchParams.get('class_id')
    const month = searchParams.get('month')
    const year = searchParams.get('year')
    const teacherId = searchParams.get('teacher_id')
    const studentId = searchParams.get('student_id')
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const offset = (page - 1) * limit

    // Build query for timeline entries with filters
    let query = supabase
      .from('timeline_entries')
      .select(`
        id,
        student_id,
        teacher_id,
        month,
        year,
        created_at,
        updated_at,
        students (
          id,
          student_id,
          name,
          class_students (
            class_id,
            classes (
              id,
              name,
              class_id
            )
          )
        ),
        teachers (
          id,
          name,
          employee_id
        ),
        timeline_details (
          id,
          subject_id,
          value,
          grade,
          notes,
          subjects (
            id,
            name,
            category
          )
        ),
        timeline_activities (
          id,
          activity
        ),
        behavior_records (
          id,
          behavior_grade,
          notes
        )
      `)
      .order('created_at', { ascending: false })

    // Apply filters
    if (classId) {
      // Filter by class through student relationship
      const { data: classStudents } = await supabase
        .from('class_students')
        .select('student_id')
        .eq('class_id', classId)
      
      if (classStudents && classStudents.length > 0) {
        const studentIds = classStudents.map(cs => cs.student_id)
        query = query.in('student_id', studentIds)
      } else {
        // No students in this class
        return NextResponse.json({
          timeline_entries: [],
          total_count: 0,
          page,
          limit,
          total_pages: 0
        })
      }
    }

    if (month) {
      query = query.eq('month', month)
    }

    if (year) {
      query = query.eq('year', parseInt(year))
    }

    if (teacherId) {
      query = query.eq('teacher_id', teacherId)
    }

    if (studentId) {
      query = query.eq('student_id', studentId)
    }

    // Get total count for pagination
    const { count } = await query.select('*', { count: 'exact', head: true })

    // Get paginated data
    const { data: timelineEntries, error } = await query
      .range(offset, offset + limit - 1)

    if (error) {
      console.error('Error fetching timeline history:', error)
      return NextResponse.json(
        { error: error.message },
        { status: 500 }
      )
    }

    // Process data to add metadata
    const processedEntries = timelineEntries?.map(entry => {
      const student = entry.students
      const teacher = entry.teachers
      const classInfo = student?.class_students?.[0]?.classes

      return {
        ...entry,
        student_name: student?.name,
        student_id_display: student?.student_id,
        teacher_name: teacher?.name,
        teacher_employee_id: teacher?.employee_id,
        class_name: classInfo?.name,
        class_id_display: classInfo?.class_id,
        subjects_count: entry.timeline_details?.length || 0,
        activities_count: entry.timeline_activities?.length || 0,
        has_behavior: !!entry.behavior_records?.[0],
        input_type: entry.created_at === entry.updated_at ? 'manual' : 'updated',
        last_modified: entry.updated_at
      }
    }) || []

    const totalPages = Math.ceil((count || 0) / limit)

    return NextResponse.json({
      timeline_entries: processedEntries,
      total_count: count || 0,
      page,
      limit,
      total_pages: totalPages,
      filters: {
        class_id: classId,
        month,
        year,
        teacher_id: teacherId,
        student_id: studentId
      }
    })

  } catch (error: any) {
    console.error('Error in timeline history:', error)
    return NextResponse.json(
      { error: error.message || "Failed to fetch timeline history" },
      { status: 500 }
    )
  }
}

// GET statistics for timeline history
export async function POST(request: NextRequest) {
  try {
    const supabase = await createServerClient()
    const { data: { session } } = await supabase.auth.getSession()
    
    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { action, filters } = body

    if (action === 'get_statistics') {
      // Get timeline statistics
      let query = supabase
        .from('timeline_entries')
        .select('id, created_at, updated_at, teacher_id, month, year')

      // Apply filters if provided
      if (filters?.class_id) {
        const { data: classStudents } = await supabase
          .from('class_students')
          .select('student_id')
          .eq('class_id', filters.class_id)
        
        if (classStudents && classStudents.length > 0) {
          const studentIds = classStudents.map(cs => cs.student_id)
          query = query.in('student_id', studentIds)
        }
      }

      if (filters?.month) query = query.eq('month', filters.month)
      if (filters?.year) query = query.eq('year', parseInt(filters.year))
      if (filters?.teacher_id) query = query.eq('teacher_id', filters.teacher_id)

      const { data: entries, error } = await query

      if (error) {
        return NextResponse.json({ error: error.message }, { status: 500 })
      }

      // Calculate statistics
      const totalEntries = entries?.length || 0
      const manualEntries = entries?.filter(e => e.created_at === e.updated_at).length || 0
      const updatedEntries = totalEntries - manualEntries

      // Group by month/year
      const monthlyStats = entries?.reduce((acc, entry) => {
        const key = `${entry.month}-${entry.year}`
        if (!acc[key]) {
          acc[key] = { month: entry.month, year: entry.year, count: 0 }
        }
        acc[key].count++
        return acc
      }, {} as Record<string, any>) || {}

      // Group by teacher
      const teacherStats = entries?.reduce((acc, entry) => {
        if (!acc[entry.teacher_id]) {
          acc[entry.teacher_id] = 0
        }
        acc[entry.teacher_id]++
        return acc
      }, {} as Record<string, number>) || {}

      return NextResponse.json({
        total_entries: totalEntries,
        manual_entries: manualEntries,
        updated_entries: updatedEntries,
        monthly_distribution: Object.values(monthlyStats),
        teacher_distribution: teacherStats
      })
    }

    return NextResponse.json({ error: "Invalid action" }, { status: 400 })

  } catch (error: any) {
    console.error('Error in timeline statistics:', error)
    return NextResponse.json(
      { error: error.message || "Failed to get timeline statistics" },
      { status: 500 }
    )
  }
}
