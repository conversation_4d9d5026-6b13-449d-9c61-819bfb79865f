import { createServerClient } from "@/utils/supabase/server"
import { NextResponse } from "next/server"
import { createStudentWithRelations, createStudentWithParents, updateStudentWithParents, updateStudent, deleteStudent } from "@/lib/supabase-crud"

// Define types for better type safety
type Class = {
  id: string;
  class_id: string;
  name: string;
  level: string;
  academic_year: string;
}



type Student = {
  id: string;
  student_id: string;
  name: string;
  gender: string;
  birth_date: string;
  address: string;
  photo_url: string;
  batch: string;
  status: string;
  user_id: string;
  classes?: Class[];
}

// GET students - this can be modified based on your access control needs
export async function GET(request: Request) {
  const supabase = await createServerClient()
  
  // Check authentication
  const { data: { session } } = await supabase.auth.getSession()
  
  if (!session) {
    return NextResponse.json(
      { error: "Unauthorized" },
      { status: 401 }
    )
  }
  
  // Get URL parameters
  const { searchParams } = new URL(request.url)
  const parentId = searchParams.get('parentId')
  const classId = searchParams.get('classId')
  const studentId = searchParams.get('id')
  
  try {
    // Handle single student request
    if (studentId) {
      const { data: student, error } = await supabase
        .from("students")
        .select(`
          id,
          student_id,
          name,
          gender,
          birth_date,
          address,
          photo_url,
          batch,
          status,
          user_id,
          created_at,
          updated_at
        `)
        .eq("id", studentId)
        .single()

      if (error) {
        console.error("Error fetching single student:", error)
        return NextResponse.json(
          { error: error.message },
          { status: 500 }
        )
      }

      return NextResponse.json({ student })
    }

    let query = supabase
      .from("students")
      .select(`
        id,
        student_id,
        name,
        gender,
        birth_date,
        address,
        photo_url,
        batch,
        status,
        user_id
      `)
      .order("name")

    const { data: students, error } = await query

    if (error) {
      console.error("Error fetching students:", error)
      // Return mock data as fallback
      const mockStudents = [
        {
          id: "mock-1",
          student_id: "STD-001",
          name: "Ahmad Fauzi",
          gender: "male",
          birth_date: "2010-01-15",
          address: "Jl. Contoh No. 123",
          photo_url: null,
          batch: "2024",
          status: "active",
          user_id: "mock-user-1",
          classes: [{ id: "class-1", name: "Kelas 7A", level: "SMP", class_id: "CLS-7A", academic_year: "2024-2025" }]
        },
        {
          id: "mock-2",
          student_id: "STD-002",
          name: "Siti Aisyah",
          gender: "female",
          birth_date: "2010-03-20",
          address: "Jl. Contoh No. 456",
          photo_url: null,
          batch: "2024",
          status: "active",
          user_id: "mock-user-2",
          classes: [{ id: "class-2", name: "Kelas 7B", level: "SMP", class_id: "CLS-7B", academic_year: "2024-2025" }]
        }
      ]
      return NextResponse.json({ students: mockStudents })
    }

    // Cast students to proper type
    const typedStudents = students as Student[];

    // If we need to filter by parent ID, we need to get the relationships first
    if (parentId) {
      const { data: studentParentRelations, error: relationError } = await supabase
        .from("student_parent")
        .select("student_id")
        .eq("parent_id", parentId);

      if (relationError) {
        return NextResponse.json(
          { error: relationError.message },
          { status: 500 }
        );
      }

      // Filter students by the IDs found in the student-parent relationship
      const studentIds = studentParentRelations.map(relation => relation.student_id);
      const filteredStudents = typedStudents?.filter(student => 
        studentIds.includes(student.id)
      ) || [];
      
      return NextResponse.json({ students: filteredStudents });
    }

    // If we need to filter by class ID, we need to get class enrollments
    if (classId) {
      const { data: classStudents, error: classError } = await supabase
        .from("class_students")
        .select("student_id")
        .eq("class_id", classId);

      if (classError) {
        return NextResponse.json(
          { error: classError.message },
          { status: 500 }
        );
      }

      // Filter students by the IDs found in the class enrollments
      const studentIds = classStudents.map(enrollment => enrollment.student_id);
      const filteredStudents = typedStudents?.filter(student => 
        studentIds.includes(student.id)
      ) || [];
      
      return NextResponse.json({ students: filteredStudents });
    }

    // Get class information for each student
    if (typedStudents && typedStudents.length > 0) {
      const studentIds = typedStudents.map(s => s.id);
      
      // Get class enrollments for these students
      const { data: classEnrollments, error: classError } = await supabase
        .from("class_students")
        .select(`
          student_id,
          class_id,
          classes:classes (
            id,
            class_id,
            name,
            level,
            academic_year
          )
        `)
        .in("student_id", studentIds);

      if (classError) {
        console.error("Error fetching class enrollments:", classError);
      } else if (classEnrollments) {
        // Create a map of student_id -> classes
        const studentClasses = new Map<string, Class[]>();
        
        classEnrollments.forEach((enrollment: any) => {
          const studentId = enrollment.student_id;
          
          if (!studentClasses.has(studentId)) {
            studentClasses.set(studentId, []);
          }
          
          // Handle the case where classes might be an object or undefined
          if (enrollment.classes) {
            studentClasses.get(studentId)?.push(enrollment.classes);
          }
        });

        // Add classes to each student
        typedStudents.forEach(student => {
          student.classes = studentClasses.get(student.id) || [];
        });
      }

      // Get parent count for each student
      const { data: parentRelations, error: parentError } = await supabase
        .from("student_parent")
        .select("student_id, parent_id, relationship")
        .in("student_id", studentIds);

      console.log("Parent relations query:", {
        parentRelations,
        parentError,
        studentIds,
        queryType: "in_filter"
      });

      // Also try a simple select all to debug
      const { data: allRelations, error: allError } = await supabase
        .from("student_parent")
        .select("student_id, parent_id, relationship");

      console.log("All parent relations in table:", { allRelations, allError });

      if (!parentError && parentRelations) {
        // Count parents for each student
        const parentCounts = new Map<string, number>();
        parentRelations.forEach((relation: any) => {
          const count = parentCounts.get(relation.student_id) || 0;
          parentCounts.set(relation.student_id, count + 1);
        });

        console.log("Parent counts:", Object.fromEntries(parentCounts));

        // Add parent count to each student
        typedStudents.forEach((student: any) => {
          student.parents_count = parentCounts.get(student.id) || 0;
        });
      } else {
        console.log("No parent relations found or error:", parentError);
        // Set default parent count to 0
        typedStudents.forEach((student: any) => {
          student.parents_count = 0;
        });
      }
    }

    return NextResponse.json({ students: typedStudents })
  } catch (error: any) {
    return NextResponse.json(
      { error: error.message || "Gagal mendapatkan data santri" },
      { status: 500 }
    )
  }
}

// CREATE a new student
export async function POST(request: Request) {
  try {
    // Authorization check
    const supabase = await createServerClient()
    const { data: { session } } = await supabase.auth.getSession()
    
    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }
    
    // Get the student data and related data from the request
    const { classId, parentId, relationship, parents, ...studentData } = await request.json()

    console.log('POST /api/students - Request data:', { classId, parentId, relationship, parents, studentData })

    // Use new multiple parents function if parents array is provided
    if (parents && Array.isArray(parents) && parents.length > 0) {
      const result = await createStudentWithParents({
        studentData,
        classId,
        parents
      })
      return NextResponse.json(result)
    }

    // Fallback to legacy single parent function for backward compatibility
    const result = await createStudentWithRelations({
      studentData,
      classId,
      parentId,
      relationship
    })
    
    return NextResponse.json(result)
  } catch (error: any) {
    return NextResponse.json(
      { error: error.message || "Gagal membuat data santri" },
      { status: 500 }
    )
  }
}

// UPDATE an existing student
export async function PUT(request: Request) {
  try {
    // Authorization check
    const supabase = await createServerClient()
    const { data: { session } } = await supabase.auth.getSession()
    
    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }
    
    // Get the student data from the request
    const { id, classId, parentId, relationship, parents, ...studentData } = await request.json()

    console.log('PUT /api/students - Request data:', { id, classId, parentId, relationship, parents, studentData })

    if (!id) {
      return NextResponse.json(
        { error: "ID santri wajib diisi" },
        { status: 400 }
      )
    }

    // Use new multiple parents function if parents array is provided
    if (parents && Array.isArray(parents)) {
      const result = await updateStudentWithParents(id, {
        studentData,
        classId,
        parents
      })
      return NextResponse.json(result)
    }

    // Fallback to legacy single parent function for backward compatibility
    const result = await updateStudent(id, studentData)
    
    if (result.success) {
      // Update class enrollment if provided
      if (classId) {
        // First check if there's an existing enrollment
        const { data: existingEnrollment } = await supabase
          .from("class_students")
          .select("id")
          .eq("student_id", id)
          .maybeSingle();
          
        if (existingEnrollment) {
          // Update existing enrollment
          const { error: updateError } = await supabase
            .from("class_students")
            .update({ class_id: classId })
            .eq("id", existingEnrollment.id);
            
          if (updateError) {
            console.error("Failed to update class enrollment:", updateError);
          }
        } else {
          // Create new enrollment
          const { error: insertError } = await supabase
            .from("class_students")
            .insert({
              class_id: classId,
              student_id: id
            });
            
          if (insertError) {
            console.error("Failed to create class enrollment:", insertError);
          }
        }
      }
      
      // Update parent relationship if provided
      if (parentId) {
        // First check if there's an existing relationship
        const { data: existingRelationship } = await supabase
          .from("student_parent")
          .select("id")
          .eq("student_id", id)
          .eq("is_primary", true)
          .maybeSingle();
          
        if (existingRelationship) {
          // Update existing relationship
          const { error: updateError } = await supabase
            .from("student_parent")
            .update({
              parent_id: parentId,
              relationship: relationship || "parent"
            })
            .eq("id", existingRelationship.id);
            
          if (updateError) {
            console.error("Failed to update parent relationship:", updateError);
          }
        } else {
          // Create new relationship
          const { error: insertError } = await supabase
            .from("student_parent")
            .insert({
              parent_id: parentId,
              student_id: id,
              relationship: relationship || "parent",
              is_primary: true
            });
            
          if (insertError) {
            console.error("Failed to create parent relationship:", insertError);
          }
        }
      }
    }
    
    return NextResponse.json(result)
  } catch (error: any) {
    return NextResponse.json(
      { error: error.message || "Gagal memperbarui data santri" },
      { status: 500 }
    )
  }
}

// DELETE a student
export async function DELETE(request: Request) {
  try {
    // Authorization check
    const supabase = await createServerClient()
    const { data: { session } } = await supabase.auth.getSession()
    
    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }
    
    // Get the ID from the URL
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')
    
    if (!id) {
      return NextResponse.json(
        { error: "ID santri wajib diisi" },
        { status: 400 }
      )
    }
    
    // With cascade delete in the database, related records in junction tables 
    // will be automatically deleted when the student is deleted
    const result = await deleteStudent(id)
    
    return NextResponse.json(result)
  } catch (error: any) {
    return NextResponse.json(
      { error: error.message || "Gagal menghapus data santri" },
      { status: 500 }
    )
  }
}