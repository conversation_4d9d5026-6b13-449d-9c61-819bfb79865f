import { createServerClient } from "@/utils/supabase/server"
import { NextResponse } from "next/server"

export async function GET(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Authorization check
    const supabase = await createServerClient()
    const { data: { session } } = await supabase.auth.getSession()

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    const { id: studentId } = await params

    if (!studentId) {
      return NextResponse.json(
        { error: "Student ID is required" },
        { status: 400 }
      )
    }

    // Verify student exists
    const { data: student, error: studentError } = await supabase
      .from('students')
      .select('id, name, student_id')
      .eq('id', studentId)
      .single()

    if (studentError || !student) {
      return NextResponse.json(
        { error: "Student not found" },
        { status: 404 }
      )
    }

    // Get timeline entries for this student
    const { data: timelineEntries, error: timelineError } = await supabase
      .from('timeline_entries')
      .select(`
        id,
        student_id,
        month,
        year,
        created_at,
        updated_at,
        timeline_details (
          id,
          subject_id,
          value,
          grade,
          notes,
          subjects (
            id,
            name,
            category
          )
        ),
        timeline_activities (
          id,
          activity
        )
      `)
      .eq('student_id', studentId)
      .order('year', { ascending: false })
      .order('month', { ascending: false })

    if (timelineError) {
      console.error('Error fetching timeline entries:', timelineError)
      return NextResponse.json(
        { error: "Failed to fetch timeline data" },
        { status: 500 }
      )
    }

    // Get behavior records for this student
    const { data: behaviorRecords, error: behaviorError } = await supabase
      .from('behavior_records')
      .select(`
        id,
        student_id,
        month,
        year,
        behavior_grade,
        notes,
        created_at
      `)
      .eq('student_id', studentId)
      .order('year', { ascending: false })
      .order('month', { ascending: false })

    if (behaviorError) {
      console.error('Error fetching behavior records:', behaviorError)
      // Don't fail the request, just log the error
    }

    // Combine timeline entries with behavior records
    const combinedTimeline = timelineEntries?.map(entry => {
      // Find matching behavior records for the same month/year
      const matchingBehavior = behaviorRecords?.filter(
        behavior => 
          behavior.month === entry.month && 
          behavior.year === entry.year
      ) || []

      return {
        ...entry,
        behavior_records: matchingBehavior
      }
    }) || []

    return NextResponse.json({
      success: true,
      student: {
        id: student.id,
        name: student.name,
        student_id: student.student_id
      },
      timeline: combinedTimeline,
      total: combinedTimeline.length
    })

  } catch (error: any) {
    console.error('Error in student timeline API:', error)
    return NextResponse.json(
      { error: error.message || "Failed to fetch student timeline" },
      { status: 500 }
    )
  }
}
