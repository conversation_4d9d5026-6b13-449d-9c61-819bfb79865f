import { createServerClient } from "@/utils/supabase/server"
import { NextResponse } from "next/server"

export async function GET(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const supabase = await createServerClient()

    // Get parents for the student
    const { data: parentRelations, error } = await supabase
      .from("student_parent")
      .select(`
        relationship,
        is_primary,
        parent_id,
        parents (
          id,
          profile_id,
          name,
          phone,
          email,
          address,
          occupation
        )
      `)
      .eq("student_id", id)

    if (error) {
      console.error("Error fetching student parents:", error)
      // Return empty array as fallback
      return NextResponse.json({ parents: [] })
    }

    // Transform the data
    const parents = parentRelations?.map((relation: any) => ({
      id: relation.parents?.id,
      profile_id: relation.parents?.profile_id,
      name: relation.parents?.name,
      phone: relation.parents?.phone,
      email: relation.parents?.email,
      address: relation.parents?.address,
      occupation: relation.parents?.occupation,
      relationship: relation.relationship,
      is_primary: relation.is_primary
    })) || []

    return NextResponse.json({ parents })
  } catch (error: any) {
    console.error("Failed to get student parents:", error)
    return NextResponse.json({ parents: [] })
  }
}

export async function DELETE(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const { searchParams } = new URL(request.url)
    const parentId = searchParams.get('parentId')

    if (!parentId) {
      return NextResponse.json(
        { error: "Parent ID is required" },
        { status: 400 }
      )
    }

    const supabase = await createServerClient()

    // Delete the parent-student relationship
    const { error } = await supabase
      .from("student_parent")
      .delete()
      .eq("student_id", id)
      .eq("parent_id", parentId)

    if (error) {
      console.error("Error removing parent relationship:", error)
      return NextResponse.json(
        { error: error.message },
        { status: 500 }
      )
    }

    return NextResponse.json({ success: true })
  } catch (error: any) {
    console.error("Failed to remove parent relationship:", error)
    return NextResponse.json(
      { error: error.message || "Failed to remove parent relationship" },
      { status: 500 }
    )
  }
}
