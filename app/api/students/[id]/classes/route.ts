import { createServerClient } from "@/utils/supabase/server"
import { NextResponse } from "next/server"

export async function GET(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const supabase = await createServerClient()

    // Get classes for the student
    const { data: classEnrollments, error } = await supabase
      .from("class_students")
      .select(`
        class_id,
        classes (
          id,
          class_id,
          name,
          level,
          academic_year,
          homeroom_teacher_id
        )
      `)
      .eq("student_id", id)

    if (error) {
      console.error("Error fetching student classes:", error)
      // Return empty array as fallback
      return NextResponse.json({ classes: [] })
    }

    // Transform the data
    const classes = classEnrollments?.map((enrollment: any) => enrollment.classes) || []

    return NextResponse.json({ classes })
  } catch (error: any) {
    console.error("Failed to get student classes:", error)
    return NextResponse.json({ classes: [] })
  }
}
