import { NextRequest, NextResponse } from "next/server"
import { createServerClient } from "@/utils/supabase/server"
import * as XLSX from 'xlsx'

// GET - Generate Excel template for students import
export async function GET(request: NextRequest) {
  try {
    const supabase = await createServerClient()
    const { data: { session } } = await supabase.auth.getSession()
    
    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    // Get available classes
    const { data: classes, error: classesError } = await supabase
      .from('classes')
      .select('id, name, class_id')
      .order('name', { ascending: true })

    if (classesError) {
      return NextResponse.json(
        { error: classesError.message },
        { status: 500 }
      )
    }

    // Create Excel workbook
    const workbook = XLSX.utils.book_new()

    // Create headers for student data
    const headers = [
      // Student Basic Info
      'ID Santri',
      '<PERSON><PERSON>',
      '<PERSON><PERSON>',
      '<PERSON><PERSON><PERSON>',
      '<PERSON><PERSON>',
      '<PERSON><PERSON><PERSON>',
      '<PERSON>',
      '<PERSON><PERSON>',
      '<PERSON><PERSON>',
      '<PERSON>',
      '<PERSON><PERSON>',
      
      // Father In<PERSON>
      '<PERSON>a Ayah',
      'No HP Ayah',
      'Email Ayah',
      'Pekerjaan Ayah',
      'Alamat Ayah',
      
      // Mother Info
      'Nama Ibu',
      'No HP Ibu',
      'Email Ibu',
      'Pekerjaan Ibu',
      'Alamat Ibu',
      
      // Guardian Info (Optional)
      'Nama Wali',
      'No HP Wali',
      'Email Wali',
      'Pekerjaan Wali',
      'Alamat Wali'
    ]

    // Create example data
    const exampleData = [
      {
        'ID Santri': 'STD-001',
        'Nama Santri': 'Ahmad Fauzi',
        'Tanggal Lahir': '2010-05-15',
        'Tempat Lahir': 'Jakarta',
        'Jenis Kelamin': 'Laki-laki',
        'Alamat': 'Jl. Masjid No. 123, Jakarta Selatan',
        'No HP Santri': '081234567890',
        'Email Santri': '<EMAIL>',
        'Tanggal Masuk': '2024-07-01',
        'Status': 'active',
        'Kelas': classes?.[0]?.name || 'Kelas 1A',
        
        'Nama Ayah': 'Budi Santoso',
        'No HP Ayah': '081234567891',
        'Email Ayah': '<EMAIL>',
        'Pekerjaan Ayah': 'Pegawai Swasta',
        'Alamat Ayah': 'Jl. Masjid No. 123, Jakarta Selatan',
        
        'Nama Ibu': 'Siti Aminah',
        'No HP Ibu': '081234567892',
        'Email Ibu': '<EMAIL>',
        'Pekerjaan Ibu': 'Ibu Rumah Tangga',
        'Alamat Ibu': 'Jl. Masjid No. 123, Jakarta Selatan',
        
        'Nama Wali': '',
        'No HP Wali': '',
        'Email Wali': '',
        'Pekerjaan Wali': '',
        'Alamat Wali': ''
      },
      {
        'ID Santri': 'STD-002',
        'Nama Santri': 'Siti Aisyah',
        'Tanggal Lahir': '2011-03-20',
        'Tempat Lahir': 'Bandung',
        'Jenis Kelamin': 'Perempuan',
        'Alamat': 'Jl. Pesantren No. 456, Bandung',
        'No HP Santri': '081234567893',
        'Email Santri': '<EMAIL>',
        'Tanggal Masuk': '2024-07-01',
        'Status': 'active',
        'Kelas': classes?.[1]?.name || 'Kelas 1B',
        
        'Nama Ayah': 'Muhammad Ali',
        'No HP Ayah': '081234567894',
        'Email Ayah': '<EMAIL>',
        'Pekerjaan Ayah': 'Wiraswasta',
        'Alamat Ayah': 'Jl. Pesantren No. 456, Bandung',
        
        'Nama Ibu': 'Fatimah',
        'No HP Ibu': '081234567895',
        'Email Ibu': '<EMAIL>',
        'Pekerjaan Ibu': 'Guru',
        'Alamat Ibu': 'Jl. Pesantren No. 456, Bandung',
        
        'Nama Wali': 'Ahmad Yusuf',
        'No HP Wali': '081234567896',
        'Email Wali': '<EMAIL>',
        'Pekerjaan Wali': 'Ustadz',
        'Alamat Wali': 'Jl. Pesantren No. 456, Bandung'
      }
    ]

    // Create worksheet
    const worksheet = XLSX.utils.json_to_sheet(exampleData)

    // Set column widths
    const colWidths = [
      { wch: 12 }, // ID Santri
      { wch: 20 }, // Nama Santri
      { wch: 15 }, // Tanggal Lahir
      { wch: 15 }, // Tempat Lahir
      { wch: 12 }, // Jenis Kelamin
      { wch: 30 }, // Alamat
      { wch: 15 }, // No HP Santri
      { wch: 25 }, // Email Santri
      { wch: 15 }, // Tanggal Masuk
      { wch: 10 }, // Status
      { wch: 15 }, // Kelas
      
      { wch: 20 }, // Nama Ayah
      { wch: 15 }, // No HP Ayah
      { wch: 25 }, // Email Ayah
      { wch: 20 }, // Pekerjaan Ayah
      { wch: 30 }, // Alamat Ayah
      
      { wch: 20 }, // Nama Ibu
      { wch: 15 }, // No HP Ibu
      { wch: 25 }, // Email Ibu
      { wch: 20 }, // Pekerjaan Ibu
      { wch: 30 }, // Alamat Ibu
      
      { wch: 20 }, // Nama Wali
      { wch: 15 }, // No HP Wali
      { wch: 25 }, // Email Wali
      { wch: 20 }, // Pekerjaan Wali
      { wch: 30 }  // Alamat Wali
    ]
    worksheet['!cols'] = colWidths

    // Add worksheet to workbook
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Data Santri')

    // Create instructions sheet
    const instructions = [
      ['PETUNJUK PENGGUNAAN TEMPLATE IMPORT SANTRI'],
      [''],
      ['1. FORMAT DATA SANTRI:'],
      ['   - ID Santri: Unik untuk setiap santri (contoh: STD-001)'],
      ['   - Nama Santri: Nama lengkap santri (WAJIB)'],
      ['   - Tanggal Lahir: Format YYYY-MM-DD atau DD/MM/YYYY'],
      ['   - Jenis Kelamin: Laki-laki atau Perempuan'],
      ['   - Status: active, inactive, graduated'],
      [''],
      ['2. FORMAT DATA ORANG TUA:'],
      ['   - Minimal isi data Ayah atau Ibu'],
      ['   - Data Wali opsional (jika berbeda dari orang tua)'],
      ['   - Alamat bisa sama dengan alamat santri'],
      [''],
      ['3. FORMAT KELAS:'],
      ['   - Gunakan nama kelas yang sudah ada di sistem'],
      ['   - Kelas yang tersedia:'],
      ...classes?.map(cls => [`     - ${cls.name}`]) || [],
      [''],
      ['4. TIPS PENGISIAN:'],
      ['   - Hapus baris contoh sebelum import'],
      ['   - Pastikan format tanggal konsisten'],
      ['   - Gunakan copy-paste untuk mempercepat'],
      ['   - Periksa data sebelum import'],
      [''],
      ['5. KOLOM WAJIB:'],
      ['   - Nama Santri (WAJIB)'],
      ['   - Minimal satu data orang tua (Ayah atau Ibu)'],
      [''],
      ['6. FORMAT TANGGAL:'],
      ['   - YYYY-MM-DD (contoh: 2010-05-15)'],
      ['   - DD/MM/YYYY (contoh: 15/05/2010)'],
      ['   - DD-MM-YYYY (contoh: 15-05-2010)'],
      [''],
      ['CATATAN PENTING:'],
      ['- Sistem akan membuat akun orang tua otomatis'],
      ['- Data yang sudah ada tidak akan ditimpa'],
      ['- Backup data sebelum import massal'],
      ['- Hubungi admin jika ada masalah']
    ]

    const instructionsSheet = XLSX.utils.aoa_to_sheet(instructions)
    instructionsSheet['!cols'] = [{ wch: 60 }]
    XLSX.utils.book_append_sheet(workbook, instructionsSheet, 'Petunjuk')

    // Create classes reference sheet
    const classesData = [
      ['DAFTAR KELAS YANG TERSEDIA'],
      [''],
      ['Nama Kelas', 'ID Kelas'],
      ...classes?.map(cls => [cls.name, cls.class_id]) || []
    ]

    const classesSheet = XLSX.utils.aoa_to_sheet(classesData)
    classesSheet['!cols'] = [{ wch: 20 }, { wch: 15 }]
    XLSX.utils.book_append_sheet(workbook, classesSheet, 'Daftar Kelas')

    // Generate Excel buffer
    const excelBuffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' })

    // Create filename
    const filename = `Template_Import_Santri_${new Date().toISOString().split('T')[0]}.xlsx`

    // Return Excel file
    return new NextResponse(excelBuffer, {
      status: 200,
      headers: {
        'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'Content-Disposition': `attachment; filename="${filename}"`,
      },
    })

  } catch (error: any) {
    console.error('Error generating students template:', error)
    return NextResponse.json(
      { error: error.message || "Failed to generate template" },
      { status: 500 }
    )
  }
}
