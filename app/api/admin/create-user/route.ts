import { cookies } from 'next/headers'
import { createServerClient } from "@/utils/supabase/server"
import { NextRequest, NextResponse } from "next/server"

export async function POST(request: NextRequest) {
  // Check if the requester is an admin
  const supabase = await createServerClient()
  const { data: { session } } = await supabase.auth.getSession()
  
  if (!session) {
    return NextResponse.json(
      { error: 'Authentication required' },
      { status: 401 }
    )
  }
  
  // Check if user is admin
  const { data: profile, error: profileError } = await supabase
    .from('profiles')
    .select('role')
    .eq('user_id', session.user.id)
    .single()
  
  if (profileError || profile?.role !== 'admin') {
    return NextResponse.json(
      { error: 'Admin privileges required' },
      { status: 403 }
    )
  }
  
  try {
    // Parse request body
    const { email, password, fullName, role } = await request.json()
    
    // Validate inputs
    if (!email || !password || !fullName || !role) {
      return NextResponse.json(
        { error: 'Email, password, fullName and role are required' },
        { status: 400 }
      )
    }
    
    // Validate role
    if (!['admin', 'teacher', 'parent'].includes(role)) {
      return NextResponse.json(
        { error: 'Role must be one of: admin, teacher, parent' },
        { status: 400 }
      )
    }
    
    // Create the user
    const { data: userData, error: userError } = await supabase.auth.admin.createUser({
      email,
      password,
      email_confirm: true,
      user_metadata: { full_name: fullName },
      app_metadata: { role }
    })
    
    if (userError) {
      console.error('Error creating user:', userError)
      return NextResponse.json(
        { error: userError.message },
        { status: 500 }
      )
    }
    
    // Create the profile entry
    const { error: profileCreateError } = await supabase
      .from('profiles')
      .insert({
        user_id: userData.user.id,
        full_name: fullName,
        role
      })
    
    if (profileCreateError) {
      console.error('Error creating profile:', profileCreateError)
      // Try to delete the user if profile creation fails
      await supabase.auth.admin.deleteUser(userData.user.id)
      return NextResponse.json(
        { error: profileCreateError.message },
        { status: 500 }
      )
    }
    
    // Create additional role-specific record if needed
    if (role === 'teacher') {
      const { error: teacherError } = await supabase
        .from('teachers')
        .insert({
          profile_id: userData.user.id,
          teacher_id: `TCH-${Date.now()}`,
          name: fullName,
          email: email,
          phone: '',
          specialization: ''
        })
      
      if (teacherError) {
        console.error('Error creating teacher record:', teacherError)
        // Continue anyway, as the profile is already created
      }
    } else if (role === 'parent') {
      const { error: parentError } = await supabase
        .from('parents')
        .insert({
          profile_id: userData.user.id,
          name: fullName,
          email: email,
          phone: '',
          address: ''
        })
      
      if (parentError) {
        console.error('Error creating parent record:', parentError)
        // Continue anyway, as the profile is already created
      }
    }
    
    return NextResponse.json({
      success: true,
      user: {
        id: userData.user.id,
        email: userData.user.email,
        role
      }
    })
    
  } catch (error) {
    console.error('Error creating user:', error)
    return NextResponse.json(
      { error: 'Failed to create user' },
      { status: 500 }
    )
  }
} 