import { createServerClient } from "@/utils/supabase/server"
import { NextResponse } from "next/server"

// GET a subject by ID
export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  const id = params.id
  
  if (!id) {
    return NextResponse.json(
      { error: "ID subject tidak valid" },
      { status: 400 }
    )
  }
  
  const supabase = await createServerClient()
  
  // Check authentication
  const { data: { session } } = await supabase.auth.getSession()
  
  if (!session) {
    return NextResponse.json(
      { error: "Unauthorized" },
      { status: 401 }
    )
  }
  
  try {
    // Get subject details
    const { data: subject, error } = await supabase
      .from('subjects')
      .select('*')
      .eq('id', id)
      .single()
    
    if (error) {
      return NextResponse.json(
        { error: error.message },
        { status: 500 }
      )
    }
    
    if (!subject) {
      return NextResponse.json(
        { error: "Subject tidak ditemukan" },
        { status: 404 }
      )
    }
    
    // Get learning objectives for this subject
    const { data: objectives, error: objectivesError } = await supabase
      .from('subject_objectives')
      .select('*')
      .eq('subject_id', id)
      .order('created_at', { ascending: true })
    
    if (objectivesError) {
      console.error("Error fetching objectives:", objectivesError)
      // Continue anyway, just without objectives
    }
    
    // Get teacher relationships for this subject
    const { data: teachers, error: teachersError } = await supabase
      .from('subject_teachers')
      .select(`
        teacher_id,
        teacher:teachers (
          id,
          name,
          teacher_id,
          specialization
        )
      `)
      .eq('subject_id', id)
    
    if (teachersError) {
      console.error("Error fetching subject teachers:", teachersError)
      // Continue anyway, just without teachers
    }
    
    return NextResponse.json({
      subject,
      objectives: objectives || [],
      teachers: teachers?.map(t => t.teacher) || []
    })
  } catch (error: any) {
    return NextResponse.json(
      { error: error.message || "Gagal mendapatkan data subject" },
      { status: 500 }
    )
  }
} 