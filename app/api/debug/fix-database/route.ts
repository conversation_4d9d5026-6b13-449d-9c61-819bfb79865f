import { NextRequest, NextResponse } from "next/server"
import { createServerClient } from "@/utils/supabase/server"

// GET - Check database status
export async function GET(request: NextRequest) {
  try {
    const supabase = await createServerClient()
    const { data: { session } } = await supabase.auth.getSession()
    
    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    // Check profiles table
    const { data: profiles, error: profilesError } = await supabase
      .from('profiles')
      .select('id, user_id, full_name, role')
      .limit(5)

    // Check teachers table structure
    const { data: teachers, error: teachersError } = await supabase
      .from('teachers')
      .select('*')
      .limit(5)

    // Check for missing profiles
    const { data: missingProfiles, error: missingError } = await supabase
      .rpc('check_missing_profiles')
      .single()

    return NextResponse.json({
      status: "Database check completed",
      profiles: {
        count: profiles?.length || 0,
        data: profiles || [],
        error: profilesError?.message || null
      },
      teachers: {
        count: teachers?.length || 0,
        data: teachers || [],
        error: teachersError?.message || null
      },
      missingProfiles: missingProfiles || null,
      recommendations: {
        needsMigration: !!teachersError || !!profilesError,
        hasData: (profiles?.length || 0) > 0 || (teachers?.length || 0) > 0
      }
    })

  } catch (error: any) {
    console.error('Error checking database:', error)
    return NextResponse.json(
      { error: error.message || "Failed to check database" },
      { status: 500 }
    )
  }
}

// POST - Fix database issues
export async function POST(request: NextRequest) {
  try {
    const supabase = await createServerClient()
    const { data: { session } } = await supabase.auth.getSession()
    
    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    const { action } = await request.json()

    if (action === 'create_missing_profiles') {
      // Create profiles for existing teachers
      const { data: teachers } = await supabase
        .from('teachers')
        .select('profile_id, name, email')

      const { data: parents } = await supabase
        .from('parents')
        .select('profile_id, name, email')

      const profilesToCreate = []

      // Add teacher profiles
      if (teachers) {
        for (const teacher of teachers) {
          if (teacher.profile_id) {
            profilesToCreate.push({
              id: teacher.profile_id,
              user_id: teacher.profile_id,
              full_name: teacher.name,
              role: 'teacher'
            })
          }
        }
      }

      // Add parent profiles
      if (parents) {
        for (const parent of parents) {
          if (parent.profile_id) {
            profilesToCreate.push({
              id: parent.profile_id,
              user_id: parent.profile_id,
              full_name: parent.name,
              role: 'parent'
            })
          }
        }
      }

      // Insert profiles
      const { data: createdProfiles, error: createError } = await supabase
        .from('profiles')
        .upsert(profilesToCreate, { onConflict: 'id' })
        .select()

      if (createError) {
        return NextResponse.json(
          { error: createError.message },
          { status: 500 }
        )
      }

      return NextResponse.json({
        message: "Missing profiles created successfully",
        created: createdProfiles?.length || 0,
        profiles: createdProfiles
      })
    }

    if (action === 'create_current_user_profile') {
      // Create profile for current user as admin
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .upsert({
          id: session.user.id,
          user_id: session.user.id,
          full_name: session.user.email?.split('@')[0] || 'Admin',
          role: 'admin'
        })
        .select()
        .single()

      if (profileError) {
        return NextResponse.json(
          { error: profileError.message },
          { status: 500 }
        )
      }

      return NextResponse.json({
        message: "Current user profile created as admin",
        profile
      })
    }

    return NextResponse.json(
      { error: "Invalid action" },
      { status: 400 }
    )

  } catch (error: any) {
    console.error('Error fixing database:', error)
    return NextResponse.json(
      { error: error.message || "Failed to fix database" },
      { status: 500 }
    )
  }
}
