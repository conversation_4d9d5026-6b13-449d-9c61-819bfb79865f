import { NextRequest, NextResponse } from "next/server"
import { createServerClient } from "@/utils/supabase/server"

// GET - Check teacher status for current user
export async function GET(request: NextRequest) {
  try {
    const supabase = await createServerClient()
    const { data: { session } } = await supabase.auth.getSession()
    
    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    // Get user info
    const userInfo = {
      id: session.user.id,
      email: session.user.email
    }

    // Check if user exists in users table
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('*')
      .eq('id', session.user.id)
      .single()

    // Check if teacher record exists
    const { data: teacher, error: teacherError } = await supabase
      .from('teachers')
      .select('*')
      .eq('user_id', session.user.id)
      .single()

    // Get all teachers for reference
    const { data: allTeachers, error: allTeachersError } = await supabase
      .from('teachers')
      .select('id, user_id, name, email, status')

    return NextResponse.json({
      userInfo,
      user: user || null,
      userError: userError?.message || null,
      teacher: teacher || null,
      teacherError: teacherError?.message || null,
      allTeachers: allTeachers || [],
      allTeachersError: allTeachersError?.message || null,
      debug: {
        message: "If teacher is null, you need to create a teacher record with user_id = " + session.user.id
      }
    })

  } catch (error: any) {
    console.error('Error in teacher status debug:', error)
    return NextResponse.json(
      { error: error.message || "Failed to debug teacher status" },
      { status: 500 }
    )
  }
}

// POST - Create teacher record for current user
export async function POST(request: NextRequest) {
  try {
    const supabase = await createServerClient()
    const { data: { session } } = await supabase.auth.getSession()

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    // Check if teacher record already exists
    const { data: existingTeacher } = await supabase
      .from('teachers')
      .select('*')
      .eq('user_id', session.user.id)
      .single()

    if (existingTeacher) {
      return NextResponse.json({
        message: "Teacher record already exists",
        teacher: existingTeacher
      })
    }

    // Get user info from users table
    const { data: user } = await supabase
      .from('users')
      .select('*')
      .eq('id', session.user.id)
      .single()

    // Create teacher record
    const teacherId = `TCH-${Date.now()}`
    const { data: newTeacher, error: createError } = await supabase
      .from('teachers')
      .insert({
        user_id: session.user.id,
        name: user?.name || session.user.email?.split('@')[0] || 'Teacher',
        email: session.user.email,
        phone: '',
        specialty: ''
      })
      .select()
      .single()

    if (createError) {
      return NextResponse.json(
        { error: createError.message },
        { status: 500 }
      )
    }

    return NextResponse.json({
      message: "Teacher record created successfully",
      teacher: newTeacher
    })

  } catch (error: any) {
    console.error('Error creating teacher record:', error)
    return NextResponse.json(
      { error: error.message || "Failed to create teacher record" },
      { status: 500 }
    )
  }
}
