import { NextRequest, NextResponse } from "next/server"
import { createServerClient } from "@/utils/supabase/server"

// GET - Check current user role and status
export async function GET(request: NextRequest) {
  try {
    const supabase = await createServerClient()
    const { data: { session } } = await supabase.auth.getSession()
    
    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    // Check if profile exists
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', session.user.id)
      .single()

    // Check if teacher record exists
    const { data: teacher, error: teacherError } = await supabase
      .from('teachers')
      .select('*')
      .eq('profile_id', session.user.id)
      .single()

    return NextResponse.json({
      session: {
        user_id: session.user.id,
        email: session.user.email
      },
      profile: profile || null,
      profileError: profileError?.message || null,
      teacher: teacher || null,
      teacherError: teacherError?.message || null,
      canAccessTimeline: profile?.role === 'admin' || !!teacher,
      recommendations: {
        needsProfileRecord: !profile,
        needsTeacherRecord: profile?.role !== 'admin' && !teacher,
        isAdmin: profile?.role === 'admin'
      }
    })

  } catch (error: any) {
    console.error('Error checking user role:', error)
    return NextResponse.json(
      { error: error.message || "Failed to check user role" },
      { status: 500 }
    )
  }
}

// POST - Create or update user record to admin
export async function POST(request: NextRequest) {
  try {
    const supabase = await createServerClient()
    const { data: { session } } = await supabase.auth.getSession()
    
    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    const { action } = await request.json()

    if (action === 'create_admin') {
      // Create or update profile record as admin
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .upsert({
          id: session.user.id,
          user_id: session.user.id,
          full_name: session.user.email?.split('@')[0] || 'Admin',
          role: 'admin'
        })
        .select()
        .single()

      if (profileError) {
        return NextResponse.json(
          { error: profileError.message },
          { status: 500 }
        )
      }

      // Also create admin teacher record for timeline access
      const { data: existingTeacher } = await supabase
        .from('teachers')
        .select('id')
        .eq('profile_id', session.user.id)
        .single()

      if (!existingTeacher) {
        const { data: adminTeacher, error: teacherError } = await supabase
          .from('teachers')
          .insert({
            profile_id: session.user.id,
            teacher_id: `ADMIN-${Date.now()}`,
            name: profile.full_name || 'Admin',
            specialization: 'Administrator',
            status: 'active'
          })
          .select()
          .single()

        if (teacherError) {
          console.error('Error creating admin teacher record:', teacherError)
        }
      }

      return NextResponse.json({
        message: "Profile and teacher record created/updated as admin successfully",
        profile
      })
    }

    if (action === 'create_teacher_record') {
      // First check if profile exists and is not admin
      const { data: profile } = await supabase
        .from('profiles')
        .select('role')
        .eq('id', session.user.id)
        .single()

      if (profile?.role === 'admin') {
        return NextResponse.json(
          { error: "Admin users don't need teacher records" },
          { status: 400 }
        )
      }

      // Create teacher record
      const teacherId = `TCH-${Date.now()}`
      const { data: teacher, error: teacherError } = await supabase
        .from('teachers')
        .insert({
          profile_id: session.user.id,
          teacher_id: teacherId,
          name: session.user.email?.split('@')[0] || 'Teacher',
          email: session.user.email || '',
          phone: '',
          specialty: ''
        })
        .select()
        .single()

      if (teacherError) {
        return NextResponse.json(
          { error: teacherError.message },
          { status: 500 }
        )
      }

      return NextResponse.json({
        message: "Teacher record created successfully",
        teacher
      })
    }

    return NextResponse.json(
      { error: "Invalid action" },
      { status: 400 }
    )

  } catch (error: any) {
    console.error('Error updating user role:', error)
    return NextResponse.json(
      { error: error.message || "Failed to update user role" },
      { status: 500 }
    )
  }
}
