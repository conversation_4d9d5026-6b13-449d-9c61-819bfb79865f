import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/utils/supabase/server'

export async function POST(request: NextRequest) {
  try {
    // Authorization check
    const supabase = await createServerClient()
    const { data: { session } } = await supabase.auth.getSession()

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    const formData = await request.formData()
    const file = formData.get('file') as File
    const key = formData.get('key') as string
    const category = formData.get('category') as string || 'general'
    const altText = formData.get('altText') as string || ''
    const caption = formData.get('caption') as string || ''

    if (!file) {
      return NextResponse.json(
        { error: 'No file uploaded' },
        { status: 400 }
      )
    }

    if (!key) {
      return NextResponse.json(
        { error: 'Key is required' },
        { status: 400 }
      )
    }

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif']
    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json(
        { error: 'Invalid file type. Only JPEG, PNG, WebP, and GIF are allowed.' },
        { status: 400 }
      )
    }

    // Validate file size (max 5MB)
    const maxSize = 5 * 1024 * 1024 // 5MB
    if (file.size > maxSize) {
      return NextResponse.json(
        { error: 'File size too large. Maximum size is 5MB.' },
        { status: 400 }
      )
    }

    // Generate unique filename
    const timestamp = Date.now()
    const randomSuffix = Math.random().toString(36).substring(2, 8)
    const fileExtension = file.name.split('.').pop()
    const filename = `${key}_${timestamp}_${randomSuffix}.${fileExtension}`
    const filePath = `${category}/${filename}`

    // Convert file to buffer
    const bytes = await file.arrayBuffer()
    const buffer = Buffer.from(bytes)

    // Upload to Supabase Storage
    const { data: uploadData, error: uploadError } = await supabase.storage
      .from('photos') // bucket name
      .upload(filePath, buffer, {
        contentType: file.type,
        upsert: true // Replace if exists
      })

    if (uploadError) {
      console.error('Supabase storage error:', uploadError)
      return NextResponse.json(
        { error: `Upload failed: ${uploadError.message}` },
        { status: 500 }
      )
    }

    // Get public URL
    const { data: urlData } = supabase.storage
      .from('photos')
      .getPublicUrl(filePath)

    const publicUrl = urlData.publicUrl

    // Save metadata to database
    const { data: imageRecord, error: dbError } = await supabase
      .from('website_images')
      .upsert({
        key,
        filename,
        original_name: file.name,
        file_path: publicUrl,
        file_size: file.size,
        mime_type: file.type,
        alt_text: altText || '',
        caption: caption || '',
        category,
        storage_path: filePath
      }, {
        onConflict: 'key'
      })
      .select()
      .single()

    if (dbError) {
      console.error('Database error:', dbError)
      // Don't fail the request if database save fails
      console.warn('Image uploaded but metadata not saved:', dbError.message)
    }

    return NextResponse.json({
      success: true,
      message: 'File uploaded successfully',
      image: imageRecord,
      url: publicUrl,
      file_path: publicUrl,
      storage_path: filePath
    })

  } catch (error: any) {
    console.error('Error uploading file:', error)
    return NextResponse.json(
      { error: error.message || 'Failed to upload file' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // Authorization check
    const supabase = await createServerClient()
    const { data: { session } } = await supabase.auth.getSession()

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const key = searchParams.get('key')

    if (!key) {
      return NextResponse.json(
        { error: 'Key is required' },
        { status: 400 }
      )
    }

    // Get image record from database
    const { data: imageRecord, error: fetchError } = await supabase
      .from('website_images')
      .select('storage_path')
      .eq('key', key)
      .single()

    if (fetchError || !imageRecord) {
      return NextResponse.json(
        { error: 'Image not found' },
        { status: 404 }
      )
    }

    // Delete from Supabase Storage
    const { error: deleteError } = await supabase.storage
      .from('photos')
      .remove([imageRecord.storage_path])

    if (deleteError) {
      console.error('Storage delete error:', deleteError)
    }

    // Delete from database
    const { error: dbDeleteError } = await supabase
      .from('website_images')
      .delete()
      .eq('key', key)

    if (dbDeleteError) {
      console.error('Database delete error:', dbDeleteError)
      return NextResponse.json(
        { error: 'Failed to delete image record' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'Image deleted successfully'
    })

  } catch (error: any) {
    console.error('Error deleting image:', error)
    return NextResponse.json(
      { error: error.message || 'Failed to delete image' },
      { status: 500 }
    )
  }
}
