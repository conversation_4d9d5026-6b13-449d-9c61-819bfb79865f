import { NextResponse } from 'next/server'
import { createServerClient } from '@/utils/supabase/server'

export async function POST() {
  try {
    const supabase = await createServerClient()
    
    // First, create admin user if not exists
    const { error: adminError } = await supabase
      .from('profiles')
      .upsert({
        id: '11111111-1111-1111-1111-111111111111',
        name: 'Admin PTQ Al Ihsan',
        email: '<EMAIL>',
        role: 'admin'
      })
    
    if (adminError) {
      console.error('Admin user error:', adminError)
      return NextResponse.json({ error: 'Failed to create admin user', details: adminError }, { status: 500 })
    }
    
    // Insert blog categories
    const categories = [
      { id: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890', name: '<PERSON><PERSON><PERSON><PERSON>', slug: 'pendidikan-islami' },
      { id: 'b2c3d4e5-f6a7-8901-bcde-f12345678901', name: '<PERSON><PERSON><PERSON>', slug: 'parenting' },
      { id: 'c3d4e5f6-a7b8-9012-cdef-123456789012', name: 'Kegiatan Pondok', slug: 'kegiatan-pondok' },
      { id: 'd4e5f6a7-b8c9-0123-def1-234567890123', name: 'Prestasi', slug: 'prestasi' },
      { id: 'e5f6a7b8-c9d0-1234-ef12-345678901234', name: 'Artikel Islami', slug: 'artikel-islami' }
    ]
    
    const { error: categoriesError } = await supabase
      .from('blog_categories')
      .upsert(categories)
    
    if (categoriesError) {
      console.error('Categories error:', categoriesError)
      return NextResponse.json({ error: 'Failed to create categories', details: categoriesError }, { status: 500 })
    }
    
    // Insert blog posts
    const posts = [
      {
        id: 'f1a2b3c4-d5e6-7890-abcd-ef1234567890',
        author_id: '11111111-1111-1111-1111-111111111111',
        title: 'Manfaat Menghafal Al-Qur\'an Sejak Dini',
        slug: 'manfaat-menghafal-al-quran-sejak-dini',
        content: '<p>Menghafal Al-Qur\'an sejak dini memiliki banyak manfaat untuk perkembangan kognitif dan spiritual anak.</p><h2>1. Meningkatkan Daya Ingat</h2><p>Proses menghafal Al-Qur\'an melatih otak untuk menyimpan informasi dalam jumlah besar.</p>',
        featured_image: '/placeholder.svg?height=300&width=600',
        status: 'published',
        published_at: '2024-03-15T10:00:00+07:00'
      },
      {
        id: 'f2a3b4c5-d6e7-8901-bcde-f12345678901',
        author_id: '11111111-1111-1111-1111-111111111111',
        title: 'Tips Parenting Islami dalam Mendidik Anak',
        slug: 'tips-parenting-islami-dalam-mendidik-anak',
        content: '<p>Mendidik anak sesuai dengan ajaran Islam merupakan tanggung jawab setiap orang tua Muslim.</p><h2>1. Menjadi Teladan yang Baik</h2><p>Anak-anak lebih banyak belajar dari apa yang mereka lihat.</p>',
        featured_image: '/placeholder.svg?height=300&width=600',
        status: 'published',
        published_at: '2024-03-10T09:00:00+07:00'
      },
      {
        id: 'f3a4b5c6-d7e8-9012-cdef-123456789012',
        author_id: '11111111-1111-1111-1111-111111111111',
        title: 'Metode Efektif Mengajarkan Bahasa Arab pada Anak',
        slug: 'metode-efektif-mengajarkan-bahasa-arab-pada-anak',
        content: '<p>Bahasa Arab adalah kunci untuk memahami Al-Qur\'an dan hadits.</p><h2>1. Mulai dari Dasar</h2><p>Ajarkan huruf hijaiyah terlebih dahulu dengan cara yang menyenangkan.</p>',
        featured_image: '/placeholder.svg?height=300&width=600',
        status: 'published',
        published_at: '2024-03-05T14:00:00+07:00'
      }
    ]
    
    const { error: postsError } = await supabase
      .from('blog_posts')
      .upsert(posts)
    
    if (postsError) {
      console.error('Posts error:', postsError)
      return NextResponse.json({ error: 'Failed to create posts', details: postsError }, { status: 500 })
    }
    
    // Insert post-category relationships
    const relations = [
      { post_id: 'f1a2b3c4-d5e6-7890-abcd-ef1234567890', category_id: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890' },
      { post_id: 'f2a3b4c5-d6e7-8901-bcde-f12345678901', category_id: 'b2c3d4e5-f6a7-8901-bcde-f12345678901' },
      { post_id: 'f3a4b5c6-d7e8-9012-cdef-123456789012', category_id: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890' }
    ]
    
    const { error: relationsError } = await supabase
      .from('post_categories')
      .upsert(relations)
    
    if (relationsError) {
      console.error('Relations error:', relationsError)
      return NextResponse.json({ error: 'Failed to create relations', details: relationsError }, { status: 500 })
    }
    
    return NextResponse.json({ 
      message: 'Blog seed data created successfully',
      data: {
        categories: categories.length,
        posts: posts.length,
        relations: relations.length
      }
    })
    
  } catch (error) {
    console.error('Seed error:', error)
    return NextResponse.json({ error: error.message }, { status: 500 })
  }
}
