import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

const createServerClient = () => {
  return createClient(supabaseUrl, supabaseKey)
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const category = searchParams.get('category')
    const key = searchParams.get('key')

    const supabase = createServerClient()

    if (key) {
      // Get specific setting by key
      const { data: setting, error } = await supabase
        .from('website_settings')
        .select('*')
        .eq('key', key)
        .single()

      if (error && error.code !== 'PGRST116') {
        throw error
      }

      return NextResponse.json({ setting })
    }

    // Build query
    let query = supabase
      .from('website_settings')
      .select('*')
      .order('category')
      .order('key')

    if (category) {
      query = query.eq('category', category)
    }

    const { data: settings, error } = await query

    if (error) {
      throw error
    }

    // Group settings by category
    const groupedSettings = settings?.reduce((acc: any, setting) => {
      const cat = setting.category || 'general'
      if (!acc[cat]) {
        acc[cat] = []
      }
      acc[cat].push(setting)
      return acc
    }, {})

    return NextResponse.json({ 
      settings: settings || [],
      grouped: groupedSettings || {}
    })

  } catch (error: any) {
    console.error('Error fetching website settings:', error)
    return NextResponse.json(
      { error: error.message || 'Failed to fetch website settings' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { key, value, type = 'text', category = 'general', description } = body

    if (!key || value === undefined) {
      return NextResponse.json(
        { error: 'Key and value are required' },
        { status: 400 }
      )
    }

    const supabase = createServerClient()

    const { data: setting, error } = await supabase
      .from('website_settings')
      .upsert({
        key,
        value: typeof value === 'string' ? value : JSON.stringify(value),
        type,
        category,
        description
      }, {
        onConflict: 'key'
      })
      .select()
      .single()

    if (error) {
      throw error
    }

    return NextResponse.json({ setting })

  } catch (error: any) {
    console.error('Error saving website setting:', error)
    return NextResponse.json(
      { error: error.message || 'Failed to save website setting' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { settings } = body

    if (!Array.isArray(settings)) {
      return NextResponse.json(
        { error: 'Settings must be an array' },
        { status: 400 }
      )
    }

    const supabase = createServerClient()

    // Update multiple settings
    const updates = settings.map(setting => ({
      key: setting.key,
      value: typeof setting.value === 'string' ? setting.value : JSON.stringify(setting.value),
      type: setting.type || 'text',
      category: setting.category || 'general',
      description: setting.description
    }))

    const { data, error } = await supabase
      .from('website_settings')
      .upsert(updates, {
        onConflict: 'key'
      })
      .select()

    if (error) {
      throw error
    }

    return NextResponse.json({ 
      message: 'Settings updated successfully',
      settings: data 
    })

  } catch (error: any) {
    console.error('Error updating website settings:', error)
    return NextResponse.json(
      { error: error.message || 'Failed to update website settings' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const key = searchParams.get('key')

    if (!key) {
      return NextResponse.json(
        { error: 'Key is required' },
        { status: 400 }
      )
    }

    const supabase = createServerClient()

    const { error } = await supabase
      .from('website_settings')
      .delete()
      .eq('key', key)

    if (error) {
      throw error
    }

    return NextResponse.json({ 
      message: 'Setting deleted successfully' 
    })

  } catch (error: any) {
    console.error('Error deleting website setting:', error)
    return NextResponse.json(
      { error: error.message || 'Failed to delete website setting' },
      { status: 500 }
    )
  }
}
