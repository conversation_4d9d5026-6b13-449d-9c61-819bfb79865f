import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'
import { writeFile, mkdir } from 'fs/promises'
import { join } from 'path'
import { existsSync } from 'fs'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

const createServerClient = () => {
  return createClient(supabaseUrl, supabaseKey)
}

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData()
    const file = formData.get('file') as File
    const key = formData.get('key') as string
    const category = formData.get('category') as string || 'general'
    const altText = formData.get('altText') as string || ''
    const caption = formData.get('caption') as string || ''

    if (!file) {
      return NextResponse.json(
        { error: 'No file uploaded' },
        { status: 400 }
      )
    }

    if (!key) {
      return NextResponse.json(
        { error: 'Key is required' },
        { status: 400 }
      )
    }

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif']
    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json(
        { error: 'Invalid file type. Only JPEG, PNG, WebP, and GIF are allowed.' },
        { status: 400 }
      )
    }

    // Validate file size (max 5MB)
    const maxSize = 5 * 1024 * 1024 // 5MB
    if (file.size > maxSize) {
      return NextResponse.json(
        { error: 'File size too large. Maximum size is 5MB.' },
        { status: 400 }
      )
    }

    // Generate unique filename
    const timestamp = Date.now()
    const fileExtension = file.name.split('.').pop()
    const filename = `${key}_${timestamp}.${fileExtension}`
    
    // Create upload directory if it doesn't exist
    const uploadDir = join(process.cwd(), 'public', 'uploads', category)
    if (!existsSync(uploadDir)) {
      await mkdir(uploadDir, { recursive: true })
    }

    // Save file to public/uploads
    const filePath = join(uploadDir, filename)
    const relativePath = `/uploads/${category}/${filename}`
    
    const bytes = await file.arrayBuffer()
    const buffer = Buffer.from(bytes)
    
    await writeFile(filePath, buffer)

    // Save to database
    const supabase = createServerClient()

    // Delete existing image with same key if exists
    const { data: existingImage } = await supabase
      .from('website_images')
      .select('*')
      .eq('key', key)
      .single()

    if (existingImage) {
      // Delete old file if exists
      try {
        const oldFilePath = join(process.cwd(), 'public', existingImage.file_path)
        if (existsSync(oldFilePath)) {
          await import('fs/promises').then(fs => fs.unlink(oldFilePath))
        }
      } catch (error) {
        console.warn('Could not delete old file:', error)
      }
    }

    const { data: imageRecord, error } = await supabase
      .from('website_images')
      .upsert({
        key,
        filename,
        original_name: file.name,
        file_path: relativePath,
        file_size: file.size,
        mime_type: file.type,
        alt_text: altText || '',
        caption: caption || '',
        category
      }, {
        onConflict: 'key'
      })
      .select()
      .single()

    if (error) {
      console.error('Database error:', error)
      throw error
    }

    return NextResponse.json({
      message: 'File uploaded successfully',
      image: imageRecord,
      url: `/api/uploads/${category}/${filename}`
    })

  } catch (error: any) {
    console.error('Error uploading file:', error)
    return NextResponse.json(
      { error: error.message || 'Failed to upload file' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const key = searchParams.get('key')
    const category = searchParams.get('category')

    const supabase = createServerClient()

    if (key) {
      // Get specific image by key
      const { data: image, error } = await supabase
        .from('website_images')
        .select('*')
        .eq('key', key)
        .single()

      if (error && error.code !== 'PGRST116') {
        throw error
      }

      return NextResponse.json({ image })
    }

    // Get all images or by category
    let query = supabase
      .from('website_images')
      .select('*')
      .order('created_at', { ascending: false })

    if (category) {
      query = query.eq('category', category)
    }

    const { data: images, error } = await query

    if (error) {
      throw error
    }

    return NextResponse.json({ images: images || [] })

  } catch (error: any) {
    console.error('Error fetching images:', error)
    return NextResponse.json(
      { error: error.message || 'Failed to fetch images' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const key = searchParams.get('key')

    if (!key) {
      return NextResponse.json(
        { error: 'Key is required' },
        { status: 400 }
      )
    }

    const supabase = createServerClient()

    // Get image record first
    const { data: image, error: fetchError } = await supabase
      .from('website_images')
      .select('*')
      .eq('key', key)
      .single()

    if (fetchError) {
      throw fetchError
    }

    // Delete file from filesystem
    if (image?.file_path) {
      try {
        const filePath = join(process.cwd(), 'public', image.file_path)
        if (existsSync(filePath)) {
          await import('fs/promises').then(fs => fs.unlink(filePath))
        }
      } catch (error) {
        console.warn('Could not delete file:', error)
      }
    }

    // Delete from database
    const { error: deleteError } = await supabase
      .from('website_images')
      .delete()
      .eq('key', key)

    if (deleteError) {
      throw deleteError
    }

    return NextResponse.json({
      message: 'Image deleted successfully'
    })

  } catch (error: any) {
    console.error('Error deleting image:', error)
    return NextResponse.json(
      { error: error.message || 'Failed to delete image' },
      { status: 500 }
    )
  }
}
