import { createServerClient } from "@/utils/supabase/server"
import { NextResponse } from "next/server"
import { createAchievement, updateAchievement, deleteAchievement, getStudentAchievements } from "@/lib/supabase-crud"

export async function GET(request: Request) {
  try {
    // Authorization check
    const supabase = await createServerClient()
    const { data: { session } } = await supabase.auth.getSession()

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    // Get query parameters
    const { searchParams } = new URL(request.url)
    const studentId = searchParams.get('studentId')
    const subjectId = searchParams.get('subjectId')
    const classId = searchParams.get('classId')
    const limit = parseInt(searchParams.get('limit') || '50')
    const offset = parseInt(searchParams.get('offset') || '0')

    let query = supabase
      .from('achievements')
      .select(`
        id,
        student_id,
        subject_id,
        value,
        grade,
        notes,
        verified_by,
        achievement_date,
        created_at,
        updated_at,
        students (
          id,
          student_id,
          name,
          class_students (
            classes (
              id,
              name
            )
          )
        ),
        subjects (
          id,
          name,
          category
        ),
        teachers:verified_by (
          id,
          name,
          teacher_id
        )
      `)
      .order('achievement_date', { ascending: false })
      .range(offset, offset + limit - 1)

    // Apply filters
    if (studentId) {
      query = query.eq('student_id', studentId)
    }

    if (subjectId) {
      query = query.eq('subject_id', subjectId)
    }

    if (classId) {
      // Get students in the class first
      const { data: classStudents } = await supabase
        .from('class_students')
        .select('student_id')
        .eq('class_id', classId)

      if (classStudents && classStudents.length > 0) {
        const studentIds = classStudents.map(cs => cs.student_id)
        query = query.in('student_id', studentIds)
      }
    }

    const { data: achievements, error } = await query

    if (error) {
      console.error('Error fetching achievements:', error)
      return NextResponse.json(
        { error: "Failed to fetch achievements" },
        { status: 500 }
      )
    }

    // Get total count for pagination
    let countQuery = supabase
      .from('achievements')
      .select('id', { count: 'exact', head: true })

    if (studentId) {
      countQuery = countQuery.eq('student_id', studentId)
    }
    if (subjectId) {
      countQuery = countQuery.eq('subject_id', subjectId)
    }

    const { count } = await countQuery

    return NextResponse.json({
      achievements: achievements || [],
      total: count || 0,
      limit,
      offset
    })
  } catch (error: any) {
    return NextResponse.json(
      { error: error.message || "Failed to get achievements" },
      { status: 500 }
    )
  }
}

// POST is handled by the existing endpoint

export async function POST(request: Request) {
  try {
    // Authorization check
    const supabase = await createServerClient()
    const { data: { session } } = await supabase.auth.getSession()

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    // Get the achievement data from the request
    const achievementData = await request.json()

    // Validate required fields
    const { student_id, subject_id, value, verified_by, achievement_date } = achievementData

    if (!student_id || !subject_id || !value || !verified_by || !achievement_date) {
      return NextResponse.json(
        { error: "student_id, subject_id, value, verified_by, and achievement_date are required" },
        { status: 400 }
      )
    }

    // Create the achievement
    const result = await createAchievement(achievementData)

    return NextResponse.json({
      success: true,
      data: result,
      message: "Achievement created successfully"
    })
  } catch (error: any) {
    return NextResponse.json(
      { error: error.message || "Failed to create achievement" },
      { status: 500 }
    )
  }
}

// PUT - Update achievement
export async function PUT(request: Request) {
  try {
    const supabase = await createServerClient()
    const { data: { session } } = await supabase.auth.getSession()

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const achievementId = searchParams.get('id')

    if (!achievementId) {
      return NextResponse.json(
        { error: "Achievement ID is required" },
        { status: 400 }
      )
    }

    const achievementData = await request.json()

    const result = await updateAchievement(achievementId, achievementData)

    return NextResponse.json({
      success: true,
      data: result,
      message: "Achievement updated successfully"
    })
  } catch (error: any) {
    return NextResponse.json(
      { error: error.message || "Failed to update achievement" },
      { status: 500 }
    )
  }
}

// DELETE - Delete achievement
export async function DELETE(request: Request) {
  try {
    const supabase = await createServerClient()
    const { data: { session } } = await supabase.auth.getSession()

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const achievementId = searchParams.get('id')

    if (!achievementId) {
      return NextResponse.json(
        { error: "Achievement ID is required" },
        { status: 400 }
      )
    }

    const result = await deleteAchievement(achievementId)

    return NextResponse.json({
      success: true,
      message: "Achievement deleted successfully"
    })
  } catch (error: any) {
    return NextResponse.json(
      { error: error.message || "Failed to delete achievement" },
      { status: 500 }
    )
  }
}