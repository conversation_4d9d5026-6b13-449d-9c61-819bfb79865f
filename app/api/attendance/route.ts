import { NextRequest, NextResponse } from "next/server"
import { createServerClient } from "@/utils/supabase/server"

// GET - Fetch attendance data
export async function GET(request: NextRequest) {
  try {
    const supabase = await createServerClient()
    const { data: { session } } = await supabase.auth.getSession()
    
    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const classId = searchParams.get('class_id')
    const date = searchParams.get('date')
    const startDate = searchParams.get('start_date')
    const endDate = searchParams.get('end_date')
    const studentId = searchParams.get('student_id')

    let query = supabase
      .from('attendance')
      .select(`
        id,
        student_id,
        class_id,
        attendance_date,
        status,
        notes,
        created_at,
        updated_at,
        students (
          id,
          student_id,
          name,
          class_students (
            classes (
              id,
              name
            )
          )
        ),
        classes (
          id,
          name
        )
      `)
      .order('attendance_date', { ascending: false })

    // Apply filters
    if (classId) {
      query = query.eq('class_id', classId)
    }

    if (studentId) {
      query = query.eq('student_id', studentId)
    }

    if (date) {
      query = query.eq('attendance_date', date)
    }

    if (startDate && endDate) {
      query = query.gte('attendance_date', startDate).lte('attendance_date', endDate)
    }

    const { data: attendanceData, error } = await query

    if (error) {
      console.error('Error fetching attendance:', error)
      return NextResponse.json(
        { error: "Failed to fetch attendance data" },
        { status: 500 }
      )
    }

    return NextResponse.json({ 
      attendance: attendanceData || [],
      total: attendanceData?.length || 0
    })
  } catch (error: any) {
    console.error('Error in attendance GET:', error)
    return NextResponse.json(
      { error: error.message || "Failed to fetch attendance" },
      { status: 500 }
    )
  }
}

// POST - Create attendance record
export async function POST(request: NextRequest) {
  try {
    const supabase = await createServerClient()
    const { data: { session } } = await supabase.auth.getSession()
    
    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { student_id, class_id, attendance_date, status, notes } = body

    // Validate required fields
    if (!student_id || !class_id || !attendance_date || !status) {
      return NextResponse.json(
        { error: "student_id, class_id, attendance_date, and status are required" },
        { status: 400 }
      )
    }

    // Validate status
    const validStatuses = ['present', 'absent', 'late', 'excused']
    if (!validStatuses.includes(status)) {
      return NextResponse.json(
        { error: "Invalid status. Must be one of: present, absent, late, excused" },
        { status: 400 }
      )
    }

    // Check if attendance record already exists for this student and date
    const { data: existingRecord } = await supabase
      .from('attendance')
      .select('id')
      .eq('student_id', student_id)
      .eq('attendance_date', attendance_date)
      .single()

    if (existingRecord) {
      // Update existing record
      const { data, error } = await supabase
        .from('attendance')
        .update({
          class_id,
          status,
          notes,
          updated_at: new Date().toISOString()
        })
        .eq('id', existingRecord.id)
        .select()
        .single()

      if (error) {
        console.error('Error updating attendance:', error)
        return NextResponse.json(
          { error: "Failed to update attendance record" },
          { status: 500 }
        )
      }

      return NextResponse.json({ 
        success: true, 
        data,
        message: "Attendance record updated successfully"
      })
    } else {
      // Create new record
      const { data, error } = await supabase
        .from('attendance')
        .insert({
          student_id,
          class_id,
          attendance_date,
          status,
          notes
        })
        .select()
        .single()

      if (error) {
        console.error('Error creating attendance:', error)
        return NextResponse.json(
          { error: "Failed to create attendance record" },
          { status: 500 }
        )
      }

      return NextResponse.json({ 
        success: true, 
        data,
        message: "Attendance record created successfully"
      })
    }
  } catch (error: any) {
    console.error('Error in attendance POST:', error)
    return NextResponse.json(
      { error: error.message || "Failed to process attendance" },
      { status: 500 }
    )
  }
}

// PUT - Bulk update attendance
export async function PUT(request: NextRequest) {
  try {
    const supabase = await createServerClient()
    const { data: { session } } = await supabase.auth.getSession()
    
    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { attendance_records } = body

    if (!Array.isArray(attendance_records) || attendance_records.length === 0) {
      return NextResponse.json(
        { error: "attendance_records array is required" },
        { status: 400 }
      )
    }

    const results = []
    const errors = []

    for (const record of attendance_records) {
      try {
        const { student_id, class_id, attendance_date, status, notes } = record

        // Check if record exists
        const { data: existingRecord } = await supabase
          .from('attendance')
          .select('id')
          .eq('student_id', student_id)
          .eq('attendance_date', attendance_date)
          .single()

        if (existingRecord) {
          // Update existing
          const { data, error } = await supabase
            .from('attendance')
            .update({
              class_id,
              status,
              notes,
              updated_at: new Date().toISOString()
            })
            .eq('id', existingRecord.id)
            .select()
            .single()

          if (error) {
            errors.push({ student_id, error: error.message })
          } else {
            results.push(data)
          }
        } else {
          // Create new
          const { data, error } = await supabase
            .from('attendance')
            .insert({
              student_id,
              class_id,
              attendance_date,
              status,
              notes
            })
            .select()
            .single()

          if (error) {
            errors.push({ student_id, error: error.message })
          } else {
            results.push(data)
          }
        }
      } catch (error: any) {
        errors.push({ student_id: record.student_id, error: error.message })
      }
    }

    return NextResponse.json({
      success: true,
      processed: results.length,
      errors: errors.length,
      results,
      error_details: errors
    })
  } catch (error: any) {
    console.error('Error in attendance bulk update:', error)
    return NextResponse.json(
      { error: error.message || "Failed to process bulk attendance" },
      { status: 500 }
    )
  }
}
