import type React from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { ArrowRight, CheckCircle2, FileText, Calendar, Users } from "lucide-react"

export default function PPDBPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-emerald-700 to-emerald-800 text-white py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto text-center">
            <h1 className="text-4xl font-bold mb-6"><PERSON><PERSON><PERSON><PERSON></h1>
            <p className="text-xl text-emerald-100">
              Bergabunglah dengan PTQ Al Ihsan dan berikan pendidikan terbaik untuk putra-putri Anda.
            </p>
          </div>
        </div>
      </section>

      {/* Registration Info */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <Tabs defaultValue="info" className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="info">Informasi PPDB</TabsTrigger>
                <TabsTrigger value="requirements">Persyaratan</TabsTrigger>
                <TabsTrigger value="register">Formulir Pendaftaran</TabsTrigger>
              </TabsList>

              <TabsContent value="info" className="mt-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Informasi Penerimaan Santri Baru</CardTitle>
                    <CardDescription>Tahun Ajaran 2024/2025</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <InfoCard
                        icon={<Calendar className="h-8 w-8 text-emerald-600" />}
                        title="Jadwal Pendaftaran"
                        items={[
                          "Gelombang 1: 1 Maret - 30 April 2024",
                          "Gelombang 2: 1 Mei - 30 Juni 2024",
                          "Pengumuman: 15 Juli 2024",
                          "Daftar Ulang: 16-31 Juli 2024",
                        ]}
                      />

                      <InfoCard
                        icon={<Users className="h-8 w-8 text-emerald-600" />}
                        title="Kuota Penerimaan"
                        items={["Santri Putra: 40 orang", "Santri Putri: 40 orang", "Total: 80 santri baru"]}
                      />
                    </div>

                    <div>
                      <h3 className="text-lg font-bold mb-3">Biaya Pendaftaran</h3>
                      <table className="w-full border-collapse">
                        <thead>
                          <tr className="bg-emerald-100">
                            <th className="border border-emerald-200 p-2 text-left">Komponen</th>
                            <th className="border border-emerald-200 p-2 text-right">Biaya</th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr>
                            <td className="border border-emerald-200 p-2">Formulir Pendaftaran</td>
                            <td className="border border-emerald-200 p-2 text-right">Rp 200.000</td>
                          </tr>
                          <tr>
                            <td className="border border-emerald-200 p-2">Biaya Masuk (sekali bayar)</td>
                            <td className="border border-emerald-200 p-2 text-right">Rp 3.500.000</td>
                          </tr>
                          <tr>
                            <td className="border border-emerald-200 p-2">SPP Bulanan</td>
                            <td className="border border-emerald-200 p-2 text-right">Rp 850.000</td>
                          </tr>
                          <tr>
                            <td className="border border-emerald-200 p-2">Seragam dan Perlengkapan</td>
                            <td className="border border-emerald-200 p-2 text-right">Rp 1.200.000</td>
                          </tr>
                        </tbody>
                      </table>
                      <p className="text-sm text-gray-500 mt-2">
                        * Tersedia beasiswa untuk santri berprestasi dan kurang mampu
                      </p>
                    </div>

                    <div>
                      <h3 className="text-lg font-bold mb-3">Alur Pendaftaran</h3>
                      <ol className="space-y-4">
                        <li className="flex items-start gap-3">
                          <div className="bg-emerald-100 text-emerald-800 rounded-full h-6 w-6 flex items-center justify-center flex-shrink-0 font-bold">
                            1
                          </div>
                          <div>
                            <p className="font-medium">Pendaftaran Online</p>
                            <p className="text-gray-600">
                              Isi formulir pendaftaran online dan upload dokumen yang diperlukan.
                            </p>
                          </div>
                        </li>
                        <li className="flex items-start gap-3">
                          <div className="bg-emerald-100 text-emerald-800 rounded-full h-6 w-6 flex items-center justify-center flex-shrink-0 font-bold">
                            2
                          </div>
                          <div>
                            <p className="font-medium">Pembayaran Biaya Pendaftaran</p>
                            <p className="text-gray-600">Transfer biaya pendaftaran ke rekening yang ditentukan.</p>
                          </div>
                        </li>
                        <li className="flex items-start gap-3">
                          <div className="bg-emerald-100 text-emerald-800 rounded-full h-6 w-6 flex items-center justify-center flex-shrink-0 font-bold">
                            3
                          </div>
                          <div>
                            <p className="font-medium">Tes Seleksi</p>
                            <p className="text-gray-600">Tes baca Al-Qur'an, wawancara, dan tes akademik.</p>
                          </div>
                        </li>
                        <li className="flex items-start gap-3">
                          <div className="bg-emerald-100 text-emerald-800 rounded-full h-6 w-6 flex items-center justify-center flex-shrink-0 font-bold">
                            4
                          </div>
                          <div>
                            <p className="font-medium">Pengumuman Hasil Seleksi</p>
                            <p className="text-gray-600">Pengumuman hasil seleksi melalui website dan SMS.</p>
                          </div>
                        </li>
                        <li className="flex items-start gap-3">
                          <div className="bg-emerald-100 text-emerald-800 rounded-full h-6 w-6 flex items-center justify-center flex-shrink-0 font-bold">
                            5
                          </div>
                          <div>
                            <p className="font-medium">Daftar Ulang</p>
                            <p className="text-gray-600">Pembayaran biaya masuk dan perlengkapan santri.</p>
                          </div>
                        </li>
                      </ol>
                    </div>
                  </CardContent>
                  <CardFooter>
                    <Button asChild>
                      <Link href="#" className="flex items-center gap-2">
                        Download Brosur PPDB
                        <FileText className="h-4 w-4" />
                      </Link>
                    </Button>
                  </CardFooter>
                </Card>
              </TabsContent>

              <TabsContent value="requirements" className="mt-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Persyaratan Pendaftaran</CardTitle>
                    <CardDescription>Dokumen dan ketentuan yang harus dipenuhi calon santri</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div>
                      <h3 className="text-lg font-bold mb-3">Persyaratan Umum</h3>
                      <ul className="space-y-2">
                        <RequirementItem>Beragama Islam</RequirementItem>
                        <RequirementItem>Lulus SD/MI atau sederajat</RequirementItem>
                        <RequirementItem>Usia maksimal 13 tahun</RequirementItem>
                        <RequirementItem>Sehat jasmani dan rohani</RequirementItem>
                        <RequirementItem>Bersedia tinggal di asrama</RequirementItem>
                        <RequirementItem>Bersedia menaati peraturan pondok</RequirementItem>
                      </ul>
                    </div>

                    <div>
                      <h3 className="text-lg font-bold mb-3">Dokumen yang Harus Disiapkan</h3>
                      <ul className="space-y-2">
                        <RequirementItem>Fotokopi Akta Kelahiran (2 lembar)</RequirementItem>
                        <RequirementItem>Fotokopi Kartu Keluarga (2 lembar)</RequirementItem>
                        <RequirementItem>Fotokopi KTP Orang Tua/Wali (2 lembar)</RequirementItem>
                        <RequirementItem>Fotokopi Ijazah SD/MI (2 lembar)</RequirementItem>
                        <RequirementItem>Surat Keterangan Sehat dari dokter</RequirementItem>
                        <RequirementItem>Pas foto berwarna ukuran 3x4 (4 lembar)</RequirementItem>
                        <RequirementItem>Surat Pernyataan Kesanggupan dari Orang Tua/Wali</RequirementItem>
                      </ul>
                    </div>

                    <div>
                      <h3 className="text-lg font-bold mb-3">Ketentuan Tes Seleksi</h3>
                      <ul className="space-y-2">
                        <RequirementItem>Tes Baca Al-Qur'an (kelancaran dan tajwid)</RequirementItem>
                        <RequirementItem>Tes Hafalan (minimal 1 juz)</RequirementItem>
                        <RequirementItem>Tes Akademik (Matematika dan Bahasa Indonesia)</RequirementItem>
                        <RequirementItem>Wawancara dengan calon santri</RequirementItem>
                        <RequirementItem>Wawancara dengan orang tua/wali</RequirementItem>
                      </ul>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="register" className="mt-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Formulir Pendaftaran</CardTitle>
                    <CardDescription>Isi formulir berikut dengan data yang benar</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <form className="space-y-6">
                      <div className="space-y-4">
                        <h3 className="text-lg font-bold">Data Calon Santri</h3>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <Label htmlFor="fullName">Nama Lengkap</Label>
                            <Input id="fullName" placeholder="Masukkan nama lengkap" />
                          </div>

                          <div className="space-y-2">
                            <Label htmlFor="nickname">Nama Panggilan</Label>
                            <Input id="nickname" placeholder="Masukkan nama panggilan" />
                          </div>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <Label htmlFor="birthPlace">Tempat Lahir</Label>
                            <Input id="birthPlace" placeholder="Masukkan tempat lahir" />
                          </div>

                          <div className="space-y-2">
                            <Label htmlFor="birthDate">Tanggal Lahir</Label>
                            <Input id="birthDate" type="date" />
                          </div>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <Label htmlFor="gender">Jenis Kelamin</Label>
                            <select id="gender" className="w-full p-2 border rounded-md">
                              <option value="">Pilih Jenis Kelamin</option>
                              <option value="male">Laki-laki</option>
                              <option value="female">Perempuan</option>
                            </select>
                          </div>

                          <div className="space-y-2">
                            <Label htmlFor="bloodType">Golongan Darah</Label>
                            <select id="bloodType" className="w-full p-2 border rounded-md">
                              <option value="">Pilih Golongan Darah</option>
                              <option value="A">A</option>
                              <option value="B">B</option>
                              <option value="AB">AB</option>
                              <option value="O">O</option>
                            </select>
                          </div>
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="address">Alamat Lengkap</Label>
                          <Textarea id="address" placeholder="Masukkan alamat lengkap" />
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <Label htmlFor="school">Asal Sekolah</Label>
                            <Input id="school" placeholder="Masukkan asal sekolah" />
                          </div>

                          <div className="space-y-2">
                            <Label htmlFor="quranMemo">Hafalan Al-Qur'an</Label>
                            <Input id="quranMemo" placeholder="Contoh: 1 Juz, 3 Juz, dll" />
                          </div>
                        </div>
                      </div>

                      <div className="space-y-4">
                        <h3 className="text-lg font-bold">Data Orang Tua/Wali</h3>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <Label htmlFor="fatherName">Nama Ayah</Label>
                            <Input id="fatherName" placeholder="Masukkan nama ayah" />
                          </div>

                          <div className="space-y-2">
                            <Label htmlFor="motherName">Nama Ibu</Label>
                            <Input id="motherName" placeholder="Masukkan nama ibu" />
                          </div>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <Label htmlFor="fatherJob">Pekerjaan Ayah</Label>
                            <Input id="fatherJob" placeholder="Masukkan pekerjaan ayah" />
                          </div>

                          <div className="space-y-2">
                            <Label htmlFor="motherJob">Pekerjaan Ibu</Label>
                            <Input id="motherJob" placeholder="Masukkan pekerjaan ibu" />
                          </div>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <Label htmlFor="parentPhone">No. HP Orang Tua</Label>
                            <Input id="parentPhone" placeholder="Masukkan nomor HP orang tua" />
                          </div>

                          <div className="space-y-2">
                            <Label htmlFor="parentEmail">Email Orang Tua</Label>
                            <Input id="parentEmail" type="email" placeholder="Masukkan email orang tua" />
                          </div>
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="parentAddress">Alamat Orang Tua</Label>
                          <Textarea id="parentAddress" placeholder="Masukkan alamat orang tua" />
                        </div>
                      </div>

                      <div className="space-y-4">
                        <h3 className="text-lg font-bold">Dokumen Pendukung</h3>

                        <div className="space-y-2">
                          <Label htmlFor="photo">Pas Foto (3x4)</Label>
                          <Input id="photo" type="file" />
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="birthCert">Akta Kelahiran</Label>
                          <Input id="birthCert" type="file" />
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="familyCard">Kartu Keluarga</Label>
                          <Input id="familyCard" type="file" />
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="certificate">Ijazah SD/MI</Label>
                          <Input id="certificate" type="file" />
                        </div>
                      </div>
                    </form>
                  </CardContent>
                  <CardFooter className="flex justify-between">
                    <Button variant="outline">Reset Formulir</Button>
                    <Button>Kirim Pendaftaran</Button>
                  </CardFooter>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-16 bg-emerald-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-800 mb-4">Pertanyaan Umum</h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Beberapa pertanyaan yang sering ditanyakan oleh calon santri dan orang tua.
            </p>
          </div>

          <div className="max-w-3xl mx-auto space-y-6">
            <FAQItem
              question="Apakah ada beasiswa untuk santri berprestasi?"
              answer="Ya, PTQ Al Ihsan menyediakan beasiswa untuk santri berprestasi dan santri dari keluarga kurang mampu. Beasiswa dapat berupa potongan biaya masuk, SPP bulanan, atau beasiswa penuh."
            />

            <FAQItem
              question="Bagaimana jadwal kunjungan orang tua?"
              answer="Jadwal kunjungan orang tua dilaksanakan setiap akhir bulan pada hari Minggu. Orang tua dapat mengunjungi santri dari pukul 08.00 hingga 16.00 WIB."
            />

            <FAQItem
              question="Apakah santri diperbolehkan membawa HP?"
              answer="Santri tidak diperbolehkan membawa HP selama di pondok. Komunikasi dengan orang tua dapat dilakukan melalui telepon pondok pada jadwal yang telah ditentukan."
            />

            <FAQItem
              question="Bagaimana jika santri sakit?"
              answer="PTQ Al Ihsan memiliki klinik kesehatan dengan perawat jaga. Jika santri sakit, akan diberikan perawatan di klinik pondok. Jika diperlukan, santri akan dirujuk ke rumah sakit terdekat dengan pemberitahuan kepada orang tua."
            />

            <FAQItem
              question="Apakah ada kegiatan ekstrakurikuler?"
              answer="Ya, PTQ Al Ihsan memiliki berbagai kegiatan ekstrakurikuler seperti Kitab Gundul Club, Pelatihan Kesiapsiagaan Kebakaran, dan kegiatan sosial untuk mengembangkan bakat dan minat santri."
            />
          </div>
        </div>
      </section>

      {/* CTA */}
      <section className="py-16 bg-gradient-to-r from-emerald-700 to-emerald-800 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-6">Masih Punya Pertanyaan?</h2>
          <p className="text-xl text-emerald-100 mb-8 max-w-2xl mx-auto">
            Jangan ragu untuk menghubungi kami jika Anda memiliki pertanyaan lebih lanjut.
          </p>
          <Button asChild size="lg" className="bg-white text-emerald-800 hover:bg-emerald-100">
            <Link href="/kontak" className="flex items-center gap-2">
              Hubungi Kami
              <ArrowRight className="h-4 w-4" />
            </Link>
          </Button>
        </div>
      </section>
    </div>
  )
}

function InfoCard({ icon, title, items }: { icon: React.ReactNode; title: string; items: string[] }) {
  return (
    <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
      <div className="flex items-center gap-3 mb-4">
        {icon}
        <h3 className="text-lg font-bold">{title}</h3>
      </div>
      <ul className="space-y-2">
        {items.map((item, index) => (
          <li key={index} className="flex items-center gap-2 text-gray-700">
            <div className="h-1.5 w-1.5 rounded-full bg-emerald-600"></div>
            {item}
          </li>
        ))}
      </ul>
    </div>
  )
}

function RequirementItem({ children }: { children: React.ReactNode }) {
  return (
    <li className="flex items-start gap-3">
      <CheckCircle2 className="h-5 w-5 text-emerald-600 mt-0.5 flex-shrink-0" />
      <span className="text-gray-700">{children}</span>
    </li>
  )
}

function FAQItem({ question, answer }: { question: string; answer: string }) {
  return (
    <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
      <h3 className="text-lg font-bold text-gray-800 mb-2">{question}</h3>
      <p className="text-gray-600">{answer}</p>
    </div>
  )
}

