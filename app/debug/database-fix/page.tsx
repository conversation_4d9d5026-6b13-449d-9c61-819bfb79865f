"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Loader2, Database, AlertTriangle, CheckCircle, Wrench, Shield } from "lucide-react"

interface DatabaseStatus {
  status: string
  profiles: {
    count: number
    data: any[]
    error: string | null
  }
  teachers: {
    count: number
    data: any[]
    error: string | null
  }
  missingProfiles: any
  recommendations: {
    needsMigration: boolean
    hasData: boolean
  }
}

export default function DatabaseFixPage() {
  const [status, setStatus] = useState<DatabaseStatus | null>(null)
  const [loading, setLoading] = useState(true)
  const [actionLoading, setActionLoading] = useState(false)

  const fetchStatus = async () => {
    try {
      const response = await fetch('/api/debug/fix-database')
      const data = await response.json()
      setStatus(data)
    } catch (error) {
      console.error('Error fetching database status:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleAction = async (action: string) => {
    setActionLoading(true)
    try {
      const response = await fetch('/api/debug/fix-database', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action }),
      })

      const result = await response.json()
      
      if (response.ok) {
        alert(result.message)
        fetchStatus() // Refresh status
      } else {
        alert(`Error: ${result.error}`)
      }
    } catch (error) {
      console.error('Error performing action:', error)
      alert('Terjadi kesalahan')
    } finally {
      setActionLoading(false)
    }
  }

  useEffect(() => {
    fetchStatus()
  }, [])

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center">
          <Loader2 className="h-6 w-6 animate-spin mr-2" />
          <span>Checking database status...</span>
        </div>
      </div>
    )
  }

  if (!status) {
    return (
      <div className="container mx-auto p-6">
        <Card>
          <CardContent className="p-6">
            <div className="text-center text-red-600">
              <AlertTriangle className="h-12 w-12 mx-auto mb-4" />
              <p>Failed to check database status</p>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Database Migration & Fix</h1>
        <p className="text-gray-600">Fix foreign key constraint issues and migrate to unified auth system</p>
      </div>

      {/* Database Status Overview */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            Database Status
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <h4 className="font-medium">Profiles Table</h4>
              <div className="flex items-center gap-2">
                {status.profiles.error ? (
                  <Badge variant="destructive">Error</Badge>
                ) : (
                  <Badge variant="default">OK</Badge>
                )}
                <span>{status.profiles.count} records</span>
              </div>
              {status.profiles.error && (
                <p className="text-sm text-red-600">{status.profiles.error}</p>
              )}
            </div>

            <div className="space-y-2">
              <h4 className="font-medium">Teachers Table</h4>
              <div className="flex items-center gap-2">
                {status.teachers.error ? (
                  <Badge variant="destructive">Error</Badge>
                ) : (
                  <Badge variant="default">OK</Badge>
                )}
                <span>{status.teachers.count} records</span>
              </div>
              {status.teachers.error && (
                <p className="text-sm text-red-600">{status.teachers.error}</p>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Migration Status */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Wrench className="h-5 w-5" />
            Migration Status
          </CardTitle>
        </CardHeader>
        <CardContent>
          {status.recommendations.needsMigration ? (
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <AlertTriangle className="h-4 w-4 text-amber-500" />
                <span className="text-amber-600">Database needs migration</span>
              </div>
              
              <div className="space-y-2">
                <p className="text-sm text-gray-600">
                  Your database has foreign key constraint issues. This usually happens when:
                </p>
                <ul className="text-sm text-gray-600 list-disc list-inside ml-4">
                  <li>Teachers/Parents tables reference user_ids that don't exist in profiles</li>
                  <li>The auth system hasn't been unified yet</li>
                  <li>Sample data was inserted without proper profile records</li>
                </ul>
              </div>

              <div className="space-y-2">
                <Button 
                  onClick={() => handleAction('create_missing_profiles')}
                  disabled={actionLoading}
                  className="gap-2"
                >
                  <Wrench className="h-4 w-4" />
                  Create Missing Profiles
                </Button>
                <p className="text-xs text-gray-500">
                  This will create profile records for all existing teachers and parents
                </p>
              </div>
            </div>
          ) : (
            <div className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <span className="text-green-600">Database structure looks good</span>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Current User Setup */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Current User Setup
          </CardTitle>
          <CardDescription>
            Set up your current user account to access the system
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <p className="text-sm text-gray-600">
              Create an admin profile for your current user account to access all features including timeline input.
            </p>
            
            <Button 
              onClick={() => handleAction('create_current_user_profile')}
              disabled={actionLoading}
              className="gap-2"
            >
              <Shield className="h-4 w-4" />
              Create Admin Profile for Current User
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Manual Migration Instructions */}
      <Card>
        <CardHeader>
          <CardTitle>Manual Migration (Advanced)</CardTitle>
          <CardDescription>
            If automatic fixes don't work, you can run the migration script manually
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="bg-gray-50 p-4 rounded-lg">
              <h4 className="font-medium mb-2">SQL Script Location:</h4>
              <code className="text-sm bg-gray-100 px-2 py-1 rounded">
                scripts/safe-migration.sql
              </code>
            </div>
            
            <div className="space-y-2">
              <h4 className="font-medium">Steps:</h4>
              <ol className="text-sm text-gray-600 list-decimal list-inside space-y-1">
                <li>Open Supabase Dashboard → SQL Editor</li>
                <li>Copy and paste the content of scripts/safe-migration.sql</li>
                <li>Run the script</li>
                <li>Refresh this page to check status</li>
              </ol>
            </div>

            <Button onClick={fetchStatus} variant="outline">
              Refresh Status
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
