"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Loader2, User, Shield, UserCheck, AlertCircle, CheckCircle } from "lucide-react"

interface UserStatus {
  session: {
    user_id: string
    email: string
  }
  profile: any
  profileError: string | null
  teacher: any
  teacherError: string | null
  canAccessTimeline: boolean
  recommendations: {
    needsProfileRecord: boolean
    needsTeacherRecord: boolean
    isAdmin: boolean
  }
}

export default function UserSetupPage() {
  const [status, setStatus] = useState<UserStatus | null>(null)
  const [loading, setLoading] = useState(true)
  const [actionLoading, setActionLoading] = useState(false)

  const fetchStatus = async () => {
    try {
      const response = await fetch('/api/debug/user-role')
      const data = await response.json()
      setStatus(data)
    } catch (error) {
      console.error('Error fetching status:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleAction = async (action: string) => {
    setActionLoading(true)
    try {
      const response = await fetch('/api/debug/user-role', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action }),
      })

      const result = await response.json()
      
      if (response.ok) {
        alert(result.message)
        fetchStatus() // Refresh status
      } else {
        alert(`Error: ${result.error}`)
      }
    } catch (error) {
      console.error('Error performing action:', error)
      alert('Terjadi kesalahan')
    } finally {
      setActionLoading(false)
    }
  }

  useEffect(() => {
    fetchStatus()
  }, [])

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center">
          <Loader2 className="h-6 w-6 animate-spin mr-2" />
          <span>Loading...</span>
        </div>
      </div>
    )
  }

  if (!status) {
    return (
      <div className="container mx-auto p-6">
        <Card>
          <CardContent className="p-6">
            <div className="text-center text-red-600">
              <AlertCircle className="h-12 w-12 mx-auto mb-4" />
              <p>Failed to load user status</p>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">User Setup & Debug</h1>
        <p className="text-gray-600">Diagnose and fix user access issues</p>
      </div>

      {/* Current Status */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            Current Status
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <p><strong>Email:</strong> {status.session.email}</p>
            <p><strong>User ID:</strong> {status.session.user_id}</p>
          </div>
          
          <div className="flex items-center gap-2">
            <strong>Timeline Access:</strong>
            {status.canAccessTimeline ? (
              <Badge variant="default" className="bg-green-500">
                <CheckCircle className="h-3 w-3 mr-1" />
                Allowed
              </Badge>
            ) : (
              <Badge variant="destructive">
                <AlertCircle className="h-3 w-3 mr-1" />
                Denied
              </Badge>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Profile Record Status */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Profile Record in Database</CardTitle>
        </CardHeader>
        <CardContent>
          {status.profile ? (
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-500" />
                <span>Profile record exists</span>
              </div>
              <p><strong>Name:</strong> {status.profile.full_name}</p>
              <p><strong>Role:</strong>
                <Badge variant={status.profile.role === 'admin' ? 'default' : 'secondary'} className="ml-2">
                  {status.profile.role}
                </Badge>
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <AlertCircle className="h-4 w-4 text-red-500" />
                <span>No profile record found</span>
              </div>
              <Button
                onClick={() => handleAction('create_admin')}
                disabled={actionLoading}
                className="gap-2"
              >
                <Shield className="h-4 w-4" />
                Create Admin Profile Record
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Teacher Record Status */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Teacher Record Status</CardTitle>
        </CardHeader>
        <CardContent>
          {status.recommendations.isAdmin ? (
            <div className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <span>Admin users don't need teacher records</span>
            </div>
          ) : status.teacher ? (
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-500" />
                <span>Teacher record exists</span>
              </div>
              <p><strong>Name:</strong> {status.teacher.name}</p>
              <p><strong>Email:</strong> {status.teacher.email}</p>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <AlertCircle className="h-4 w-4 text-red-500" />
                <span>No teacher record found</span>
              </div>
              {status.profile && status.profile.role !== 'admin' && (
                <Button 
                  onClick={() => handleAction('create_teacher_record')}
                  disabled={actionLoading}
                  className="gap-2"
                >
                  <UserCheck className="h-4 w-4" />
                  Create Teacher Record
                </Button>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>
            Recommended actions based on your current status
          </CardDescription>
        </CardHeader>
        <CardContent>
          {status.canAccessTimeline ? (
            <div className="text-center py-4">
              <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
              <p className="text-green-600 font-medium">
                ✅ You can now access the timeline input feature!
              </p>
              <Button 
                className="mt-4" 
                onClick={() => window.location.href = '/dashboard/teacher/timeline-input'}
              >
                Go to Timeline Input
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              <p className="text-amber-600">
                You need to complete the setup above to access timeline features.
              </p>
              <Button onClick={fetchStatus} variant="outline">
                Refresh Status
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
