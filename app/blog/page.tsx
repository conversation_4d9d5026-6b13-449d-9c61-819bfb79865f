import Image from "next/image"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardDescription, Card<PERSON>oot<PERSON>, CardHeader, CardTitle } from "@/components/ui/card"
import { ArrowRight, Calendar, User, Tag, Search } from "lucide-react"
import { Input } from "@/components/ui/input"
import { createServerClient } from "@/utils/supabase/server"

interface BlogPost {
  id: string
  title: string
  slug: string
  content: string
  featured_image: string | null
  status: string
  published_at: string | null
  created_at: string
  profiles: {
    name: string
    email: string
  } | null
  post_categories: Array<{
    blog_categories: {
      id: string
      name: string
      slug: string
    }
  }>
}

interface BlogCategory {
  id: string
  name: string
  slug: string
  post_count?: number
}

// Function to get excerpt from content
function getExcerpt(content: string, maxLength: number = 150): string {
  // Remove HTML tags and get plain text
  const plainText = content.replace(/<[^>]*>/g, '').replace(/\n/g, ' ')
  if (plainText.length <= maxLength) return plainText
  return plainText.substring(0, maxLength).trim() + '...'
}

// Function to format date
function formatDate(dateString: string): string {
  return new Date(dateString).toLocaleDateString('id-ID', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

async function getBlogData() {
  try {
    const supabase = await createServerClient()

    // Fetch published blog posts
    const { data: posts, error: postsError } = await supabase
      .from('blog_posts')
      .select(`
        *,
        profiles:author_id (
          name,
          email
        ),
        post_categories (
          blog_categories (
            id,
            name,
            slug
          )
        )
      `)
      .eq('status', 'published')
      .order('published_at', { ascending: false })
      .limit(12)

    if (postsError) {
      console.error('Error fetching posts:', postsError)
      return { posts: [], categories: [] }
    }

    // Fetch categories with post counts
    const { data: categories, error: categoriesError } = await supabase
      .from('blog_categories')
      .select(`
        *,
        post_categories (
          post_id,
          blog_posts!inner (
            status
          )
        )
      `)
      .order('name')

    if (categoriesError) {
      console.error('Error fetching categories:', categoriesError)
      return { posts: posts || [], categories: [] }
    }

    // Calculate post counts for categories
    const categoriesWithCounts = categories?.map(category => ({
      ...category,
      post_count: category.post_categories?.filter(
        (pc: any) => pc.blog_posts?.status === 'published'
      ).length || 0
    })) || []

    return {
      posts: posts || [],
      categories: categoriesWithCounts
    }
  } catch (error) {
    console.error('Error in getBlogData:', error)
    return { posts: [], categories: [] }
  }
}

export default async function BlogPage() {
  const { posts, categories } = await getBlogData()
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-emerald-700 to-emerald-800 text-white py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto text-center">
            <h1 className="text-4xl font-bold mb-6">Blog & Artikel</h1>
            <p className="text-xl text-emerald-100">
              Informasi, tips, dan artikel seputar pendidikan Islami dan kegiatan di PTQ Al Ihsan.
            </p>
          </div>
        </div>
      </section>

      {/* Blog Content */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="flex flex-col lg:flex-row gap-8">
            {/* Main Content */}
            <div className="lg:w-2/3">
              {posts.length === 0 ? (
                <div className="text-center py-12">
                  <p className="text-gray-500 text-lg">Belum ada artikel yang dipublikasikan</p>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {posts.map((post) => (
                    <BlogCard key={post.id} post={post} />
                  ))}
                </div>
              )}

              {/* Pagination */}
              <div className="mt-12 flex justify-center">
                <nav className="flex items-center gap-1">
                  <Button variant="outline" size="icon" disabled>
                    <span className="sr-only">Previous page</span>
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="h-4 w-4"
                    >
                      <path d="m15 18-6-6 6-6"></path>
                    </svg>
                  </Button>
                  <Button variant="outline" size="sm" className="bg-emerald-600 text-white hover:bg-emerald-700">
                    1
                  </Button>
                  <Button variant="outline" size="sm">
                    2
                  </Button>
                  <Button variant="outline" size="sm">
                    3
                  </Button>
                  <Button variant="outline" size="icon">
                    <span className="sr-only">Next page</span>
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="h-4 w-4"
                    >
                      <path d="m9 18 6-6-6-6"></path>
                    </svg>
                  </Button>
                </nav>
              </div>
            </div>

            {/* Sidebar */}
            <div className="lg:w-1/3 space-y-8">
              {/* Search */}
              <Card>
                <CardHeader>
                  <CardTitle>Pencarian</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                    <Input placeholder="Cari artikel..." className="pl-10" />
                  </div>
                </CardContent>
              </Card>

              {/* Categories */}
              <Card>
                <CardHeader>
                  <CardTitle>Kategori</CardTitle>
                </CardHeader>
                <CardContent>
                  {categories.length === 0 ? (
                    <p className="text-gray-500 text-sm">Belum ada kategori</p>
                  ) : (
                    <ul className="space-y-2">
                      {categories.map((category) => (
                        <li key={category.id}>
                          <Link
                            href={`/blog/category/${category.slug}`}
                            className="flex justify-between items-center py-2 hover:text-emerald-600 transition-colors"
                          >
                            <span>{category.name}</span>
                            <span className="bg-gray-100 text-gray-600 text-xs px-2 py-1 rounded-full">
                              {category.post_count || 0}
                            </span>
                          </Link>
                        </li>
                      ))}
                    </ul>
                  )}
                </CardContent>
              </Card>

              {/* Recent Posts */}
              <Card>
                <CardHeader>
                  <CardTitle>Artikel Terbaru</CardTitle>
                </CardHeader>
                <CardContent>
                  {posts.length === 0 ? (
                    <p className="text-gray-500 text-sm">Belum ada artikel</p>
                  ) : (
                    <ul className="space-y-4">
                      {posts.slice(0, 3).map((post) => (
                        <li key={post.id} className="flex gap-3">
                          <div className="relative w-16 h-16 flex-shrink-0">
                            <Image
                              src={post.featured_image || "/placeholder.svg"}
                              alt={post.title}
                              fill
                              className="object-cover rounded-md"
                            />
                          </div>
                          <div>
                            <Link
                              href={`/blog/${post.slug}`}
                              className="font-medium hover:text-emerald-600 transition-colors line-clamp-2"
                            >
                              {post.title}
                            </Link>
                            <p className="text-xs text-gray-500 mt-1">
                              {formatDate(post.published_at || post.created_at)}
                            </p>
                          </div>
                        </li>
                      ))}
                    </ul>
                  )}
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}

function BlogCard({ post }: { post: BlogPost }) {
  const excerpt = getExcerpt(post.content)
  const category = post.post_categories?.[0]?.blog_categories

  return (
    <Card className="overflow-hidden hover:shadow-md transition-shadow">
      <div className="relative h-48">
        <Image
          src={post.featured_image || "/placeholder.svg"}
          alt={post.title}
          fill
          className="object-cover"
        />
      </div>
      <CardHeader>
        <div className="flex items-center gap-4 text-sm text-gray-500 mb-2">
          <div className="flex items-center gap-1">
            <Calendar className="h-4 w-4" />
            <span>{formatDate(post.published_at || post.created_at)}</span>
          </div>
          <div className="flex items-center gap-1">
            <User className="h-4 w-4" />
            <span>{post.profiles?.name || 'Admin'}</span>
          </div>
        </div>
        <CardTitle className="line-clamp-2">
          <Link href={`/blog/${post.slug}`} className="hover:text-emerald-600 transition-colors">
            {post.title}
          </Link>
        </CardTitle>
        <CardDescription className="line-clamp-3">{excerpt}</CardDescription>
      </CardHeader>
      <CardFooter className="flex justify-between items-center">
        <div className="flex items-center gap-1 text-sm text-gray-500">
          <Tag className="h-4 w-4" />
          <span>{category?.name || 'Umum'}</span>
        </div>
        <Button asChild variant="ghost" size="sm" className="text-emerald-600 hover:text-emerald-700 p-0">
          <Link href={`/blog/${post.slug}`} className="flex items-center gap-1">
            Baca Selengkapnya
            <ArrowRight className="h-4 w-4" />
          </Link>
        </Button>
      </CardFooter>
    </Card>
  )
}

