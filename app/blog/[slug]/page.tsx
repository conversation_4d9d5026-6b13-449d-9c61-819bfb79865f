import Image from "next/image"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { ArrowLeft, Calendar, User, Tag, Share2, Facebook, Twitter, Linkedin, Copy } from "lucide-react"
import { createServerClient } from "@/utils/supabase/server"
import { notFound } from "next/navigation"

interface BlogPost {
  id: string
  title: string
  slug: string
  content: string
  featured_image: string | null
  status: string
  published_at: string | null
  created_at: string
  profiles: {
    name: string
    email: string
  } | null
  post_categories: Array<{
    blog_categories: {
      id: string
      name: string
      slug: string
    }
  }>
}

// Function to format date
function formatDate(dateString: string): string {
  return new Date(dateString).toLocaleDateString('id-ID', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

async function getBlogPost(slug: string): Promise<BlogPost | null> {
  try {
    const supabase = await createServerClient()

    const { data: post, error } = await supabase
      .from('blog_posts')
      .select(`
        *,
        profiles:author_id (
          name,
          email
        ),
        post_categories (
          blog_categories (
            id,
            name,
            slug
          )
        )
      `)
      .eq('slug', slug)
      .eq('status', 'published')
      .single()

    if (error) {
      console.error('Error fetching blog post:', error)
      return null
    }

    return post
  } catch (error) {
    console.error('Error in getBlogPost:', error)
    return null
  }
}

async function getRelatedPosts(currentPostId: string, categoryId?: string): Promise<BlogPost[]> {
  try {
    const supabase = await createServerClient()

    let query = supabase
      .from('blog_posts')
      .select(`
        *,
        profiles:author_id (
          name,
          email
        ),
        post_categories (
          blog_categories (
            id,
            name,
            slug
          )
        )
      `)
      .eq('status', 'published')
      .neq('id', currentPostId)
      .order('published_at', { ascending: false })
      .limit(3)

    // If we have a category, try to get posts from the same category
    if (categoryId) {
      const { data: relatedPosts } = await supabase
        .from('blog_posts')
        .select(`
          *,
          profiles:author_id (
            name,
            email
          ),
          post_categories!inner (
            blog_categories (
              id,
              name,
              slug
            )
          )
        `)
        .eq('status', 'published')
        .eq('post_categories.blog_categories.id', categoryId)
        .neq('id', currentPostId)
        .order('published_at', { ascending: false })
        .limit(3)

      if (relatedPosts && relatedPosts.length > 0) {
        return relatedPosts
      }
    }

    // Fallback to general recent posts
    const { data: posts } = await query
    return posts || []
  } catch (error) {
    console.error('Error fetching related posts:', error)
    return []
  }
}

export default async function BlogPostPage({ params }: { params: { slug: string } }) {
  const post = await getBlogPost(params.slug)

  if (!post) {
    notFound()
  }

  const category = post.post_categories?.[0]?.blog_categories
  const relatedPosts = await getRelatedPosts(post.id, category?.id)


  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-emerald-700 to-emerald-800 text-white py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto text-center">
            <h1 className="text-4xl font-bold mb-6">{post.title}</h1>
            <div className="flex flex-wrap justify-center gap-4 text-emerald-100">
              <div className="flex items-center gap-1">
                <Calendar className="h-4 w-4" />
                <span>{formatDate(post.published_at || post.created_at)}</span>
              </div>
              <div className="flex items-center gap-1">
                <User className="h-4 w-4" />
                <span>{post.profiles?.name || 'Admin'}</span>
              </div>
              {category && (
                <div className="flex items-center gap-1">
                  <Tag className="h-4 w-4" />
                  <span>{category.name}</span>
                </div>
              )}
            </div>
          </div>
        </div>
      </section>

      {/* Blog Content */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="flex flex-col lg:flex-row gap-8">
            {/* Main Content */}
            <div className="lg:w-2/3">
              <Button asChild variant="outline" className="mb-6">
                <Link href="/blog" className="flex items-center gap-2">
                  <ArrowLeft className="h-4 w-4" />
                  Kembali ke Blog
                </Link>
              </Button>

              <div className="bg-white rounded-xl shadow-sm overflow-hidden">
                <div className="relative h-80 w-full">
                  <Image src={post.featured_image || "/placeholder.svg"} alt={post.title} fill className="object-cover" />
                </div>

                <div className="p-6 md:p-8">
                  {/* Share buttons */}
                  <div className="flex items-center gap-4 mb-6 pb-6 border-b">
                    <span className="text-gray-500 flex items-center gap-1">
                      <Share2 className="h-4 w-4" />
                      Bagikan:
                    </span>
                    <div className="flex gap-2">
                      <Button variant="outline" size="icon" className="rounded-full h-8 w-8">
                        <Facebook className="h-4 w-4" />
                        <span className="sr-only">Share on Facebook</span>
                      </Button>
                      <Button variant="outline" size="icon" className="rounded-full h-8 w-8">
                        <Twitter className="h-4 w-4" />
                        <span className="sr-only">Share on Twitter</span>
                      </Button>
                      <Button variant="outline" size="icon" className="rounded-full h-8 w-8">
                        <Linkedin className="h-4 w-4" />
                        <span className="sr-only">Share on LinkedIn</span>
                      </Button>
                      <Button variant="outline" size="icon" className="rounded-full h-8 w-8">
                        <Copy className="h-4 w-4" />
                        <span className="sr-only">Copy link</span>
                      </Button>
                    </div>
                  </div>

                  {/* Article content */}
                  <div className="prose prose-emerald max-w-none" dangerouslySetInnerHTML={{ __html: post.content }} />

                  {/* Category */}
                  {category && (
                    <div className="mt-8 pt-6 border-t">
                      <div className="flex flex-wrap items-center gap-2">
                        <span className="text-gray-500">Kategori:</span>
                        <Link
                          href={`/blog/category/${category.slug}`}
                          className="bg-gray-100 hover:bg-emerald-100 text-gray-700 hover:text-emerald-700 px-3 py-1 rounded-full text-sm transition-colors"
                        >
                          {category.name}
                        </Link>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Sidebar */}
            <div className="lg:w-1/3 space-y-8">
              {/* Author Card */}
              <Card>
                <CardHeader>
                  <CardTitle>Tentang Penulis</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center gap-4">
                    <div className="relative h-16 w-16 rounded-full overflow-hidden">
                      <Image
                        src="/placeholder.svg?height=64&width=64"
                        alt={post.profiles?.name || 'Admin'}
                        fill
                        className="object-cover"
                      />
                    </div>
                    <div>
                      <h3 className="font-bold">{post.profiles?.name || 'Admin'}</h3>
                      <p className="text-sm text-gray-500">Pengajar di PTQ Al Ihsan</p>
                    </div>
                  </div>
                  <p className="mt-4 text-gray-600">
                    Penulis adalah pengajar berpengalaman di PTQ Al Ihsan dengan fokus pada pendidikan tahfidz dan
                    pembentukan karakter Islami.
                  </p>
                </CardContent>
              </Card>

              {/* Related Posts */}
              <Card>
                <CardHeader>
                  <CardTitle>Artikel Terkait</CardTitle>
                </CardHeader>
                <CardContent>
                  {relatedPosts.length === 0 ? (
                    <p className="text-gray-500 text-sm">Tidak ada artikel terkait</p>
                  ) : (
                    <ul className="space-y-4">
                      {relatedPosts.map((relatedPost) => (
                        <li key={relatedPost.id} className="flex gap-3">
                          <div className="relative w-16 h-16 flex-shrink-0">
                            <Image
                              src={relatedPost.featured_image || "/placeholder.svg"}
                              alt={relatedPost.title}
                              fill
                              className="object-cover rounded-md"
                            />
                          </div>
                          <div>
                            <Link
                              href={`/blog/${relatedPost.slug}`}
                              className="font-medium hover:text-emerald-600 transition-colors line-clamp-2"
                            >
                              {relatedPost.title}
                            </Link>
                            <p className="text-xs text-gray-500 mt-1">
                              {formatDate(relatedPost.published_at || relatedPost.created_at)}
                            </p>
                          </div>
                        </li>
                      ))}
                    </ul>
                  )}
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}

