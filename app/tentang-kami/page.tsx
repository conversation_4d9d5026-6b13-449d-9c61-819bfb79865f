"use client"

import type React from "react"
import { useState, useEffect } from "react"
import Head from "next/head"

import Image from "next/image"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { ArrowRight, CheckCircle2, Loader2 } from "lucide-react"
import { useWebsiteSettings, useFeatureFlags, useFacilitiesSettings } from "@/hooks/use-website-settings"

interface Teacher {
  id: string
  name: string
  specialization: string | null
  photo_url: string | null
  join_date: string | null
  status: string
}

export default function AboutPage() {
  // Load dynamic data from settings
  const { getContent, getImageUrl, loading } = useWebsiteSettings()
  const featureFlags = useFeatureFlags()
  const { facilities, loading: facilitiesLoading } = useFacilitiesSettings()

  // State for teachers data
  const [teachers, setTeachers] = useState<Teacher[]>([])
  const [teachersLoading, setTeachersLoading] = useState(true)

  // Load teachers data
  useEffect(() => {
    const loadTeachers = async () => {
      try {
        setTeachersLoading(true)
        const response = await fetch('/api/teachers')
        if (response.ok) {
          const data = await response.json()
          setTeachers(data.teachers || [])
        }
      } catch (error) {
        console.error('Error loading teachers:', error)
      } finally {
        setTeachersLoading(false)
      }
    }

    loadTeachers()
  }, [])

  // Show loading state while data is being fetched
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-emerald-600 mx-auto" />
          <p className="mt-2 text-gray-600">Memuat halaman tentang kami...</p>
        </div>
      </div>
    )
  }

  // Get about content
  const aboutContent = getContent('about')
  const visionContent = aboutContent.find(item => item.key === 'vision')
  const missionContent = aboutContent.find(item => item.key === 'mission')
  const historyContent = aboutContent.find(item => item.key === 'history')

  return (
    <>
      <Head>
        <title>Tentang Kami - PTQ Al Ihsan</title>
        <meta name="description" content="Mengenal lebih dekat PTQ Al Ihsan Ungaran - Pondok Tahfidz Quran dengan visi mencetak generasi Qur'ani yang berakhlak mulia, cerdas, dan bermanfaat bagi umat." />
        <meta name="keywords" content="tentang ptq al ihsan, sejarah pondok tahfidz, visi misi pesantren, profil lembaga pendidikan islam, pondok tahfidz ungaran" />
        <meta property="og:title" content="Tentang Kami - PTQ Al Ihsan" />
        <meta property="og:description" content="Mengenal lebih dekat PTQ Al Ihsan Ungaran - Pondok Tahfidz Quran dengan visi mencetak generasi Qur'ani yang berakhlak mulia." />
        <meta property="og:type" content="website" />
        <link rel="canonical" href="/tentang-kami" />
      </Head>
      <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-emerald-700 to-emerald-800 text-white py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto text-center">
            <h1 className="text-4xl font-bold mb-6">Tentang PTQ Al Ihsan</h1>
            <p className="text-xl text-emerald-100">
              Pondok Tahfidzul Qur'an yang berfokus pada pembentukan generasi Qur'ani dengan pemahaman Islam yang benar.
            </p>
          </div>
        </div>
      </section>

      {/* Vision & Mission */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="bg-white rounded-xl shadow-md overflow-hidden">
              <div className="p-8">
                <h2 className="text-3xl font-bold text-emerald-700 mb-6">Visi</h2>
                <p className="text-xl text-gray-700 mb-8">
                  {visionContent?.content ||
                    "Melahirkan generasi unggul yang mencintai Al Qur'an, berakhlak mulia, dan mampu bersaing secara akademis."
                  }
                </p>

                <h2 className="text-3xl font-bold text-emerald-700 mb-6">Misi</h2>
                <div className="text-gray-700 whitespace-pre-line">
                  {missionContent?.content ? (
                    <div className="space-y-4">
                      {missionContent.content.split('\n').map((mission, index) => (
                        mission.trim() && (
                          <MissionItem key={index}>{mission.trim()}</MissionItem>
                        )
                      ))}
                    </div>
                  ) : (
                    <ul className="space-y-4">
                      <MissionItem>Membentuk santri yang hafal minimal 7 Juz Al Qur'an.</MissionItem>
                      <MissionItem>
                        Mengajarkan dasar-dasar Aqidah, Hadits, dan Fiqih dengan pemahaman yang kuat.
                      </MissionItem>
                      <MissionItem>Mengembangkan kemampuan membaca kitab kuning tanpa harakat (kitab gundul).</MissionItem>
                      <MissionItem>Meningkatkan kemampuan public speaking dalam dakwah dan kehidupan sosial.</MissionItem>
                    </ul>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* History & Background */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row gap-12 items-center">
            <div className="md:w-1/2">
              <h2 className="text-3xl font-bold text-gray-800 mb-6">Sejarah PTQ Al Ihsan</h2>
              <div className="text-gray-600 whitespace-pre-line">
                {historyContent?.content || (
                  <>
                    <p className="mb-4">
                      PTQ Al Ihsan didirikan pada 2019 dengan visi mewujudkan lembaga pendidikan Islam yang mendalam dan
                      berkualitas. Berawal dari semangat untuk membentuk generasi yang mencintai Al-Qur'an dan memiliki ilmu
                      yang bermanfaat, PTQ Al Ihsan tumbuh semakin baik.
                    </p>
                    <p className="mb-4">
                      Kami meyakini pentingnya mendidik santri dalam nilai agama yang kuat sambil mempersiapkan mereka untuk
                      tantangan masa depan.
                    </p>
                    <p className="mb-4">
                      Dengan perjalanan yang penuh dedikasi selama 6 tahun ini, PTQ Al Ihsan kini memiliki lokasi sendiri yang
                      asri dan nyaman, serta program unggulan yang mencakup Pendalaman Agama, tahfidzul Qur'an, dan
                      pengembangan diri.
                    </p>
                    <p>
                      Semua ini diarahkan untuk mewujudkan generasi muslim yang berakhlak mulia, berilmu, dan mampu
                      berkontribusi positif di tengah masyarakat. Kami terus berinovasi untuk memastikan pendidikan kami
                      berkualitas dan bermakna.
                    </p>
                  </>
                )}
              </div>
            </div>
            <div className="md:w-1/2 relative">
              <div className="relative h-[400px] w-full rounded-lg overflow-hidden shadow-lg">
                <Image
                  src={getImageUrl('about_hero', '/placeholder.svg?height=400&width=600')}
                  alt="Sejarah PTQ Al Ihsan"
                  fill
                  className="object-cover"
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Facilities */}
      <section className="py-16 bg-emerald-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-800 mb-4">Fasilitas Kami</h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              PTQ Al Ihsan menyediakan fasilitas yang mendukung proses belajar dan kenyamanan santri.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {facilitiesLoading ? (
              // Loading skeleton
              Array.from({ length: 6 }).map((_, index) => (
                <div key={index} className="bg-white rounded-lg shadow-md overflow-hidden animate-pulse">
                  <div className="h-48 bg-gray-300"></div>
                  <div className="p-6">
                    <div className="h-6 bg-gray-300 rounded mb-2"></div>
                    <div className="h-4 bg-gray-300 rounded"></div>
                  </div>
                </div>
              ))
            ) : facilities.length > 0 ? (
              facilities.map((facility) => (
                <FacilityCard
                  key={facility.key}
                  title={facility.title}
                  description={facility.content}
                  image={facility.imageUrl}
                />
              ))
            ) : (
              <div className="col-span-full text-center py-8">
                <p className="text-gray-500">Belum ada data fasilitas yang tersedia.</p>
              </div>
            )}
          </div>
        </div>
      </section>

      {/* Team/Teachers */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-800 mb-4">Pengajar Kami</h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Tim pengajar yang berpengalaman dan berkompeten di bidangnya.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {teachersLoading ? (
              // Loading skeleton
              Array.from({ length: 6 }).map((_, index) => (
                <div key={index} className="bg-white rounded-lg shadow-md overflow-hidden animate-pulse">
                  <div className="h-64 bg-gray-300"></div>
                  <div className="p-6">
                    <div className="h-6 bg-gray-300 rounded mb-2"></div>
                    <div className="h-4 bg-gray-300 rounded mb-2"></div>
                    <div className="h-4 bg-gray-300 rounded w-3/4"></div>
                  </div>
                </div>
              ))
            ) : teachers.length > 0 ? (
              teachers.map((teacher) => (
                <TeacherCard
                  key={teacher.id}
                  name={teacher.name}
                  role={teacher.specialization || "Pengajar"}
                  subjects={teacher.specialization || ""}
                  image={teacher.photo_url || "/placeholder.svg?height=300&width=300"}
                />
              ))
            ) : (
              <div className="col-span-full text-center py-8">
                <p className="text-gray-500">Belum ada data pengajar yang tersedia.</p>
              </div>
            )}
          </div>
        </div>
      </section>

      {/* CTA */}
      <section className="py-16 bg-gradient-to-r from-emerald-700 to-emerald-800 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-6">Bergabunglah dengan PTQ Al Ihsan</h2>
          <p className="text-xl text-emerald-100 mb-8 max-w-2xl mx-auto">
            Berikan pendidikan terbaik untuk putra-putri Anda di PTQ Al Ihsan.
          </p>
          {featureFlags.ppdbMenu && (
            <Button asChild size="lg" className="bg-white text-emerald-800 hover:bg-emerald-100">
              <Link href="/ppdb" className="flex items-center gap-2">
                Daftar Sekarang
                <ArrowRight className="h-4 w-4" />
              </Link>
            </Button>
          )}
        </div>
      </section>
      </div>
    </>
  )
}

function MissionItem({ children }: { children: React.ReactNode }) {
  return (
    <li className="flex items-start gap-3">
      <CheckCircle2 className="h-6 w-6 text-emerald-600 mt-0.5 flex-shrink-0" />
      <span className="text-gray-700">{children}</span>
    </li>
  )
}

function FacilityCard({ title, description, image }: { title: string; description: string; image: string }) {
  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
      <div className="relative h-48">
        <Image src={image || "/placeholder.svg"} alt={title} fill className="object-cover" />
      </div>
      <div className="p-6">
        <h3 className="text-xl font-bold text-gray-800 mb-2">{title}</h3>
        <p className="text-gray-600">{description}</p>
      </div>
    </div>
  )
}

function TeacherCard({
  name,
  role,
  subjects,
  image,
}: { name: string; role: string; subjects?: string; image: string }) {
  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow text-center">
      <div className="relative h-64 w-full">
        <Image src={image || "/placeholder.svg"} alt={name} fill className="object-cover" />
      </div>
      <div className="p-6">
        <h3 className="text-xl font-bold text-gray-800 mb-1">{name}</h3>
        <p className="text-emerald-600 font-medium">{role}</p>
        {subjects && <p className="text-gray-600 mt-2 text-sm">{subjects}</p>}
      </div>
    </div>
  )
}
