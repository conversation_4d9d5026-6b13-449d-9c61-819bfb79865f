// Simple test to check if blog data exists in database
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

const supabase = createClient(supabaseUrl, supabaseKey)

async function testBlogData() {
  console.log('Testing blog data...')
  
  // Check blog posts
  const { data: posts, error: postsError } = await supabase
    .from('blog_posts')
    .select('*')
  
  console.log('Blog posts:', posts?.length || 0)
  if (postsError) console.error('Posts error:', postsError)
  
  // Check blog categories
  const { data: categories, error: categoriesError } = await supabase
    .from('blog_categories')
    .select('*')
  
  console.log('Blog categories:', categories?.length || 0)
  if (categoriesError) console.error('Categories error:', categoriesError)
  
  // Check post-category relationships
  const { data: relations, error: relationsError } = await supabase
    .from('post_categories')
    .select('*')
  
  console.log('Post-category relations:', relations?.length || 0)
  if (relationsError) console.error('Relations error:', relationsError)
  
  // Check profiles (authors)
  const { data: profiles, error: profilesError } = await supabase
    .from('profiles')
    .select('*')
  
  console.log('Profiles:', profiles?.length || 0)
  if (profilesError) console.error('Profiles error:', profilesError)
}

testBlogData()
